import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { TextLoader } from "langchain/document_loaders/fs/text";
import { DocxLoader } from "@langchain/community/document_loaders/fs/docx";
import { PDFLoader } from "@langchain/community/document_loaders/fs/pdf";
import * as XLSX from 'xlsx';

interface DocumentChunk {
  pageContent: string;
  metadata: Record<string, any>;
}

// Clean array function to handle undefined values in arrays
function cleanArray(arr: any[]): any[] {
  return arr.map(item => {
    if (Array.isArray(item)) {
      return cleanArray(item);
    }
    return item ?? ''; // Replace undefined/null with empty string
  });
}

// Clean object function to handle nested objects and arrays
function cleanObject(obj: any): any {
  if (Array.isArray(obj)) {
    return cleanArray(obj);
  }
  
  if (obj && typeof obj === 'object') {
    const cleaned: Record<string, any> = {};
    for (const [key, value] of Object.entries(obj)) {
      if (value === undefined || value === null) {
        cleaned[key] = '';
      } else if (Array.isArray(value)) {
        cleaned[key] = cleanArray(value);
      } else if (typeof value === 'object') {
        cleaned[key] = cleanObject(value);
      } else {
        cleaned[key] = value;
      }
    }
    return cleaned;
  }
  
  return obj ?? '';
}

// Process CSV files using CSVLoader
async function processCSV(file: File): Promise<DocumentChunk[]> {
  try {
    const text = await file.text();
    // Split into lines and remove empty lines
    const lines = text.split(/\r?\n/).filter(line => line.trim());
    
    if (lines.length === 0) {
      throw new Error('CSV file is empty');
    }

    // Find the first line that doesn't start with # or other common comment indicators
    let headerIndex = 0;
    while (headerIndex < lines.length && 
           (lines[headerIndex].startsWith('#') || 
            lines[headerIndex].startsWith('//') || 
            lines[headerIndex].startsWith(';'))) {
      headerIndex++;
    }

    if (headerIndex >= lines.length) {
      throw new Error('No valid data found in CSV file');
    }

    // Parse and clean headers
    const headers = parseCSVLine(lines[headerIndex])
      .map(header => String(header || '').trim())
      .map((header, index) => header || `Column${index + 1}`);

    // Get data rows (everything after headers)
    const rows = lines.slice(headerIndex + 1)
      .filter(line => !line.startsWith('#')) // Skip any additional comments
      .map(line => parseCSVLine(line));

    // Create chunks
    const chunks: DocumentChunk[] = [];
    const ROWS_PER_CHUNK = 50;

    // Store comments if any exist
    const comments = lines.slice(0, headerIndex).join('\n');

    // Add summary chunk
    const summaryContent = `CSV Summary:\n` +
      (comments ? `File Comments:\n${comments}\n\n` : '') +
      `Total Rows: ${rows.length}\n` +
      `Columns: ${headers.join(', ')}\n\n` +
      `Sample Data (First 5 Rows):\n` +
      rows.slice(0, 5).map((row, idx) => 
        `Row ${idx + 1}:\n` + 
        headers.map((header, i) => `  ${header}: ${String(row[i] || '')}`).join('\n')
      ).join('\n\n');

    chunks.push({
      pageContent: summaryContent,
      metadata: {
        type: 'csv_summary',
        totalRows: rows.length,
        headers: headers,
        hasComments: comments.length > 0,
        is_summary: true
      }
    });

    // Process remaining rows in chunks
    for (let i = 0; i < rows.length; i += ROWS_PER_CHUNK) {
      const chunkRows = rows.slice(i, Math.min(i + ROWS_PER_CHUNK, rows.length));
      let chunkContent = '';

      chunkRows.forEach((row, rowIdx) => {
        chunkContent += `Row ${i + rowIdx + 1}:\n`;
        headers.forEach((header, colIdx) => {
          chunkContent += `${header}: ${String(row[colIdx] || '')}\n`;
        });
        chunkContent += '\n';
      });

      chunks.push({
        pageContent: chunkContent.trim(),
        metadata: {
          type: 'csv_chunk',
          rowRange: {
            start: i + 1,
            end: Math.min(i + ROWS_PER_CHUNK, rows.length)
          },
          totalRows: rows.length,
          headers: headers
        }
      });
    }

    return chunks;
  } catch (error) {
    console.error('CSV processing error:', error);
    throw error;
  }
}

// Improved CSV line parser that handles quotes and special characters
function parseCSVLine(line: string): string[] {
  const fields: string[] = [];
  let field = '';
  let inQuotes = false;
  let i = 0;
  
  // Skip initial whitespace
  while (i < line.length && line[i].trim() === '') i++;
  
  while (i < line.length) {
    const char = line[i];
    const nextChar = line[i + 1];
    
    if (char === '"') {
      if (!inQuotes && field.trim() === '') {
        // Start of quoted field
        inQuotes = true;
      } else if (inQuotes && nextChar === '"') {
        // Escaped quote inside quoted field
        field += '"';
        i++;
      } else if (inQuotes) {
        // End of quoted field
        inQuotes = false;
      } else {
        // Quote in middle of unquoted field
        field += char;
      }
    } else if (char === ',' && !inQuotes) {
      // End of field
      fields.push(field.trim());
      field = '';
    } else {
      field += char;
    }
    
    i++;
  }
  
  // Add the last field
  fields.push(field.trim());
  
  return fields;
}

// Process Excel files
async function processExcel(blob: Blob): Promise<DocumentChunk[]> {
  try {
    const arrayBuffer = await blob.arrayBuffer();
    const workbook = XLSX.read(arrayBuffer, { type: 'array' });
    
    let allSheetContent: DocumentChunk[] = [];
    
    for (const sheetName of workbook.SheetNames) {
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1,
        defval: '' 
      }) as any[][];
      
      if (jsonData.length > 0) {
        const headers = jsonData[0]
          .map(header => String(header || '').trim())
          .map((header, index) => header || `Column${index + 1}`);

        const content = jsonData.slice(1);
        
        // Create summary for this sheet
        const sheetSummary = {
          pageContent: `Sheet: ${sheetName}\nColumns: ${headers.join(', ')}\nTotal Rows: ${content.length}`,
          metadata: cleanObject({
            sheet: sheetName,
            type: 'excel_summary',
            headers: headers,
            rowCount: content.length,
            is_summary: true
          })
        };
        allSheetContent.push(sheetSummary);

        // Process rows in chunks
        const ROWS_PER_CHUNK = 50;
        for (let i = 0; i < content.length; i += ROWS_PER_CHUNK) {
          const chunkRows = content.slice(i, i + ROWS_PER_CHUNK);
          let chunkContent = `Sheet: ${sheetName}\n\n`;
          
          const rowData = chunkRows.map((row, rowIndex) => {
            const rowObj: Record<string, string> = {};
            headers.forEach((header, colIndex) => {
              rowObj[header] = String(row[colIndex] || '').trim();
            });
            return rowObj;
          });

          chunkContent += rowData.map((row, idx) => 
            `Row ${i + idx + 1}:\n` + 
            Object.entries(row)
              .map(([header, value]) => `${header}: ${value}`)
              .join('\n')
          ).join('\n\n');

          allSheetContent.push({
            pageContent: chunkContent.trim(),
            metadata: cleanObject({
              sheet: sheetName,
              type: 'excel_chunk',
              rowRange: {
                start: i + 1,
                end: Math.min(i + ROWS_PER_CHUNK, content.length)
              },
              headers: headers,
              totalRows: content.length
            })
          });
        }
      }
    }
    
    return allSheetContent;
  } catch (error) {
    console.error('Excel processing error:', error);
    throw error;
  }
}

// Process text files using TextLoader
async function processText(file: File): Promise<DocumentChunk[]> {
  try {
    const blob = new Blob([await file.arrayBuffer()], { type: 'text/plain' });
    const loader = new TextLoader(blob);
    const docs = await loader.load();
    
    if (docs.length === 0) {
      throw new Error('No content found in text file');
    }

    const splitter = new RecursiveCharacterTextSplitter({
      chunkSize: 1000,
      chunkOverlap: 200,
      separators: ["\n\n", "\n", ". ", " ", ""]
    });

    const chunks = await splitter.splitDocuments(docs);
    
    return chunks.map(chunk => ({
      pageContent: chunk.pageContent,
      metadata: cleanObject({
        ...chunk.metadata,
        type: 'text_chunk'
      })
    }));
  } catch (error) {
    console.error('Text processing error:', error);
    throw error;
  }
}

// Main document processing function
export async function processDocument(
file: File, docId: string, fileType: string, fileName: string, userId: string, category: string, additionalParameter: string, CHUNK_SIZE: number, CHUNK_OVERLAP: number): Promise<DocumentChunk[]> {
  try {
    let chunks: DocumentChunk[] = [];
    
    switch (fileType.toLowerCase()) {
      case "text/csv":
        chunks = await processCSV(file);
        break;

      case "application/vnd.ms-excel":
      case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        const excelBlob = new Blob([await file.arrayBuffer()], { type: file.type });
        chunks = await processExcel(excelBlob);
        break;

      case "text/plain":
      case "application/rtf":
        chunks = await processText(file);
        break;

      case "application/msword":
      case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        const docxBlob = new Blob([await file.arrayBuffer()], { type: file.type });
        const docxLoader = new DocxLoader(docxBlob);
        const docContent = await docxLoader.load();
        const splitter = new RecursiveCharacterTextSplitter({
          chunkSize: 1000,
          chunkOverlap: 200,
          separators: ["\n\n", "\n", ". ", " ", ""]
        });
        chunks = await splitter.splitDocuments(docContent);
        break;

      case "application/pdf":
        const pdfBlob = new Blob([await file.arrayBuffer()], { type: file.type });
        const pdfLoader = new PDFLoader(pdfBlob);
        const pdfContent = await pdfLoader.load();
        chunks = await new RecursiveCharacterTextSplitter({
          chunkSize: 1000,
          chunkOverlap: 200,
          separators: ["\n\n", "\n", ". ", " ", ""]
        }).splitDocuments(pdfContent);
        break;

      default:
        throw new Error(`Unsupported file type: ${fileType}`);
    }

    if (!chunks.length) {
      throw new Error('No content was extracted from the document');
    }

    // Add common metadata to all chunks
    return chunks.map((chunk, index) => ({
      pageContent: chunk.pageContent,
      metadata: cleanObject({
        ...chunk.metadata,
        doc_id: docId,
        chunk_id: `${docId}_${index + 1}`,
        file_name: fileName,
        file_type: fileType,
        position: index + 1,
        total_chunks: chunks.length,
        is_summary: chunk.metadata.is_summary || index === 0,
        processed_at: new Date().toISOString()
      })
    }));

  } catch (error) {
    console.error('Document processing error:', error);
    throw error;
  }
}