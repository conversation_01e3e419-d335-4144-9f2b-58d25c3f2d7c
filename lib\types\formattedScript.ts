import { Timestamp } from 'firebase/firestore';

export interface FormattedScriptDocument {
  id: string;
  originalFileId: string; // Reference to the original file in "files" collection
  originalNamespace: string; // Namespace from the original "files" collection
  formattedMarkdown: string; // The processed markdown content
  metadata: {
    title: string;
    author: string;
    characters: string[];
    summary: string;
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
  userId: string; // For user-specific access control
  formattingStatus: 'pending' | 'processing' | 'completed' | 'failed';
  formattingError?: string; // Store error message if formatting fails
}

export interface FormattingProgress {
  stage: 'uploading' | 'processing' | 'formatting' | 'storing' | 'completed' | 'error';
  progress: number; // 0-100
  message: string;
}
