import React, { useCallback } from 'react';
import <PERSON>act<PERSON><PERSON>, {
  React<PERSON>lowProvider,
  addEdge,
  Node,
  Edge,
  Controls,
  Background,
  MiniMap
} from 'react-flow-renderer';

export interface FlowDiagramNode {
  id: string;
  data: { label: string };
  position: { x: number; y: number };
}

export interface FlowDiagramEdge {
  id: string;
  source: string;
  target: string;
}

export interface FlowDiagramData {
  nodes: FlowDiagramNode[];
  edges: FlowDiagramEdge[];
}

interface FlowDiagramProps {
  data: FlowDiagramData;
}

/**
 * FlowDiagram Component
 * 
 * This component takes in `data` with `nodes` and `edges` and renders a 
 * flow diagram using react-flow-renderer. It displays nodes and edges,
 * along with basic controls (zoom, fit view, etc.).
 */
const FlowDiagram: React.FC<FlowDiagramProps> = ({ data }) => {
  const { nodes, edges } = data;

  // Convert our simplified node and edge format to what react-flow expects.
  const formattedNodes: Node[] = nodes.map((node) => ({
    id: node.id,
    data: { label: node.data.label },
    position: node.position
  }));

  const formattedEdges: Edge[] = edges.map((edge) => ({
    id: edge.id,
    source: edge.source,
    target: edge.target
  }));

  // If you need to handle interactions like drag and drop, onConnect, etc.,
  // you can define callbacks here. For now, we’ll just render the diagram.

  // onConnect callback if you want to make edges interactive:
  const onConnect = useCallback((params: any) => {
    // This would update edges if we had state and wanted to add new edges on user interaction.
    // For a static diagram, you can ignore this.
    // Example: setEdges((eds) => addEdge(params, eds));
  }, []);

  return (
    <div style={{ width: '100%', height: '400px' }}>
      <ReactFlowProvider>
        <ReactFlow
          nodes={formattedNodes}
          edges={formattedEdges}
          onConnect={onConnect}
          fitView
          style={{ background: '#f0f0f0', border: '1px solid #ccc' }}
        >
          <Controls />
          <MiniMap />
          <Background />
        </ReactFlow>
      </ReactFlowProvider>
    </div>
  );
};

export { FlowDiagram };
