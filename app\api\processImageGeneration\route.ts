import { NextRequest, NextResponse } from 'next/server';
import { adminDb, adminStorage } from 'components/firebase-admin';
import { GenerateImageAgent } from 'components/Agents/GenerateImageAgent';
import { GenerateImageTool } from 'components/tools/generateImageTool';
import { v4 as uuidv4 } from 'uuid';

interface JobData {
  prompt: string;
  originalPrompt?: string;
  refinedPrompt?: string;
  id: string;
  userId: string;
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  let jobData: JobData | undefined;
  try {
    // 1) Parse and validate request
    const { jobId, userId } = await req.json();
    if (!jobId) {
      throw new Error('No jobId provided in request.');
    }
    if (!userId) {
      throw new Error('No userId provided in request.');
    }
    console.log(`Processing job: ${jobId}`);

    // 2) Validate API key
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OpenAI API key not configured in environment variables.');
    }

    // 3) Initialize image generation services
    const imageTool = new GenerateImageTool(apiKey);
    const imageAgent = new GenerateImageAgent({ generateImageTool: imageTool });

    // Get the job document using direct reference
    let jobDoc;
    try {
      const jobRef = adminDb.collection('users').doc(userId).collection('images').doc(jobId);
      jobDoc = await jobRef.get();
      console.log(`Firestore query completed for job: ${jobId}`);

      if (!jobDoc.exists) {
        throw new Error(`Job ${jobId} not found for user ${userId}`);
      }

      jobData = jobDoc.data() as JobData;
      if (!jobData || !jobData.prompt) {
        throw new Error(`No prompt found in job ${jobId}`);
      }
      console.log(`Job data retrieved for user: ${userId}`);
    } catch (error) {
      console.error('Firestore query error:', error);
      throw new Error(`Failed to fetch job: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 4) Generate the image and handle response
    let imageResponse;
    try {
      imageResponse = await imageAgent.generateImage({ prompt: jobData.prompt });
      console.log(`Image generated for job: ${jobId}`);
    } catch (error) {
      console.error('Image generation error:', error);
      throw new Error(`Failed to generate image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    
    if (!imageResponse.base64Image) {
      throw new Error(imageResponse.error || 'Failed to generate image');
    }

    // 5) Process the base64 string
    const base64Image = imageResponse.base64Image;
    
    // 6) Handle potential data URI prefix
    const base64Data = base64Image.replace(/^data:image\/\w+;base64,/, '');
    
    // 7) Convert to buffer with validation
    let imageBuffer: Buffer;
    try {
      imageBuffer = Buffer.from(base64Data, 'base64');
      if (imageBuffer.length === 0) {
        throw new Error('Generated image buffer is empty');
      }
      console.log(`Image buffer created for job: ${jobId}`);
    } catch (error) {
      throw new Error(`Failed to process image data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 8) Upload to Firebase Storage using user-specific path
    const bucket = adminStorage.bucket();
    const filePath = `users/${userId}/generated/${jobId}.png`;
    const file = bucket.file(filePath);

    try {
      await file.save(imageBuffer, {
        metadata: {
          contentType: 'image/png',
          metadata: {
            jobId,
            userId,
            generatedAt: new Date().toISOString()
          }
        }
      });
      console.log(`Image uploaded to Firebase Storage for job: ${jobId}`);
    } catch (error) {
      console.error('Firebase Storage upload error:', error);
      throw new Error(`Failed to upload image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 9) Generate signed URL
    let downloadUrl;
    try {
      [downloadUrl] = await file.getSignedUrl({
        action: 'read',
        expires: '03-01-2500'
      });
      console.log(`Signed URL generated for job: ${jobId}`);
    } catch (error) {
      console.error('Signed URL generation error:', error);
      throw new Error(`Failed to generate signed URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 10) Update Firestore job status in user-specific collection
    const jobRef = adminDb.collection('users').doc(userId).collection('images').doc(jobId);
    try {
      await jobRef.update({
        status: 'completed',
        imageUrl: downloadUrl,
        updatedAt: new Date(),
        processedAt: new Date()
      });
      console.log(`Firestore job status updated for job: ${jobId}`);
    } catch (error) {
      console.error('Firestore update error:', error);
      throw new Error(`Failed to update job status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

// 11) Create record in files collection with namespace
const namespace = uuidv4();
try {
  // Use the namespace as the document ID instead of auto-generating one
  const fileRef = adminDb.collection('users').doc(userId).collection('files').doc(namespace);
  await fileRef.set({
    category: 'My Images',
    createdAt: new Date(),
    downloadUrl: downloadUrl,
    isImage: true,
    name: jobData.prompt,
    namespace: namespace,  // This will now match the document ID
    ref: `uploads/${userId}/generated/${jobId}.png`,
    size: imageBuffer.length,
    type: 'image/png',
    jobId: jobId,
    description: jobData.prompt
  });
  console.log(`Files collection record created for job: ${jobId} with namespace: ${namespace}`);
} catch (error) {
  console.error('Files collection update error:', error);
  throw new Error(`Failed to create files record: ${error instanceof Error ? error.message : 'Unknown error'}`);
}

    // 12) Return success response with namespace
    return NextResponse.json({
      success: true,
      imageUrl: downloadUrl,
      namespace: namespace,
      jobId
    });

  } catch (error) {
    console.error('Image generation process error:', error);

    // Attempt to update job status on failure
    try {
      if (jobData?.userId && jobData?.id) {
        const jobRef = adminDb.collection('users').doc(jobData.userId).collection('images').doc(jobData.id);
        await jobRef.update({
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error',
          updatedAt: new Date(),
          failedAt: new Date()
        });
        console.log(`Job status updated to failed for job: ${jobData.id}`);
      }
    } catch (updateError) {
      console.error('Failed to update job status:', updateError);
    }

    // Return error response
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to process image',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}