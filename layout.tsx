// app/layout.tsx
import "styles/globals.css";
import { ReactNode } from "react";
import { SelectedDocProvider } from "components/SelectedDocContext";
import { SessionProvider } from "next-auth/react";
import { ThemeProvider } from "contexts/ThemeContext";

export const metadata = {
  title: 'CastMate-ai',
  description: 'Developed by Indefhubs',
};

export default function RootLayout({
  children,
}: {
  children: ReactNode,
}) {
  return (
    <html lang="en">
      <body className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-950 text-gray-900 dark:text-white transition-colors duration-300">
        <SessionProvider>
          <ThemeProvider>
            <SelectedDocProvider>
              <div className="flex flex-row w-full min-h-screen">
                <div className="h-screen">
                  {/* <SideBar /> */}
                </div>
                <div className="flex-grow">
                  <div className="max-w-full">{children}</div>
                </div>
              </div>
            </SelectedDocProvider>
          </ThemeProvider>
        </SessionProvider>
      </body>
    </html>
  );
}