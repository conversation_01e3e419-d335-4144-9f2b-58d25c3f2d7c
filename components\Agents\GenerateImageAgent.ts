// components/Agents/GenerateImageAgent.ts
import { GenerateImageTool } from '../tools/generateImageTool';
import { Timestamp } from 'firebase-admin/firestore';
import { adminDb } from 'components/firebase-admin';

export interface GenerateImageParams {
  prompt: string;
}

export interface GenerateImageResponse {
  success: boolean;
  base64Image?: string;
  error?: string;
}

export interface ImageJob {
  id: string;
  prompt: string;
  userId: string;
  status: 'initialized' | 'refining' | 'generating' | 'completed' | 'failed';
  originalPrompt: string;
  refinedPrompt?: string;
  error?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  imageUrl?: string;
}

export class GenerateImageAgent {
  private readonly generateImageTool: GenerateImageTool;

  constructor(config: { generateImageTool: GenerateImageTool }) {
    this.generateImageTool = config.generateImageTool;
  }

  private getUserImagesCollection(userId: string) {
    return adminDb.collection('users').doc(userId).collection('images');
  }

  public async initializeJob(prompt: string, userId: string): Promise<string> {
    if (!prompt?.trim()) {
      throw new Error('Invalid prompt provided');
    }
    if (!userId?.trim()) {
      throw new Error('Invalid userId provided');
    }

    try {
      const jobRef = this.getUserImagesCollection(userId).doc();
      const now = Timestamp.now();

      const job: ImageJob = {
        id: jobRef.id,
        prompt: prompt.trim(),
        userId,
        status: 'initialized',
        originalPrompt: prompt.trim(),
        createdAt: now,
        updatedAt: now
      };

      await jobRef.set(job);
      return jobRef.id;

    } catch (error) {
      console.error('[GenerateImageAgent] Job initialization failed:', error);
      throw new Error(
        error instanceof Error 
          ? `Failed to initialize job: ${error.message}`
          : 'Failed to initialize image generation job'
      );
    }
  }

  public async refinePrompt(jobId: string, userId: string): Promise<void> {
    if (!jobId?.trim()) {
      throw new Error('Invalid jobId provided');
    }
    if (!userId?.trim()) {
      throw new Error('Invalid userId provided');
    }

    try {
      const jobRef = this.getUserImagesCollection(userId).doc(jobId);
      const jobDoc = await jobRef.get();

      if (!jobDoc.exists) {
        throw new Error(`Job ${jobId} not found for user ${userId}`);
      }

      const job = jobDoc.data() as ImageJob;

      await jobRef.update({
        status: 'refining',
        updatedAt: Timestamp.now()
      });

      const refinedPrompt = await this.generateImageTool.refinePrompt(job.originalPrompt);

      await jobRef.update({
        refinedPrompt,
        status: 'generating',
        updatedAt: Timestamp.now()
      });

    } catch (error) {
      console.error('[GenerateImageAgent] Prompt refinement failed:', error);
      
      try {
        const jobRef = this.getUserImagesCollection(userId).doc(jobId);
        await jobRef.update({
          status: 'failed',
          error: error instanceof Error ? error.message : 'Failed to refine prompt',
          updatedAt: Timestamp.now()
        });
      } catch (updateError) {
        console.error('[GenerateImageAgent] Failed to update job status:', updateError);
      }

      throw error;
    }
  }

  public async generateImage(params: GenerateImageParams): Promise<GenerateImageResponse> {
    if (!params.prompt?.trim()) {
      return {
        success: false,
        error: 'Invalid prompt provided'
      };
    }

    try {
      const result = await this.generateImageTool.generateImageBase64(
        params.prompt.trim(),
        'dall-e-3',
        '1024x1024'
      );

      return {
        success: result.success,
        base64Image: result.base64Image,
        error: result.error
      };

    } catch (error) {
      console.error('[GenerateImageAgent] Image generation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate image'
      };
    }
  }
}