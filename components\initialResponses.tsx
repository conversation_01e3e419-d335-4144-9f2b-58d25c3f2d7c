import React from 'react';
import Image from 'next/image';
import { File } from 'lucide-react';

interface InitialResponsesProps {
  initialResponses: { type: string; message: string }[];
  onPromptClick: (prompt: string) => void;
  showInitialResponses: boolean;
  documentName: string;
  firstName?: string;
  isFirstMessage?: boolean;
  resetWelcome?: boolean; // Add this line
}

export default function InitialResponses({
  initialResponses,
  onPromptClick,
  showInitialResponses,
  documentName,
  isFirstMessage = false,
  firstName,
  resetWelcome = false,
}: InitialResponsesProps) {
  if (!showInitialResponses || resetWelcome) return null; // Add this line

  const defaultMessage = "Here are initial questions to get you started";

  const formatDocumentName = (name: string): string => {
    if (!name || name.trim() === '' || name === "Unknown Document") return '';
    return name.split(/\s+/).filter(Boolean).join(' ');
  };

  const displayMessage = documentName && documentName.trim() !== '' && documentName !== "Unknown Document"
    ? initialResponses[0]?.message.replace('{documentName}', formatDocumentName(documentName))
    : defaultMessage;

  if (isFirstMessage) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center justify-center w-full max-w-4xl px-4">
          <div className="flex items-center justify-center gap-6 mb-8 mt-5">
            <div className="relative">
              <div 
                className="absolute inset-0 rounded-full animate-glow"
                style={{
                  filter: 'blur(20px)',
                  transform: 'scale(1.2)',
                  zIndex: 0
                }}
              />
              <div className="relative animate-pulse-slow">
                <Image
                  src="/Welcome-favicon2.png"
                  alt="Company Logo"
                  width={60}
                  height={100}
                  className="relative z-10"
                />
              </div>
            </div>
            
            <div 
              className="text-[60px] mt-10 text-blue-200 font-semibold tracking-wider"
              style={{
                animation: 'fadeIn 0.5s ease-in-out',
                textShadow: '0 0 20px rgba(250, 204, 21, 0.2)'
              }}
            >
              Welcome, {firstName || ''}
            </div>
          </div>
          
          {documentName && (
            <div className="w-full max-w-xl text-center mb-8">
              <div className="text-amber-500 text-sm">
                iKe will help you find out all you need to know about, {documentName.slice(0, -4)}
              </div>
              <div className="flex justify-center items-center space-x-8 w-full">
  {initialResponses
    .filter(prompt => !prompt.message.startsWith('You are now chatting about:'))
    .slice(0, 3) // Limit to first 3 items
    .map((prompt, index) => (
      <button
        key={index}
        onClick={() => onPromptClick(prompt.message)}
        className="w-55 h-45 mt-10 flex items-center shadow-md shadow-ike-dark-purple  justify-center text-sm bg-[#2d1d3a] hover:bg-[#3d2d4a] text-blue-200 rounded-lg p-2 text-left transition-colors duration-200"
      >
        <File className="w-10 h-10 mr-2 mb-16" />
      {prompt.message}
          </button>
        ))}
      </div>            
            </div>
          )}
          
          {/* Using proper conditional rendering syntax */}
          {!isFirstMessage && (
            <div className="w-full max-w-4xl">
              <div className="flex flex-wrap justify-center gap-3">
                {initialResponses.map((prompt, index) => (
                  <button
                    key={index}
                    onClick={() => onPromptClick(prompt.message)}
                    className="text-sm bg-[#2d1d3a] hover:bg-[#3d2d4a] text-amber-500 px-6 py-3 rounded-full transition-colors duration-200"
                  >
                    {prompt.message}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Alternative style for non-first messages
  return (
    <div className="initial-responses max-w-full overflow-hidden">
      <div className="text-center text-xs text-gray-400 mb-4">
        {displayMessage}
      </div>
      <div className="flex flex-wrap justify-center gap-2 max-w-full">
        {initialResponses.slice(1).map((prompt, index) => (
          <button
            key={index}
            onClick={() => onPromptClick(prompt.message)}
            className="text-xs bg-ike-purple hover:bg-ike-dark-purple text-amber-500 px-3 py-2 rounded-full transition-colors duration-200"
          >
            {prompt.message}
          </button>
        ))}
      </div>
    </div>
  );
}

// Add the animation styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = `
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes glow {
      0% {
        background-color: rgba(34, 197, 94, 0.5); /* green */
      }
      33% {
        background-color: rgba(239, 68, 68, 0.5); /* red */
      }
      66% {
        background-color: rgba(234, 179, 8, 0.5); /* yellow */
      }
      100% {
        background-color: rgba(34, 197, 94, 0.5); /* back to green */
      }
    }

    @keyframes pulse-slow {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.05);
      }
      100% {
        transform: scale(1);
      }
    }

    .animate-glow {
      animation: glow 6s infinite;
    }

    .animate-pulse-slow {
      animation: pulse-slow 2s infinite;
    }
  `;
  document.head.appendChild(styleSheet);
}

