import { NextRequest, NextResponse } from "next/server";
import { testRagIndexingEndpoints } from "../../../components/scriptreaderAI/elevenlabs";

/**
 * Test API route to verify RAG indexing endpoints
 *
 * Usage: POST /api/test-rag-indexing
 * Body: { "knowledgeBaseId": "kb_id", "documentId": "doc_id" }
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { knowledgeBaseId, documentId } = body;

    if (!knowledgeBaseId || !documentId) {
      return NextResponse.json(
        { error: "Both knowledgeBaseId and documentId are required in request body" },
        { status: 400 }
      );
    }

    console.log(`[TEST-RAG] Testing RAG indexing endpoints for document: ${documentId} in KB: ${knowledgeBaseId}`);

    const testResults = await testRagIndexingEndpoints(knowledgeBaseId, documentId);

    return NextResponse.json({
      success: true,
      knowledgeBaseId: knowledgeBaseId,
      documentId: documentId,
      testResults: testResults,
      message: "RAG indexing endpoint test completed. Check console logs for detailed results."
    });

  } catch (error) {
    console.error("[TEST-RAG] Error testing RAG indexing endpoints:", error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: "RAG indexing endpoint test failed. Check console logs for details."
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to provide usage instructions
 */
export async function GET() {
  return NextResponse.json({
    message: "RAG Indexing Test Endpoint",
    usage: {
      method: "POST",
      endpoint: "/api/test-rag-indexing",
      body: {
        knowledgeBaseId: "your_knowledge_base_id_here",
        documentId: "your_document_id_here"
      },
      description: "Tests the ElevenLabs RAG indexing API endpoints with an existing knowledge base and document ID"
    },
    example: {
      curl: `curl -X POST http://localhost:3000/api/test-rag-indexing \\
  -H "Content-Type: application/json" \\
  -d '{"knowledgeBaseId": "kb_123", "documentId": "uvYgVFPMBWa8I53O0xY0"}'`
    }
  });
}
