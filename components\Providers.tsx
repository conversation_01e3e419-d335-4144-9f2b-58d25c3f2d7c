"use client";

import React from 'react';
import SideBar from 'components/SideBar';

interface ChatLayoutProps {
  children: React.ReactNode;
}

const ChatLayout: React.FC<ChatLayoutProps> = ({ children }) => {
  return (
    <div className="flex h-screen w-screen overflow-hidden">
      <div className="w-64 flex-shrink-0 bg-gray-900 overflow-y-auto">
        <SideBar />
      </div>
      <main className="flex-1 overflow-hidden bg-ike-message-bg">
        {children}
      </main>
    </div>
  );
};

export default ChatLayout;