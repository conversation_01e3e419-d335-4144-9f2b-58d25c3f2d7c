// lib/schemas/anthropometricSchema.ts

import { z } from "zod";

/**
 * Function to extract a number from a string or any value.
 * If the value cannot be converted to a number, returns 0.
 * @param value - The value to extract a number from.
 * @returns The extracted number or 0 if conversion fails.
 */
function extractNumber(value: any): number {
  if (typeof value === 'number') {
    return value; // Already a number
  }

  if (typeof value === 'string') {
    // Use a regular expression to extract digits and decimal points
    const numberMatch = value.match(/[\d\.]+/);
    if (numberMatch) {
      const parsedNumber = parseFloat(numberMatch[0]);
      return isNaN(parsedNumber) ? 0 : parsedNumber;
    }
  }

  return 0; // Return 0 if not a number or cannot be parsed
}

/**
 * Function to apply default values to parsed anthropometric data.
 * Ensures that all required fields are present and correctly typed.
 * @param anthropometricData - The data to be processed.
 * @returns Processed anthropometric data with default values applied.
 */
export function processAnthropometricData(anthropometricData: any) {
  return {
    clientName: anthropometricData.clientName || 'Unknown Client',
    age: extractNumber(anthropometricData.age),
    height: anthropometricData.height || 'N/A',
    weight: anthropometricData.weight || 'N/A',
    bmi: extractNumber(anthropometricData.bmi),
    bodyFatPercentage: extractNumber(anthropometricData.bodyFatPercentage),
    muscleMass: extractNumber(anthropometricData.muscleMass),
    waistCircumference: anthropometricData.waistCircumference || 'N/A',
    hipCircumference: anthropometricData.hipCircumference || 'N/A',
    waistToHipRatio: anthropometricData.waistToHipRatio || 'N/A',
    bmr: extractNumber(anthropometricData.bmr),
    activityLevel: anthropometricData.activityLevel || 'N/A',
    recommendedCalories: anthropometricData.recommendedCalories || 'N/A',
    hydrationNeeds: anthropometricData.hydrationNeeds || 'N/A',
    specialConsiderations: anthropometricData.specialConsiderations || 'N/A',
    otherDetails: anthropometricData.otherDetails || 'N/A',
    // Initialize embedding fields as empty arrays; they'll be populated during ingestion
    waistCircumferenceEmbedding: anthropometricData.waistCircumferenceEmbedding || [],
    hipCircumferenceEmbedding: anthropometricData.hipCircumferenceEmbedding || [],
    waistToHipRatioEmbedding: anthropometricData.waistToHipRatioEmbedding || [],
  };
}

/**
 * Zod schema for validating processed anthropometric data.
 */
export const anthropometric_filters = z.object({
  clientName: z.string().describe("Name of the client"),
  age: z.number().describe("Age of the client"),
  height: z.string().describe("Height of the client"),
  weight: z.string().describe("Weight of the client"),
  bmi: z.number().describe("BMI of the client"),
  bodyFatPercentage: z.number().describe("Body fat percentage"),
  muscleMass: z.number().describe("Muscle mass percentage"),
  waistCircumference: z.string().optional().describe("Waist circumference"),
  hipCircumference: z.string().optional().describe("Hip circumference"),
  waistToHipRatio: z.string().optional().describe("Waist to Hip ratio"),
  bmr: z.number().optional().describe("Basal metabolic rate"),
  activityLevel: z.string().optional().describe("Physical activity level"),
  recommendedCalories: z.string().optional().describe("Recommended daily calories"),
  hydrationNeeds: z.string().optional().describe("Daily water intake requirement"),
  specialConsiderations: z.string().optional().describe("Any special considerations"),
  otherDetails: z.string().optional().describe("Additional details"),
  // Include embeddings if needed
  waistCircumferenceEmbedding: z.array(z.number()).optional(),
  hipCircumferenceEmbedding: z.array(z.number()).optional(),
  waistToHipRatioEmbedding: z.array(z.number()).optional(),
});
