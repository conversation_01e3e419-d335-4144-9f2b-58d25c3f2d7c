// app/providers.tsx
'use client';

import { ReactNode } from "react";
import { SessionProvider } from "next-auth/react";
import { SelectedDocProvider } from "components/SelectedDocContext";
import { AnalyticsProvider } from "lib/analytics/analyticsProvider";
import { ThemeProvider } from "contexts/ThemeContext";

export function Providers({ children }: { children: ReactNode }) {
  return (
    <SessionProvider>
      <ThemeProvider>
        <SelectedDocProvider>
          <AnalyticsProvider>
            {children}
          </AnalyticsProvider>
        </SelectedDocProvider>
      </ThemeProvider>
    </SessionProvider>
  );
}