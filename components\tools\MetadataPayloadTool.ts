import { Tool } from "@langchain/core/tools";
import { TokenManagement } from "@/src/tokenTracker/tokenManagement";

interface Source {
  title: string;
  page: number;
  [key: string]: any;
}

interface MetadataPayloadInput {
  content: string;
  metadata: {
    sources: Source[];
    totalTokens?: number;
    chunkCount?: number;
    averageRelevance?: number;
    namespaceDistribution?: Record<string, number>;
    [key: string]: any;
  };
  tokenManager: TokenManagement;
  maxPreviewLength?: number;
}

interface MetadataPayloadOutput {
  success: boolean;
  payload: string;
  error?: string;
  details?: {
    pageContent: string;
    pageNumber: string;
    pageTitle: string;
    tokenUsage: any;
  };
}

/**
 * MetadataPayloadTool
 * Generates formatted metadata payloads for client consumption
 */
export class MetadataPayloadTool extends Tool {
  name = "metadataPayload";
  description = "Generate metadata payload for client";

  private readonly DEFAULT_MAX_PREVIEW_LENGTH = 800;

  async _call(input: MetadataPayloadInput): Promise<MetadataPayloadOutput> {
    try {
      // Validate input
      this.validateInput(input);

      const maxPreviewLength = input.maxPreviewLength || this.DEFAULT_MAX_PREVIEW_LENGTH;

      // Generate the metadata payload
      const details = {
        pageContent: this.generateContentPreview(input.content, maxPreviewLength),
        pageNumber: this.formatSourcePages(input.metadata.sources),
        pageTitle: this.formatSourceTitles(input.metadata.sources),
        tokenUsage: input.tokenManager.getMetadataPayload({
          ...input.metadata,
          chunkCount: input.metadata.chunkCount ?? 0, // Ensure chunkCount is a number
          averageRelevance: input.metadata.averageRelevance ?? 0, // Ensure averageRelevance is a number
          // Add similar handling for other optional properties if needed
        })
      };

      // Create the final payload
      const payload = JSON.stringify(details, null, 2);

      return {
        success: true,
        payload,
        details
      };

    } catch (error) {
      console.error("MetadataPayloadTool: Error generating metadata payload:", error);
      return {
        success: false,
        payload: "",
        error: `Failed to generate metadata payload: ${(error as Error).message}`
      };
    }
  }

  private validateInput(input: MetadataPayloadInput): void {
    if (!input.content || typeof input.content !== 'string') {
      throw new Error("Invalid content provided");
    }

    if (!input.metadata || !Array.isArray(input.metadata.sources)) {
      throw new Error("Invalid metadata structure");
    }

    if (!input.tokenManager) {
      throw new Error("TokenManager is required");
    }

    if (input.maxPreviewLength && (
      typeof input.maxPreviewLength !== 'number' || 
      input.maxPreviewLength <= 0
    )) {
      throw new Error("Invalid maxPreviewLength");
    }
  }

  private generateContentPreview(content: string, maxLength: number): string {
    if (!content) return "";
    return content.length > maxLength 
      ? content.substring(0, maxLength) + "..."
      : content;
  }

  private formatSourcePages(sources: Source[]): string {
    return sources
      .map(s => `${s.title} (Page ${s.page})`)
      .join(", ");
  }

  private formatSourceTitles(sources: Source[]): string {
    return [...new Set(sources.map(s => s.title))].join("\n");
  }
}