import React from 'react';

interface PdfDocumentAreaProps {
  pageContent: string;
  pageNumber: string | null;
  docTitle: string | null;
  onPageContentUpdate: React.Dispatch<React.SetStateAction<string>>;
  onPageNumberUpdate: React.Dispatch<React.SetStateAction<string | null>>;  
}

const PdfDocumentArea: React.FC<PdfDocumentAreaProps> = ({ pageContent, pageNumber, docTitle }) => {
  return (
    <div className="w-full bg-ike-dark-purple p-4 flex flex-col h-full">
      {/* Container for the textarea */}
      <div className="flex-grow rounded-md bg-ike-dark-purple p-1">
        <textarea
          className="w-full pl-2  pt-3 h-full text-xs text-indigo-500 resize-none border-none bg-gray-300 rounded-md"
          placeholder=""
          value={`The following documents were extracted for the search:\n\n${pageContent}`}
          readOnly
        />
      </div>
      {/* Container for the title, page number, and black space */}
      <div className="h-[15%] bg-ike-message-ai bg-opacity-75 rounded-md text-white mt-2 flex flex-col items-center justify-center">
        {docTitle && (
          <p className="text-xs p1 pl-5">{docTitle}</p>
        )}
          {pageNumber && pageNumber !== 'Unknown Pa' && (
            <p className="text-xs mt-1 text-left">
              Extracts taken from page/s
              <span>{pageNumber}</span>
            </p>
          )}
      </div>
    </div>
  );
};

export default PdfDocumentArea;