import React from 'react';
import { Folder, ChevronDown, ChevronRight, FilesIcon, FoldersIcon } from 'lucide-react';
import { Timestamp } from 'firebase/firestore';
import { FileData } from './FileManagerstructure';
import FileList from './FileList';
import { SortConfig } from './FileManagerDirectory';

interface FolderListProps {
  groupedFiles: {
    category: string;
    files: FileData[];
    isExpanded: boolean;
  }[];
  toggleCategory: (category: string) => void;
  handleFileClick: (file: FileData) => void;
  formatTimestamp: (timestamp: Timestamp | undefined) => string;
  truncateFileName: (fileName: string, maxLength?: number) => string;
  sortConfig: SortConfig | null;
}

const FolderList: React.FC<FolderListProps> = ({
  groupedFiles,
  toggleCategory,
  handleFileClick,
  formatTimestamp,
  truncateFileName,
  sortConfig,
}) => {
  const sortedFolders = [...groupedFiles].sort((a, b) => a.category.localeCompare(b.category));

  return (
    <div className="divide-y divide-gray-700 mt-5">
    <div className="text-gray-400 text-left ml-2 font-semibold mb-1  shadow-md p-2 w-max rounded-xl flex items-center">
      <FoldersIcon className="h-5 w-5 mr-1" />
      My list of folders
    </div>
      {sortedFolders.map(({ category, files, isExpanded }) => (
        <div key={category}>
          <div className="grid grid-cols-6 gap-2 p-1 text-xs text-left ml-2 mr-2 rounded-xl bg-ike-dark-purple text-white items-center mb-1 hover:bg-ike-purple_b">
            <div className="col-span-2 flex items-center">
              <button
                onClick={() => toggleCategory(category)}
                className="focus:outline-none mr-2"
                aria-label={isExpanded ? `Collapse ${category}` : `Expand ${category}`}
              >
                {isExpanded ? (
                  <ChevronDown className="h-5 w-5 text-amber-500" />
                ) : (
                  <ChevronRight className="h-5 w-5 text-amber-500" />
                )}
              </button>
              <Folder className="inline mr-2 text-amber-500" />
              {category}
            </div>
            <div className="flex items-center justify-center w-full">
              {files.length}
            </div>
            <div className="flex items-center justify-center w-full">
              {files.reduce((sum, file) => sum + file.chatCount, 0)}
            </div>
            <div></div>
            <div></div>
          </div>
          {isExpanded && (
            <div className="pl-4">
              <FileList
                files={files}
                handleFileClick={handleFileClick}
                truncateFileName={truncateFileName}
                formatTimestamp={formatTimestamp}
                sortConfig={sortConfig}
                variant="categorized"
              />
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default FolderList;