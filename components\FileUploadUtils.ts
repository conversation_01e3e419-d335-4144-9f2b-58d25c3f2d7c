import React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { useDropzone, DropzoneOptions } from 'react-dropzone';
import { useRouter } from 'next/navigation';
import {
  <PERSON><PERSON>he<PERSON>,
  CircleArrowDown,
  Hammer,
  Rocket,
  Save,
  ThumbsUp
} from 'lucide-react';
import { 
  FileIcon,
  FileTextIcon,
  ImageIcon,
  FileSpreadsheetIcon,
  FileTypeIcon,
  FileArchiveIcon
} from 'lucide-react';

export enum StatusText {
  UPLOADING = 'UPLOADING',
  GENERATING = 'GENERATING',
  UPLOADED = 'UPLOADED',
  SAVING = 'SAVING',
  IDLE = 'IDLE',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  ERROR = 'ERROR'
}

export const getFileIcon = (fileType: string): JSX.Element => {
  const defaultProps = { className: "h-16 w-16 text-indigo-600" };

  switch (fileType) {
    // Document types
    case 'application/pdf':
      return React.createElement(FileTextIcon, defaultProps);
    
    case 'application/msword':
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      return React.createElement(FileTypeIcon, defaultProps);
    
    case 'text/plain':
    case 'application/rtf':
      return React.createElement(FileTextIcon, defaultProps);

    // Spreadsheet types
    case 'application/vnd.ms-excel':
    case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
    case 'text/csv':
      return React.createElement(FileSpreadsheetIcon, defaultProps);

    // Image types
    case 'image/jpeg':
    case 'image/png':
    case 'image/gif':
    case 'image/webp':
      return React.createElement(ImageIcon, defaultProps);

    // Archive types (in case you add support for them later)
    case 'application/zip':
    case 'application/x-rar-compressed':
      return React.createElement(FileArchiveIcon, defaultProps);

    // Default file icon for unrecognized types
    default:
      return React.createElement(FileIcon, defaultProps);
  }
}

export const SUPPORTED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'] as const;
export const SUPPORTED_DOCUMENT_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
  'application/rtf',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/csv'
] as const;

export type SupportedImageType = typeof SUPPORTED_IMAGE_TYPES[number];
export type SupportedDocumentType = typeof SUPPORTED_DOCUMENT_TYPES[number];
export type SupportedFileType = SupportedImageType | SupportedDocumentType;

export const MAX_IMAGE_SIZE = 4 * 1024 * 1024; // 4MB
export const MAX_DOC_SIZE = 50 * 1024 * 1024; // 50MB

export const ACCEPTED_FILE_TYPES: Record<string, string[]> = {
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'text/plain': ['.txt'],
  'application/rtf': ['.rtf'],
  'application/vnd.ms-excel': ['.xls'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  'text/csv': ['.csv'],
  'image/*': ['.jpg', '.jpeg', '.png', '.gif', '.webp']
};

type StatusIconsType = {
  [K in StatusText]: React.ReactElement;
};

export const STATUS_ICONS: StatusIconsType = {
  [StatusText.UPLOADING]: React.createElement(Rocket, { 
    className: "text-indigo-600",
    size: 20 
  }),
  [StatusText.GENERATING]: React.createElement(Hammer, { 
    className: "text-indigo-600 animate-bounce",
    size: 20 
  }),
  [StatusText.UPLOADED]: React.createElement(CheckCheck, { 
    className: "text-indigo-600",
    size: 20 
  }),
  [StatusText.SAVING]: React.createElement(Save, { 
    className: "text-indigo-600",
    size: 20 
  }),
  [StatusText.IDLE]: React.createElement(CircleArrowDown, { 
    className: "animate-bounce",
    size: 20 
  }),
  [StatusText.PROCESSING]: React.createElement(Hammer, { 
    className: "text-indigo-600",
    size: 20 
  }),
  [StatusText.COMPLETED]: React.createElement(CheckCheck, { 
    className: "text-indigo-600",
    size: 20 
  }),
  [StatusText.ERROR]: React.createElement(ThumbsUp, { 
    className: "text-red-600",
    size: 20 
  })
};

interface UseFileUploadProps {
  handleUpload: (file: File, category: string) => Promise<void>;
  selectedCategory: string | null;
  newCategory: string;
}

interface UseFileUploadReturn {
  fileName: string;
  selectedFile: File | null;
  preview: string | null;
  error: string | null;
  clearSelection: () => void;
  getRootProps: ReturnType<typeof useDropzone>['getRootProps'];
  getInputProps: ReturnType<typeof useDropzone>['getInputProps'];
  isDragActive: boolean;
}

export const useFileUpload = ({ 
  handleUpload, 
  selectedCategory, 
  newCategory 
}: UseFileUploadProps): UseFileUploadReturn => {
  const [fileName, setFileName] = useState<string>("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const validateFile = (file: File): boolean => {
    const isImage = SUPPORTED_IMAGE_TYPES.includes(file.type as SupportedImageType);
    const isDocument = SUPPORTED_DOCUMENT_TYPES.includes(file.type as SupportedDocumentType);
    
    if (!isImage && !isDocument) {
      setError('Unsupported file type. Please check the list of supported formats below.');
      return false;
    }

    const maxSize = isImage ? MAX_IMAGE_SIZE : MAX_DOC_SIZE;
    if (file.size > maxSize) {
      setError(`File size exceeds the ${isImage ? '4MB' : '50MB'} limit.`);
      return false;
    }

    return true;
  };

  const clearSelection = useCallback(() => {
    if (preview) {
      URL.revokeObjectURL(preview);
    }
    setSelectedFile(null);
    setPreview(null);
    setError(null);
    setFileName("");
  }, [preview]);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      setError(null);
      const file = acceptedFiles[0];
      
      if (!validateFile(file)) {
        return;
      }

      setFileName(file.name);
      setSelectedFile(file);

      // Create preview for images
      if (SUPPORTED_IMAGE_TYPES.includes(file.type as SupportedImageType)) {
        const objectUrl = URL.createObjectURL(file);
        setPreview(objectUrl);
      } else {
        setPreview(null);
      }

      const category = newCategory.trim() !== "" ? newCategory : selectedCategory || "";
      if (file) {
        await handleUpload(file, category);
      }
    },
    [handleUpload, selectedCategory, newCategory]
  );

  const dropzoneOptions: DropzoneOptions = {
    onDrop,
    maxFiles: 1,
    accept: ACCEPTED_FILE_TYPES,
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone(dropzoneOptions);

  return {
    fileName,
    selectedFile,
    preview,
    error,
    clearSelection,
    getRootProps,
    getInputProps,
    isDragActive,
  };
};

export const useFileIdRedirect = (fileId?: string): void => {
  const router = useRouter();

  useEffect(() => {
    if (fileId) {
      router.push(`/fileManager`);
    }
  }, [fileId, router]);
};

export const isUploadInProgress = (progress: number | null): boolean => {
  return progress != null && progress >= 0 && progress <= 100;
};