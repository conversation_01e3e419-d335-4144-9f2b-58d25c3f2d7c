'use client';

import { createContext, useContext } from 'react';
import { AnalyticsMiddleware } from './AnalyticsMiddleware';

export interface AnalyticsContextType {
  analyticsMiddleware: AnalyticsMiddleware | null;
  isInitialized: boolean;
}

export const AnalyticsContext = createContext<AnalyticsContextType>({
  analyticsMiddleware: null,
  isInitialized: false
});

export const useAnalytics = () => useContext(AnalyticsContext);