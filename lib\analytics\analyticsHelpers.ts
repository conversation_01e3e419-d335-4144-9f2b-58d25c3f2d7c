// app/lib/firebase/analyticsHelpers.ts
import { collection, query, where, orderBy, limit, getDocs } from 'firebase/firestore';
import { db, ANALYTICS_COLLECTIONS, retryOperation } from 'components/firebase';

export async function getQueryMetrics(userId: string, maxResults: number = 10) {
  return retryOperation(async () => {
    const queryRef = collection(db, ANALYTICS_COLLECTIONS.QUERY_METRICS);
    const q = query(
      queryRef,
      where('userId', '==', userId),
      orderBy('timestamp', 'desc'),
      limit(maxResults) // Use the renamed parameter here
    );
    
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  });
}

export async function getChunkMetricsForQuery(queryId: string) {
  return retryOperation(async () => {
    const chunkRef = collection(db, ANALYTICS_COLLECTIONS.CHUNK_METRICS);
    const q = query(
      chunkRef,
      where('queryId', '==', queryId),
      orderBy('relevanceScore', 'desc')
    );
    
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  });
}

export async function getTokenMetricsForQuery(queryId: string) {
  return retryOperation(async () => {
    const tokenRef = collection(db, ANALYTICS_COLLECTIONS.TOKEN_METRICS);
    const q = query(
      tokenRef,
      where('queryId', '==', queryId),
      orderBy('timestamp', 'asc')
    );
    
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  });
}

export async function getContentMetricsForQuery(queryId: string) {
  return retryOperation(async () => {
    const contentRef = collection(db, ANALYTICS_COLLECTIONS.CONTENT_METRICS);
    const q = query(
      contentRef,
      where('queryId', '==', queryId),
      orderBy('timestamp', 'desc'),
      limit(1)
    );
    
    const snapshot = await getDocs(q);
    return snapshot.docs[0]?.data();
  });
}