import React from 'react';
import { X } from 'lucide-react';
import VisualizationRenderer from './VisualizationRenderer';

interface ChartData {
  url: string;
  description: string;
  id: string;
  type: string;
}

interface ChartModalProps {
  chart: ChartData;
  onClose: () => void;
}

const dashboardModal: React.FC<ChartModalProps> = ({ chart, onClose }) => {
  const [chartData, setChartData] = React.useState<any>(null);

  React.useEffect(() => {
    const fetchChartData = async () => {
      try {
        const response = await fetch(chart.url);
        if (!response.ok) {
          throw new Error('Failed to fetch chart data');
        }
        const data = await response.json();
        setChartData(data);
      } catch (error) {
        console.error('Error fetching chart data:', error);
      }
    };

    fetchChartData();
  }, [chart.url]);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-gray-800">{chart.description}</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={24} />
          </button>
        </div>
        <div className="w-full h-[60vh]">
          {chartData ? (
            <VisualizationRenderer data={chartData} />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <p className="text-gray-500">Loading chart...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default dashboardModal;

