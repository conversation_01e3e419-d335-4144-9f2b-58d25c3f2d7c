// src/types/token-config.ts

/**
 * Default token limits and thresholds for the application
 */
export const TOKEN_LIMITS = {
    DEFAULT_MAX_TOKENS: 6500,
    DEFAULT_WARNING_THRESHOLD: 0.90,
    SYSTEM_PROMPT_TOKENS: 300,
    MAX_INPUT_TOKENS: 2800
  } as const;
  
  /**
   * Interface for token usage tracking
   */
  export interface TokenUsage {
    contextTokens: number;
    systemPromptTokens: number;
    chatHistoryTokens: number;
    totalTokens: number;
  }
  
  export interface TokenLimitStatus {
    isApproachingLimit: boolean;
    hasExceededLimit: boolean;
    currentUsagePercent: number;
    availableTokens: number;
  }



  /**
   * Props interface for TokenLimitAlert component
   */
  export interface TokenLimitAlertProps {
    tokenUsage: TokenUsage;
    maxTokens?: number;
    warningThreshold?: number;
    onNewChatClick?: () => void;
  }
  
  /**
   * Configuration for token management
   */
  export interface TokenConfig {
    maxInputTokens: number;
    systemPromptTokens: number;
    chatHistoryTokens: number;
  }
  
  /**
   * Interface for processed metadata
   */
  export interface ProcessedMetadata {
    totalTokens: number;
    chunkCount: number;
    averageRelevance: number;
    namespaceDistribution: Record<string, number>;
  }