'use client'

/**
 * @fileoverview ResultsGrid Component
 * 
 * Purpose:
 * This component serves as a container for displaying search results in a responsive grid layout.
 * It handles both the loading state with skeletons and the presentation of actual search results
 * using ResultCard components.
 * 
 * Features:
 * - Responsive grid layout (3 columns on large screens)
 * - Loading state handling with skeleton placeholders
 * - Dynamic rendering of ResultCard components
 * - Efficient list rendering with proper key usage
 */

import React from 'react';
import { SearchResult } from './ChatVectorTypes';
import ResultCard from './ResultCard';
import { ResultCardSkeleton } from './ResultCardSkeleton';

/**
 * Interface defining the props for the ResultsGrid component
 * @property {SearchResult[]} results - Array of search results to display
 * @property {boolean} [isLoading] - Optional flag to indicate loading state
 */
interface ResultsGridProps {
  results: SearchResult[];
  isLoading?: boolean;
}

/**
 * ResultsGrid component displays a grid of search results or loading skeletons
 * 
 * @param {ResultsGridProps} props - Component props
 * @param {SearchResult[]} props.results - Array of search results to display
 * @param {boolean} [props.isLoading] - Optional loading state flag
 * 
 * @returns {React.ReactElement} A grid of either ResultCard components or skeleton loaders
 */
const ResultsGrid: React.FC<ResultsGridProps> = ({ results, isLoading }) => {
  // Handle loading state by displaying skeleton placeholders
  if (isLoading) {
    return (
      <div className="grid lg:grid-cols-3 gap-3">
        {/* Create an array of 3 skeleton placeholders */}
        {[...Array(3)].map((_, index) => (
          <ResultCardSkeleton key={index} />
        ))}
      </div>
    );
  }

  // Render the actual results grid when data is available
  return (
    <div className="grid lg:grid-cols-3 gap-3">
      {/* Map through results array and render a ResultCard for each item */}
      {results.map((result) => (
        <ResultCard 
          key={result.id} // Use unique ID for optimal React rendering
          result={result} 
        />
      ))}
    </div>
  );
};

export default ResultsGrid;