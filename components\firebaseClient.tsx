import {
  addDoc,
  collection,
  serverTimestamp,
  query,
  orderBy,
  onSnapshot,
  getDocs
  
} from 'firebase/firestore';
import { db } from './firebase';

export interface ChatData {
  id: string;
  text: string;
  userId: string;
  role?: 'user' | 'ai';
  createdAt: {
    seconds: number;
    nanoseconds: number;
  };
  fileDocumentId?: string | null;
}

// Example usage in an async function
// Function to fetch the fileDocumentId for a given chatId
export const getFileDocumentIdForChat = async (
  userEmail: string,
  chatId: string
): Promise<string | null> => {
  try {
    const messagesQuery = query(
      collection(db, 'users', userEmail, 'chats', chatId, 'messages'),
      orderBy('createdAt', 'asc')
    );

    const querySnapshot = await getDocs(messagesQuery);
    for (const docSnapshot of querySnapshot.docs) {
      const data = docSnapshot.data();
      if (data.fileDocumentId) {
        return data.fileDocumentId as string;
      }
    }

    return null;
  } catch (error) {
    console.error('Error retrieving fileDocumentId for chat:', error);
    return null;
  }
};

// Send a new message to Firestore
export const sendMessageToFirestore = async (
  userEmail: string,
  chatId: string,
  messageText: string,
  fileDocumentId: string | null
) => {
  await addDoc(collection(db, 'users', userEmail, 'chats', chatId, 'messages'), {
    text: messageText,
    userId: userEmail,
    createdAt: serverTimestamp(),
    fileDocumentId: fileDocumentId,
  });
};

// Get chat history from Firestore
export const getChatHistory = async (
  userEmail: string,
  chatId: string
): Promise<ChatData[]> => {
  const chatHistoryQuery = query(
    collection(db, 'users', userEmail, 'chats', chatId, 'messages'),
    orderBy('createdAt', 'asc')
  );
  const chatHistorySnapshot = await getDocs(chatHistoryQuery);
  return chatHistorySnapshot.docs.map((doc) => doc.data() as ChatData);
};

// Listen for changes in the chat messages
export const subscribeToMessages = (
  userEmail: string,
  chatId: string,
  onMessagesUpdate: (messagesData: ChatData[]) => void,
  onError: (error: Error) => void
) => {
  const messagesQuery = query(
    collection(db, 'users', userEmail, 'chats', chatId, 'messages'),
    orderBy('createdAt', 'asc')
  );

  const unsubscribe = onSnapshot(
    messagesQuery,
    (snapshot) => {
      const messagesData = snapshot.docs.map((doc) => {
        const data = doc.data() as ChatData;
        return {
          id: doc.id,
          userId: data.userId,
          text: data.text,
          createdAt: data.createdAt,
          fileDocumentId: data.fileDocumentId || null,
        };
      });
      onMessagesUpdate(messagesData);
    },
    (error) => {
      onError(error);
    }
  );

  return unsubscribe;
};
