import { NextRequest, NextResponse } from "next/server";
import { doc, getDoc } from "firebase/firestore";
import { db } from "../../../../components/firebase";
import { getServerSession } from "next-auth/next";
import { authOptions } from "../../auth/[...nextauth]/authOptions";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse> {
  try {
    // Validate NextAuth session
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: "Unauthorized - No valid session" },
        { status: 401 }
      );
    }

    const { id } = params;
    const userId = session.user.email;

    if (!id) {
      return NextResponse.json(
        { success: false, error: "Script ID is required" },
        { status: 400 }
      );
    }

    // Get formatted script from Firestore
    const formattedScriptRef = doc(db, "users", userId, "formatted_scripts", id);
    const formattedScriptSnap = await getDoc(formattedScriptRef);

    if (!formattedScriptSnap.exists()) {
      return NextResponse.json(
        { success: false, error: "Formatted script not found" },
        { status: 404 }
      );
    }

    const formattedScriptData = formattedScriptSnap.data();

    // Check if formatting failed
    if (formattedScriptData.formattingStatus === 'failed') {
      return NextResponse.json({
        success: false,
        error: "Script formatting failed",
        formattingError: formattedScriptData.formattingError,
        requiresReformatting: true
      }, { status: 422 });
    }

    // Check if formatting is still in progress
    if (formattedScriptData.formattingStatus === 'processing') {
      return NextResponse.json({
        success: false,
        error: "Script formatting is still in progress",
        status: 'processing'
      }, { status: 202 });
    }

    return NextResponse.json({
      success: true,
      data: {
        id: formattedScriptData.id,
        formattedMarkdown: formattedScriptData.formattedMarkdown,
        metadata: formattedScriptData.metadata,
        formattingStatus: formattedScriptData.formattingStatus,
        createdAt: formattedScriptData.createdAt,
        updatedAt: formattedScriptData.updatedAt
      }
    });

  } catch (error) {
    console.error("Error fetching formatted script:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : "Unknown error occurred" 
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS(req: NextRequest): Promise<NextResponse> {
  return NextResponse.json({}, { status: 200 });
}
