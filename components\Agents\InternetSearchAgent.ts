// components/Agents/InternetSearchAgent.ts
import { InternetSearchTool } from "../tools/InternetSearchTool";
import { TokenManagement } from "@/src/tokenTracker/tokenManagement";

interface InternetSearchAgentConfig {
  maxResults?: number;
  tokenManager?: TokenManagement;
}

/**
 * InternetSearchAgent
 * 
 * An agent specialized in managing internet searches and processing search results.
 * This agent utilizes the InternetSearchTool to perform searches and handles the
 * processing and formatting of search results.
 */
export class InternetSearchAgent {
  private searchTool: InternetSearchTool;
  static description = `InternetSearchAgent handles internet search operations when:
  - Real-time information is needed
  - Information might be more current than in the knowledge base
  - External verification or fact-checking is required
  - User explicitly requests internet search
  
  The agent ensures proper:
  - Result filtering and ranking
  - Token usage tracking
  - Error handling and recovery
  - Search result formatting`;

  constructor(config: InternetSearchAgentConfig) {
    this.searchTool = new InternetSearchTool({
      maxResults: config.maxResults,
      tokenManager: config.tokenManager
    });
  }

  async search(query: string) {
    try {
      console.log("InternetSearchAgent: Starting search process");
      
      const result = await this.searchTool.call(query);
      
      if (typeof result === 'string') {
        // Parse string result if returned in string format
        return JSON.parse(result);
      }
      
      return result;

    } catch (error) {
      console.error("InternetSearchAgent: Search failed", error);
      return {
        success: false,
        results: [],
        metadata: {
          source: 'brave_search',
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        }
      };
    }
  }

  // Method to get agent's description
  getDescription(): string {
    return InternetSearchAgent.description;
  }
}