import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from 'components/firebase-admin';
import { LLMDataProcessingTool } from 'components/tools/LLMDataProcessingTool';

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const body = await req.json();
    const { prompt, userId } = body;

    if (!prompt || typeof prompt !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Invalid prompt provided' },
        { status: 400 }
      );
    }
    if (!userId || typeof userId !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Invalid userId provided' },
        { status: 400 }
      );
    }

    const apiKey = process.env.GROQ_API_KEY;
    if (!apiKey) {
      console.error('Groq API key not configured');
      return NextResponse.json(
        { success: false, error: 'Service configuration error' },
        { status: 500 }
      );
    }

    const llmProcessor = new LLMDataProcessingTool(apiKey, {
      maxRetries: 3,
      temperature: 0.3
    });

    const jobRef = adminDb.collection('users').doc(userId).collection('charts').doc();
    const now = new Date();

    await jobRef.set({
      id: jobRef.id,
      prompt: prompt.trim(),
      userId,
      status: 'initialized',
      createdAt: now,
      updatedAt: now
    });

    // Process the prompt to generate initial chart data
    const processedData = await llmProcessor.call({
      content: prompt,
      options: {
        includeTimeAnalysis: true,
        includeSuggestions: true
      }
    });

    // Update the job with processed data
    await jobRef.update({
      processedData,
      status: 'processing',
      updatedAt: new Date()
    });

    return NextResponse.json({
      success: true,
      jobId: jobRef.id,
      message: 'Chart generation job initialized successfully'
    });

  } catch (error) {
    console.error('[Route] Chart generation initialization failed:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to initialize chart generation'
      },
      { status: 500 }
    );
  }
}

