// lib/schemas/Recipes_filters.ts

import { z } from 'zod';

/**
 * Function to extract a number from a string or any value.
 * If the value cannot be converted to a number, returns 0.
 * @param value - The value to extract a number from.
 * @returns The extracted number or 0 if conversion fails.
 */
function extractNumber(value: any): number {
  if (typeof value === 'number') {
    return value; // Already a number
  }

  if (typeof value === 'string') {
    // Use a regular expression to extract digits and decimal points
    const numberMatch = value.match(/[\d\.]+/);
    if (numberMatch) {
      const parsedNumber = parseFloat(numberMatch[0]);
      return isNaN(parsedNumber) ? 0 : parsedNumber;
    }
  }

  return 0; // Return 0 if not a number or cannot be parsed
}

/**
 * Function to apply default values to parsed recipe data.
 * Ensures that all required fields are present and correctly typed.
 * @param recipeData - The data to be processed.
 * @returns Processed recipe data with default values applied.
 */
export function processRecipeData(recipeData: any) {
  return {
    recipeName: recipeData.recipeName || 'Unknown Recipe',
    ingredients: recipeData.ingredients || ['No ingredients specified'],
    measurements: recipeData.measurements || ['No measurements specified'],
    instructions: recipeData.instructions || ['No instructions specified'],
    cookingTime: recipeData.cookingTime || 'N/A',
    temperature: recipeData.temperature || 'N/A',

    // Ensure servingSize, proteins, fats, carbohydrates, and calories are parsed as numbers
    servingSize: extractNumber(recipeData.servingSize),
    proteins: extractNumber(recipeData.proteins),
    fats: extractNumber(recipeData.fats),
    carbohydrates: extractNumber(recipeData.carbohydrates),
    calories: extractNumber(recipeData.calories),

    preparationTime: recipeData.preparationTime || 'N/A',
    dietaryRestrictions: recipeData.dietaryRestrictions || 'None',
    cuisineType: recipeData.cuisineType || 'N/A',
    cookingMethod: recipeData.cookingMethod || 'N/A',
    storageInstructions: recipeData.storageInstructions || 'N/A',
    equipmentNeeded: recipeData.equipmentNeeded || 'N/A',

    // Initialize embedding fields as empty arrays; they'll be populated during ingestion
    ingredientsEmbedding: recipeData.ingredientsEmbedding || [],
    measurementsEmbedding: recipeData.measurementsEmbedding || [],
    instructionsEmbedding: recipeData.instructionsEmbedding || [],
  };
}

/**
 * Zod schema for validating processed recipe data.
 */
export const RecipesSchema = z.object({
  recipeName: z.string(),
  ingredients: z.array(z.string()),
  measurements: z.array(z.string()),
  instructions: z.array(z.string()),
  cookingTime: z.string().optional(),
  temperature: z.string().optional(),
  servingSize: z.number(),
  proteins: z.number(),
  fats: z.number(),
  carbohydrates: z.number(),
  calories: z.number(),
  preparationTime: z.string().optional(),
  dietaryRestrictions: z.string().optional(),
  cuisineType: z.string().optional(),
  cookingMethod: z.string().optional(),
  storageInstructions: z.string().optional(),
  equipmentNeeded: z.string().optional(),
  // Include embeddings if needed
  ingredientsEmbedding: z.array(z.number()).optional(),
  measurementsEmbedding: z.array(z.number()).optional(),
  instructionsEmbedding: z.array(z.number()).optional(),
});
