'use client';

import { useState, useEffect } from 'react';
import { signIn, signOut, useSession } from 'next-auth/react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

export default function AuthPopup() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (status === 'authenticated') {
      router.push('/fileManager'); // Redirect if authenticated
    }
  }, [status, router]);

  const handleSignIn = async () => {
    try {
      await signIn('google', { callbackUrl: '/fileManager' });
    } catch (error) {
      console.error('Sign in error:', error);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut({ callbackUrl: '/' }); // Redirect after signing out
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsModalOpen(!isModalOpen)} // Open pop-up on button click
        className="bg-fuchsia-950 hover:bg-rose-400 text-white font-bold py-2 px-4 rounded transition duration-300 ease-in-out transform hover:scale-105"
      >
        {session ? 'Profile' : 'Sign In'}
      </button>

      {isModalOpen && (
        <div className="absolute top-12 right-0 bg-white p-4 rounded-lg shadow-lg w-80 z-50">
          {/* Modal Header */}
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center space-x-3">
              {session?.user?.image && (
                <Image
                  src={session.user.image}
                  alt={`${session.user.name}'s profile`}
                  width={40}
                  height={40}
                  className="rounded-full"
                />
              )}
              <div>
                <h2 className="text-md font-semibold">
                  {session?.user?.name ?? 'Guest User'}
                </h2>
                {session && (
                  <p className="text-sm text-gray-600">{session?.user?.email}</p>
                )}
              </div>
            </div>
            <button
              className="text-gray-500 hover:text-gray-700"
              onClick={() => setIsModalOpen(false)}
            >
              ✕
            </button>
          </div>

          {/* Modal Body */}
          <div className="mt-4">
            {session ? (
              <button
                onClick={handleSignOut}
                className="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded w-full transition duration-300 ease-in-out transform hover:scale-105"
              >
                Sign Out
              </button>
            ) : (
              <button
                onClick={handleSignIn}
                className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded w-full transition duration-300 ease-in-out transform hover:scale-105"
              >
                Sign in with Google
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
