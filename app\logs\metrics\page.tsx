'use client'

import React, { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON>hart2, 
  <PERSON><PERSON>, 
  MessageCircle, 
  <PERSON><PERSON>, 
  Clock, 
  Arrow<PERSON>p, 
  MessageSquare, 
  AlertCircle,
  AlertTriangle,
  Loader2
} from 'lucide-react'
import { 
  collection, 
  query, 
  where, 
  getDocs, 
  Timestamp, 
  orderBy,
  QueryDocumentSnapshot,
  DocumentData,
  FirestoreError
} from 'firebase/firestore'
import { db } from 'components/firebase'

// Types
interface DashboardMetrics {
  totalRequests: number
  successRate: number
  totalMessages: number
  truncatedMessages: number
  totalTokens: number
  avgTokensPerMessage: number
  contextTokens: number
  systemPromptTokens: number
  chatHistoryTokens: number
  responseTime: number
  activeUsers: number
  messagesOverTokenLimit: number
  averageMessageLength: number
  willBeTruncated: boolean
  originalCount: number
  convertedCount: number
}

interface ProcessMetrics extends DocumentData {
  contextTokens: number
  systemPromptTokens: number
  chatHistoryTokens: number
  timestamp: Timestamp
  averageRelevance: number
  chunkCount: number
  namespaceDistribution: Record<string, number>
  totalTokens: number
}

interface QueryMetrics extends DocumentData {
  status: string
  startTime: number
  completionTime: number
  userId: string
  timestamp: Timestamp
  error?: string
}

interface MetricCardProps {
  title: string
  value: string | number
  subtitle: string
  icon: React.ComponentType<any>
  alert?: boolean
  loading?: boolean
}

type TimeRange = '1h' | '24h'

// Constants
const INITIAL_METRICS: DashboardMetrics = {
  totalRequests: 0,
  successRate: 0,
  totalMessages: 0,
  truncatedMessages: 0,
  totalTokens: 0,
  avgTokensPerMessage: 0,
  contextTokens: 0,
  systemPromptTokens: 300,
  chatHistoryTokens: 0,
  responseTime: 0,
  activeUsers: 0,
  messagesOverTokenLimit: 0,
  averageMessageLength: 0,
  willBeTruncated: false,
  originalCount: 0,
  convertedCount: 0
}

const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white" />
  </div>
);


// Components
const MetricCard: React.FC<MetricCardProps> = ({ 
  title, 
  value, 
  subtitle, 
  icon: Icon, 
  alert,
  loading 
}) => (
  <div className={`bg-white rounded-lg shadow-md p-6 ${alert ? 'border-l-4 border-yellow-400' : ''}`}>
    <div className="flex items-center justify-between mb-2">
      <h3 className="text-sm font-medium text-gray-600">{title}</h3>
      <Icon className={`w-4 h-4 ${alert ? 'text-yellow-400' : 'text-gray-400'}`} />
    </div>
    <div className="mt-2">
      {loading ? (
        <div className="flex items-center space-x-2">
           <LoadingSpinner />;

          <span className="text-white">Loading...</span>
        </div>
      ) : (
        <div className="text-2xl font-bold text-gray-900">{value}</div>
      )}
      <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
    </div>
  </div>
)

const LoadingOverlay: React.FC = () => (
  <div className="flex justify-center items-center h-64">
    <div className="flex flex-col items-center">
    <LoadingSpinner />
      <p className="text-gray-500">Loading metrics...</p>
    </div>
  </div>
)

const ErrorAlert: React.FC<{ error: string }> = ({ error }) => (
  <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6 rounded-md">
    <div className="flex items-center">
      <AlertTriangle className="w-5 h-5 text-red-400 mr-2" />
      <p className="text-red-700">Error loading metrics: {error}</p>
    </div>
  </div>
)

// Utility functions
const calculateResponseTime = (queryMetrics: QueryMetrics[]): number => {
  const validResponses = queryMetrics.filter(q => 
    q.completionTime && 
    q.startTime && 
    q.completionTime > q.startTime &&
    (q.completionTime - q.startTime) < 60000
  )
  
  if (validResponses.length === 0) return 0

  const totalTime = validResponses.reduce((sum, q) => 
    sum + (q.completionTime - q.startTime), 0
  )
  
  return totalTime / validResponses.length / 1000
}

const getLatestProcessMetrics = (docs: QueryDocumentSnapshot[]): ProcessMetrics => {
  if (docs.length === 0) return {} as ProcessMetrics
  
  const sortedDocs = docs.sort((a, b) => {
    const aTimestamp = a.data().timestamp?.toMillis() || 0
    const bTimestamp = b.data().timestamp?.toMillis() || 0
    return bTimestamp - aTimestamp
  })
  
  return sortedDocs[0].data() as ProcessMetrics
}

// Main component
export default function MetricsDashboard() {
  const [timeRange, setTimeRange] = useState<TimeRange>('1h')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [metrics, setMetrics] = useState<DashboardMetrics>(INITIAL_METRICS)
  const [refreshKey, setRefreshKey] = useState(0)

  useEffect(() => {
    const fetchMetrics = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const startTime = new Date(
          Date.now() - (timeRange === '1h' ? 3600000 : 86400000)
        )

        const [
          queryMetricsSnapshot,
          tokenMetricsSnapshot,
          chatMetricsSnapshot,
          processMetricsSnapshot
        ] = await Promise.all([
          getDocs(query(
            collection(db, 'query_metrics'),
            where('timestamp', '>=', Timestamp.fromDate(startTime)),
            orderBy('timestamp', 'desc')
          )),
          getDocs(query(
            collection(db, 'token_metrics'),
            where('timestamp', '>=', Timestamp.fromDate(startTime)),
            orderBy('timestamp', 'desc')
          )),
          getDocs(query(
            collection(db, 'chat_metrics'),
            where('timestamp', '>=', Timestamp.fromDate(startTime)),
            orderBy('timestamp', 'desc')
          )),
          getDocs(query(
            collection(db, 'process_metrics'),
            where('timestamp', '>=', Timestamp.fromDate(startTime)),
            orderBy('timestamp', 'desc')
          ))
        ])

        const queryMetrics = queryMetricsSnapshot.docs.map(doc => doc.data() as QueryMetrics)
        const tokenMetrics = tokenMetricsSnapshot.docs.map(doc => doc.data())
        const chatMetrics = chatMetricsSnapshot.docs.map(doc => doc.data())
        const latestProcessMetrics = getLatestProcessMetrics(processMetricsSnapshot.docs)

        // Calculate metrics
        const totalRequests = queryMetrics.length
        const successfulQueries = queryMetrics.filter(q => q.status === 'completed').length
        const successRate = totalRequests ? (successfulQueries / totalRequests) * 100 : 0

        const totalTokens = tokenMetrics.reduce((sum, doc) => sum + (doc.totalTokens || 0), 0)
        const messageCount = tokenMetrics.reduce((sum, doc) => sum + (doc.messageCount || 0), 0)
        const avgTokensPerMessage = messageCount ? totalTokens / messageCount : 0

        const avgResponseTime = calculateResponseTime(queryMetrics)
        const latestChatMetrics = chatMetrics[0] || {}
        const uniqueUsers = new Set(queryMetrics.map(q => q.userId)).size

        setMetrics({
          totalRequests,
          successRate,
          totalMessages: latestChatMetrics?.totalMessages || 0,
          truncatedMessages: latestChatMetrics?.truncatedMessages || 0,
          totalTokens,
          avgTokensPerMessage,
          contextTokens: latestProcessMetrics?.contextTokens || 0,
          systemPromptTokens: latestProcessMetrics?.systemPromptTokens || 300,
          chatHistoryTokens: latestProcessMetrics?.chatHistoryTokens || 0,
          responseTime: avgResponseTime,
          activeUsers: uniqueUsers,
          messagesOverTokenLimit: latestChatMetrics?.messagesOverTokenLimit || 0,
          averageMessageLength: latestChatMetrics?.averageMessageLength || 0,
          willBeTruncated: latestChatMetrics?.willBeTruncated || false,
          originalCount: latestChatMetrics?.originalCount || 0,
          convertedCount: latestChatMetrics?.convertedCount || 0
        })

      } catch (err) {
        const errorMessage = err instanceof FirestoreError 
          ? `${err.code}: ${err.message}`
          : 'An unexpected error occurred'
        setError(errorMessage)
        console.error('Error fetching metrics:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchMetrics()
    
    // Set up auto-refresh every minute
    const intervalId = setInterval(() => {
      setRefreshKey(prev => prev + 1)
    }, 60000)

    return () => clearInterval(intervalId)
  }, [timeRange, refreshKey])

  return (
    <div className="container mx-auto px-4 py-8 bg-ike-message-bg mt-5">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-white">Metrics Dashboard</h1>
        <div className="flex space-x-4">
          {(['1h', '24h'] as const).map(range => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-4 py-2 rounded-md transition-colors ${
                timeRange === range 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Last {range === '1h' ? 'Hour' : '24 Hours'}
            </button>
          ))}
        </div>
      </div>

      {error && <ErrorAlert error={error} />}

      {loading && !error ? (
        <LoadingOverlay />
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <MetricCard
            title="Total Requests"
            value={metrics.totalRequests.toLocaleString()}
            subtitle={`Last ${timeRange === '1h' ? 'hour' : '24 hours'}`}
            icon={BarChart2}
            loading={loading}
          />
          <MetricCard
            title="Success Rate"
            value={`${metrics.successRate.toFixed(1)}%`}
            subtitle="Completed queries"
            icon={Zap}
            loading={loading}
          />
          <MetricCard
            title="Messages"
            value={metrics.totalMessages.toLocaleString()}
            subtitle={`${metrics.truncatedMessages} truncated`}
            icon={MessageCircle}
            alert={metrics.truncatedMessages > 0}
            loading={loading}
          />
          <MetricCard
            title="Chat History"
            value={metrics.chatHistoryTokens.toLocaleString()}
            subtitle={`${metrics.messagesOverTokenLimit} over limit`}
            icon={MessageSquare}
            alert={metrics.messagesOverTokenLimit > 0}
            loading={loading}
          />
          <MetricCard
            title="Message Processing"
            value={`${metrics.convertedCount}/${metrics.originalCount}`}
            subtitle={`Avg length: ${metrics.averageMessageLength.toFixed(1)}`}
            icon={AlertCircle}
            alert={metrics.willBeTruncated}
            loading={loading}
          />
          <MetricCard
            title="Token Usage"
            value={metrics.totalTokens.toLocaleString()}
            subtitle={`Avg ${metrics.avgTokensPerMessage.toFixed(1)} per message`}
            icon={Tags}
            loading={loading}
          />
          <MetricCard
            title="Context Tokens"
            value={metrics.contextTokens.toLocaleString()}
            subtitle="Current context size"
            icon={ArrowUp}
            loading={loading}
          />
          <MetricCard
            title="System Prompt"
            value={metrics.systemPromptTokens.toLocaleString()}
            subtitle="Tokens used"
            icon={Tags}
            loading={loading}
          />
          <MetricCard
            title="Response Time"
            value={`${metrics.responseTime.toFixed(2)}s`}
            subtitle="Average response time"
            icon={Clock}
            loading={loading}
          />
        </div>
      )}
    </div>
  )
}