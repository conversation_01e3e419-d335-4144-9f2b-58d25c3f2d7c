import { getApp, getApps, initializeApp, App, cert } from "firebase-admin/app";
import { getFirestore } from "firebase-admin/firestore";
import * as admin from "firebase-admin";

let app: App;

if (getApps().length === 0) {
  // Try to get service account from environment variable first
  let serviceAccount;

  if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
    try {
      serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
    } catch (error) {
      console.error('Error parsing FIREBASE_SERVICE_ACCOUNT_KEY:', error);
    }
  }

  // Fallback to service_key.json file if environment variable is not available
  if (!serviceAccount) {
    try {
      serviceAccount = require('../service_key.json');
    } catch (error) {
      console.error('Error loading service_key.json:', error);
      throw new Error('Firebase service account configuration not found');
    }
  }

  app = initializeApp({
    credential: cert(serviceAccount),
    storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
  });
} else {
  app = getApp();
}

const adminDb = getFirestore(app);
const adminStorage = admin.storage();

export { app as adminApp, adminDb, adminStorage };
