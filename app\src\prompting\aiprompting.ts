/**
 * aiprompting.ts
 * 
 * This module handles the construction and formatting of AI prompts, integrating
 * system messages, chat history, and context into structured prompts for AI interaction.
 */

import { BasePromptValueInterface } from "@langchain/core/prompt_values";
import {
  SystemMessagePromptTemplate,
  HumanMessagePromptTemplate,
  AIMessagePromptTemplate,
  ChatPromptTemplate,
  MessagesPlaceholder
} from "@langchain/core/prompts";

/**
 * Defines the structure for tracking token usage across different components
 */
interface TokenUsage {
  contextTokens: number;     // Tokens used by context
  chatHistoryTokens: number; // Tokens used by chat history
  totalTokens: number;       // Total tokens in use
  systemPromptTokens: number; // Tokens used by system prompt
}

/**
 * Defines the structure for document sources with relevance scoring
 */
interface Source {
  title: string;            // Document title
  page: number;            // Page number in document
  doc_id: string | undefined; // Document identifier
  relevance: number;       // Relevance score
}

/**
 * Base message interface
 */
interface BaseMessage {
  role: "user" | "ai";
  content: string;
  text: string;
}

/**
 * Parameters required for prompt creation
 */
export interface PromptParams {
  systemPrompt: string;
  context: string;
  metadata: Metadata;
  chatHistory: BaseMessage[];
  userQuery: string;
}

/**
 * Metadata interface for additional context and tracking
 */
export interface Metadata {
  sources: Source[];           // Array of document sources
  tokenUsage: TokenUsage;      // Token usage statistics
  relevance: number;           // Overall relevance score
  internetSearch?: boolean;    // Whether internet search was performed
  [key: string]: Source[] | TokenUsage | number | boolean | undefined;
}

export function getSystemPrompt(): string {
  return `Your name is iKe , pronounced EK. You are the professional who authored the Publications, report or content in the collection.  
With extensive knowledge and expertise in your field, 
you are helpful and dedicated to informing and assisting users with accurate and relevant information.

**Response Guidelines:**

1. **Source Attribution:**
- Clearly distinguish between internal knowledge base information and external internet sources
- When using external sources, begin your response with: "Based on external sources..."
- When using internal sources, begin with: "Based on our internal documentation..."
- For mixed sources, clearly separate and label each source of information
- Always include source references when citing external information

2. **Focused Expertise:**
- For internal content: Maintain your role as the content author and provide detailed information
- For external content: Act as an informed curator of the information
- Avoid including any information not explicitly contained in the provided sources

3. **Content Integration:**
- Clearly separate internal knowledge from external internet sources
- When using both, structure your response to show the progression from internal to external information
- Highlight any discrepancies or updates between internal and external sources
- Explicitly state when supplementing internal information with external sources

4. **Key Terms**: 
- Use blockquotes with bold headers to define important concepts and terminology, creating clear visual breaks in the text.
- Use **bold** for technical terms and concepts when first introduced, and *italics* for emphasis or to highlight key phrases. 
Create visual hierarchy through:
1. Clear paragraph breaks for major concept transitions
2. Strategic use of blockquotes for definitions and key insights
3. Bold terms for technical vocabulary
4. Italics for emphasis and nuance

5. **Chat History Awareness:**
- Consider the full context of the conversation, including previous exchanges
- Maintain consistency with previous responses while providing new relevant information

6. **If you don't have the answer:**
- Clearly state if the information is not available in the internal knowledge base
- When providing external sources, explicitly state that they are from internet searches
- If no information is available from either source, clearly state this
- Refer to specific sources when suggesting further reading or verification`;
}

export async function createPrompt({
  systemPrompt,
  context,
  metadata,
  chatHistory,
  userQuery,
}: PromptParams): Promise<BasePromptValueInterface> {
  try {
    // Format metadata section with clear source attribution
    const metadataSection = [
      "Document Information:",
      "Sources:",
      ...metadata.sources.map(source => {
        const sourceType = source.doc_id?.startsWith('ext_') ? '[External Source]' : '[Internal Source]';
        return `- ${sourceType} ${source.title} (Page ${source.page}) - Relevance: ${source.relevance.toFixed(2)}`;
      }),
      "",
      metadata.internetSearch ? "External Search: Performed" : "External Search: Not Required",
      "",
      "Token Usage:",
      `- Context Tokens: ${metadata.tokenUsage.contextTokens}`,
      `- Chat History Tokens: ${metadata.tokenUsage.chatHistoryTokens}`,
      `- Total Tokens: ${metadata.tokenUsage.totalTokens}`,
      "",
      `Average Relevance: ${metadata.relevance.toFixed(2)}`,
      "\n"
    ].join('\n');

    // Create base messages array with system prompt and metadata
    const basePrompt = ChatPromptTemplate.fromMessages([
      SystemMessagePromptTemplate.fromTemplate(systemPrompt),
      SystemMessagePromptTemplate.fromTemplate(metadataSection),
      new MessagesPlaceholder("chat_history"),
      SystemMessagePromptTemplate.fromTemplate(
        metadata.internetSearch 
          ? "Current Context (Including External Sources):\n{context}\n"
          : "Current Context (Internal Sources Only):\n{context}\n"
      ),
      HumanMessagePromptTemplate.fromTemplate("{query}")
    ]);

    // Transform chat history into proper message objects
    const chatHistoryMessages = chatHistory.flatMap((msg, index) => {
      const role = msg.role;
      const content = msg.text || msg.content;
      
      // Skip any undefined or empty messages
      if (!content) return [];
      
      const Template = role === "user" 
        ? HumanMessagePromptTemplate 
        : AIMessagePromptTemplate;
      
      return [Template.fromTemplate(String(content))];
    });

    // Format and return the complete prompt
    return await basePrompt.formatPromptValue({
      chat_history: chatHistoryMessages,
      context: context || "",
      query: userQuery || ""
    });

  } catch (error) {
    console.error("Error creating prompt:", {
      error,
      systemPrompt: !!systemPrompt,
      contextLength: context?.length,
      chatHistoryLength: chatHistory?.length,
      queryLength: userQuery?.length,
      metadata: {
        sourcesLength: metadata?.sources?.length,
        hasTokenUsage: !!metadata?.tokenUsage,
        relevance: metadata?.relevance,
        internetSearch: metadata?.internetSearch
      }
    });
    throw error;
  }
}