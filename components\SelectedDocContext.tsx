"use client"

import React, { createContext, useState, useContext } from 'react';

interface SelectedDocContextType {
  selectedDocumentId: string | null;
  setSelectedDocId: (id: string | null) => void;
}

const SelectedDocContext = createContext<SelectedDocContextType | undefined>(undefined);

export const SelectedDocProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [selectedDocumentId, setSelectedDocId] = useState<string | null>(null);

  return (
    <SelectedDocContext.Provider value={{ selectedDocumentId, setSelectedDocId }}>
      {children}
    </SelectedDocContext.Provider>
  );
};

export const useSelectedDoc = (): SelectedDocContextType => {
  const context = useContext(SelectedDocContext);
  if (!context) {
    throw new Error('useSelectedDoc must be used within a SelectedDocProvider');
  }
  return context;
};
