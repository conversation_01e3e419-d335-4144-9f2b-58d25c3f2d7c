import { Chat<PERSON>penAI } from "@langchain/openai";
import { z } from "zod";

export async function identifyLogicalSections(fullText: string): Promise<any> {
  const model = new ChatOpenAI({
    temperature: 0,
    model: process.env.OPENAI_MODEL!,
    apiKey: process.env.OPENAI_API_KEY!,
  });

  // Define a Zod schema to structure the output of logical sections
  const sectionSchema = z.object({
    sections: z.array(z.object({
      sectionTitle: z.string(),
      start: z.number(), // Start position in the text
      end: z.number(),   // End position in the text
    })),
  });

  const structuredLlm = model.withStructuredOutput(sectionSchema, {
    method: "json_mode",
    name: "SectionSchema",
  });

  const prompt = `
    Analyze the document content and identify the main sections such as "Introduction", "Body", 
    "Conclusion", "Chapters", "Summaries", "Key Points", "Sections","Actions" or "Instructions". 
    Use any structural clues like headings, numbering, or semantic patterns in the text. 
    Ignore non-content elements like page numbers and images.

    Document Content:
    ${fullText}

  The output should strictly follow this JSON format without any additional text:
    {
      "sections": [
        { "sectionTitle": "Introduction", "start": 0, "end": 500 },
        { "sectionTitle": "Chapter 1", "start": 501, "end": 2000 },
        ...
      ]
    }
  `;

  const response = await structuredLlm.invoke(prompt);

  if (response) {
    // Validate the response using the Zod schema
    const parseResult = sectionSchema.safeParse(response);
    if (!parseResult.success) {
      console.error("Schema validation failed:", parseResult.error);
      throw new Error("Failed to generate valid schema.");
    }

    return response; // The structured data is now validated
  } else {
    throw new Error("No valid structured schema generated from the LLM.");
  }
}
