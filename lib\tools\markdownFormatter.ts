// --- File: lib/tools/markdownFormatterTool.ts ---

import { FormattedScript, ScriptMetadata, ScriptLine } from './scriptFormatter';

export class MarkdownFormatterTool {
  /**
   * Format a script object into markdown for display
   * @param script - The formatted script object
   * @returns Markdown string representation of the script
   */
  async formatScriptToMarkdown(script: FormattedScript): Promise<string> {
    try {
      const { metadata, lines } = script;

      // Generate the markdown content
      let markdown = this._formatScriptMetadata(metadata); // Keep metadata formatting
      markdown += '\n\n';
      markdown += this._formatScriptLines(lines); // Use updated line formatting

      // Process the markdown for consistent rendering (optional, keep if needed)
      // return markdownRendererTool.preprocessMarkdown(markdown);
      return markdown; // Return raw markdown for ReactMarkdown to process
    } catch (error: any) {
      console.error("Error formatting script to markdown:", error);

      // --- Fallback logic remains largely the same, but should ideally
      // --- also try to match the new desired format if possible.
      // --- For brevity, the detailed fallback generation is omitted here,
      // --- but it should aim for ## CHARACTER \n\n Dialogue format too.
      try {
        let fallbackMarkdown = "";
        if (script && script.metadata) {
           const { author, characters, summary, title } = script.metadata;
           // Use title in fallback if needed, otherwise omit as per original logic
           // fallbackMarkdown += `# ${title || "Script"}\n\n`;
           if (author && author.toLowerCase() !== 'unknown author') {
             fallbackMarkdown += `### By ${author}\n\n`;
           }
           if (summary) {
             fallbackMarkdown += `### Summary\n${summary}\n\n`;
           }
           if (characters && characters.length > 0) {
             fallbackMarkdown += "### Characters\n";
             characters.forEach(character => {
               fallbackMarkdown += `- ${character}\n`;
             });
             fallbackMarkdown += "\n";
           }
        }

        if (script && script.lines && script.lines.length > 0) {
          fallbackMarkdown += "## Script\n\n";
          let currentCharacter = "";
          const linesToShow = script.lines.slice(0, 30); // Show more lines in fallback potentially
          linesToShow.forEach(line => {
              if (line.character && line.character !== currentCharacter) {
                  currentCharacter = line.character;
                  fallbackMarkdown += `## ${currentCharacter}\n\n`; // Use H2 for character
              }
              if (line.text) {
                  fallbackMarkdown += `${line.text}\n\n`; // Dialogue as paragraph
              }
              if (line.notes) {
                  fallbackMarkdown += `*${line.notes}*\n\n`; // Notes italicized
              }
          });
          if (script.lines.length > 30) {
            fallbackMarkdown += "\n*... more lines not shown ...*\n";
          }
        } else {
           fallbackMarkdown += "\n\n*Could not display script lines.*\n";
        }
        return fallbackMarkdown;
        // return markdownRendererTool.preprocessMarkdown(fallbackMarkdown);
      } catch (fallbackError) {
        console.error("Error creating fallback markdown:", fallbackError);
        return "## Original Content\n\nShowing unformatted script content.";
      }
    }
  }

  /**
   * Format script metadata into markdown
   * @param metadata - Script metadata
   * @returns Markdown string for the metadata section
   */
  private _formatScriptMetadata(metadata: ScriptMetadata): string {
    // --- This function remains the same ---
    const { title, author, characters, summary } = metadata;
    let markdown = '';
    // Don't repeat the title as it's already shown in the tab header
    if (author && author.toLowerCase() !== 'unknown author') {
      markdown += `### By ${author}\n\n`;
    }
    if (summary && summary.toLowerCase() !== 'could not extract summary from script.') {
      markdown += `### Summary\n${summary}\n\n`;
    }
    if (characters && characters.length > 0 &&
        !(characters.length === 1 && characters[0].toLowerCase().includes('character'))) {
      markdown += `### Characters\n`;
      characters.forEach(character => {
        // Keep character list bold, or make plain based on preference
        markdown += `- **${character}**\n`;
      });
      markdown += `\n`;
    }
    return markdown;
  }

  /**
   * Format script lines into markdown with Character Headings and Paragraphs
   * @param lines - Array of script lines
   * @returns Markdown string for the lines section
   */
  private _formatScriptLines(lines: ScriptLine[]): string {
    let markdown = '## Script\n\n'; // Section header
    let currentCharacter = ""; // Track the last character header printed

    lines.forEach(line => {
      const { character, text, notes } = line;

      // Check if character is valid and different from the last one printed
      // Also handles cases where the character might be "ACTION" or similar generic stage direction indicator
      if (character && character.trim() && character !== currentCharacter) {
          currentCharacter = character;
          // Output character name as H2 heading
          markdown += `## ${character.trim()}\n\n`;
      } else if (!character && text && currentCharacter !== "ACTION/NOTE") {
          // Handle lines without a specific character (e.g., scene descriptions, general stage directions)
          // Check if we need a generic header or just print the text.
          // Let's just print the text for now, assuming it's description/action.
          // Could add a check here later if a specific header like "### Action" is desired.
          currentCharacter = "ACTION/NOTE"; // Prevent repeating this section immediately if multiple such lines occur
          // No specific header, just the text will follow.
      }

      // Output the dialogue or action text as a paragraph
      if (text && text.trim()) {
          markdown += `${text.trim()}\n\n`; // Add two newlines for paragraph break
      }

      // Output notes if present, italicized, as a separate paragraph
      if (notes && notes.trim()) {
          markdown += `*${notes.trim()}*\n\n`; // Add two newlines for paragraph break
      }
    });

    // Remove potential excess trailing whitespace/newlines
    return markdown.trim() + '\n';
  }


  /**
   * Format script lines into a table format (Keep as is, not relevant to main request)
   * @param lines - Array of script lines
   * @returns Markdown table string
   */
  formatScriptToTable(lines: ScriptLine[]): string {
    // --- This function remains the same ---
    let markdown = '| Line | Character | Text | Notes |\n';
    markdown += '| ---- | --------- | ---- | ----- |\n';

    lines.forEach(line => {
      const { lineNumber, character, text, notes } = line;
      // Use hybrid approach for table line numbers too if this format is used elsewhere
      markdown += `| <span class="line-number" style="color:#FF8C00;font-weight:bold;">**${lineNumber || ''}**</span> | **${character || ''}** | ${text || ''} | ${notes || ''} |\n`;
    });

    return markdown;
  }
}

// Export a singleton instance
export const markdownFormatterTool = new MarkdownFormatterTool();