import { Tool } from "@langchain/core/tools";
import { StreamOptions } from "@/src/types/shared";
import { CallbackHandlerMethods } from "@langchain/core/callbacks/base";
import { LLMResult } from "@langchain/core/outputs";

interface StreamToClientInput {
  streamOptions: StreamOptions;
  content: string | null;
  isMetadata?: boolean;
  closeStream?: boolean;
}

interface StreamToClientOutput {
  success: boolean;
  error?: string;
}

export class StreamToClientTool extends Tool {
  name = "streamToClient";
  description = "Stream data to client with metadata handling";

  private readonly CLOSE_STREAM_DELAY = 500;
  private streamControllers: Map<string, {
    controller: ReadableStreamDefaultController;
    isClosed: boolean;
    isProcessing: boolean;
  }> = new Map();

  createCallbackHandler(streamOptions: StreamOptions): CallbackHandlerMethods {
    if (!streamOptions.controller) {
      throw new Error('Stream controller is required');
    }

    const streamId = streamOptions.streamId || 'default';
    this.streamControllers.set(streamId, {
      controller: streamOptions.controller,
      isClosed: false,
      isProcessing: true
    });

    return {
      handleLLMNewToken: async (token: string): Promise<void> => {
        const stream = this.streamControllers.get(streamId);
        if (stream && !stream.isClosed) {
          try {
            stream.controller.enqueue(token);
          } catch (error) {
            console.error('Error enqueueing token:', error);
          }
        }
      },
      handleLLMEnd: async (output: LLMResult): Promise<void> => {
        try {
          const stream = this.streamControllers.get(streamId);
          if (stream) {
            stream.isProcessing = false;
            if (!stream.isClosed && !stream.isProcessing) {
              await this.closeStream(streamOptions);
            }
          }
        } catch (error) {
          console.error('Error in handleLLMEnd:', error);
        }
      },
      handleLLMError: async (error: Error): Promise<void> => {
        console.error("LLM Error in stream:", error);
        try {
          await this.streamError(streamOptions, error);
          const stream = this.streamControllers.get(streamId);
          if (stream) {
            stream.isProcessing = false;
            if (!stream.isClosed) {
              await this.closeStream(streamOptions);
            }
          }
        } catch (streamError) {
          console.error('Error handling LLM error:', streamError);
        }
      }
    };
  }

  async _call(input: StreamToClientInput): Promise<StreamToClientOutput> {
    try {
      this.validateInput(input);
      const { streamOptions, content, isMetadata = false, closeStream = false } = input;

      const stream = this.getStreamController(streamOptions);
      if (!stream || stream.isClosed) {
        return { success: false, error: 'Stream unavailable or closed' };
      }

      if (!content && closeStream) {
        await this.closeStream(streamOptions);
        return { success: true };
      }

      if (content) {
        if (isMetadata) {
          const metadata = typeof content === 'string' ? content : JSON.stringify(content);
          stream.controller.enqueue(JSON.stringify({
            type: 'metadata',
            content: metadata
          }));
        } else {
          stream.controller.enqueue(content);
        }

        if (closeStream) {
          // Close stream after final message
          await this.closeStream(streamOptions);
        }
      }

      return { success: true };

    } catch (error) {
      console.error("StreamToClientTool: Error streaming to client:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private getStreamController(streamOptions: StreamOptions) {
    const streamId = streamOptions.streamId || 'default';
    return this.streamControllers.get(streamId);
  }

  private validateInput(input: StreamToClientInput): void {
    if (!input.streamOptions?.controller) {
      throw new Error("Stream controller is required");
    }

    if (input.content !== null && typeof input.content !== 'string') {
      throw new Error("Content must be a string or null");
    }
  }

  async startProcessing(streamOptions: StreamOptions): Promise<void> {
    const stream = this.getStreamController(streamOptions);
    if (stream) {
      stream.isProcessing = true;
      console.log(`Stream ${streamOptions.streamId}: Started processing`);
    }
  }

  async finishProcessing(streamOptions: StreamOptions): Promise<void> {
    const stream = this.getStreamController(streamOptions);
    if (stream) {
      stream.isProcessing = false;
      console.log(`Stream ${streamOptions.streamId}: Finished processing`);
      if (!stream.isClosed && !stream.isProcessing) {
        await this.closeStream(streamOptions);
      }
    }
  }

  async streamMetadata(streamOptions: StreamOptions, metadata: string | Record<string, any>): Promise<void> {
    const stream = this.getStreamController(streamOptions);
    if (stream && !stream.isClosed) {
      const formattedMetadata = typeof metadata === 'string' ? metadata : JSON.stringify(metadata);
      const message = JSON.stringify({
        type: 'metadata',
        content: formattedMetadata
      });
      stream.controller.enqueue(message);
    }
  }

  async streamError(streamOptions: StreamOptions, error: Error): Promise<void> {
    const stream = this.getStreamController(streamOptions);
    if (stream && !stream.isClosed) {
      const errorMessage = JSON.stringify({
        type: 'error',
        content: {
          name: error.name,
          message: error.message,
          stack: error.stack
        }
      });
      stream.controller.enqueue(errorMessage);
    }
  }

  async handleError(controller: ReadableStreamDefaultController, error: Error): Promise<void> {
    try {
      const errorPayload = JSON.stringify({
        type: 'error',
        content: {
          name: error.name,
          message: error.message,
          stack: error.stack
        }
      });
      controller.enqueue(errorPayload);
      await new Promise(resolve => setTimeout(resolve, this.CLOSE_STREAM_DELAY));
      controller.close();
    } catch (e) {
      console.error('Error handling stream error:', e);
    }
  }

  async streamToken(streamOptions: StreamOptions, token: string): Promise<StreamToClientOutput> {
    const stream = this.getStreamController(streamOptions);
    if (stream && !stream.isClosed) {
      stream.controller.enqueue(token);
      return { success: true };
    }
    return { success: false, error: 'Stream unavailable or closed' };
  }

  async streamVisualization(
    streamOptions: StreamOptions,
    visualization: any
  ): Promise<void> {
    const stream = this.getStreamController(streamOptions);
    if (stream && !stream.isClosed) {
      const message = JSON.stringify({
        type: 'visualization',
        content: visualization
      });
      stream.controller.enqueue(message);
      // After sending the final visualization JSON, ensure processing is finished
      await this.finishProcessing(streamOptions); 
    }
  }

  private async closeStream(streamOptions: StreamOptions): Promise<void> {
    const streamId = streamOptions.streamId || 'default';
    const stream = this.streamControllers.get(streamId);
    
    if (stream && !stream.isClosed) {
      if (stream.isProcessing) {
        console.log(`Stream ${streamId}: Attempt to close while still processing`);
        return;
      }
      
      try {
        stream.isClosed = true;
        await new Promise(resolve => setTimeout(resolve, this.CLOSE_STREAM_DELAY));
        stream.controller.close();
        console.log(`Stream ${streamId}: Closed successfully`);
      } catch (error) {
        console.warn(`Stream ${streamId}: Error while closing:`, error);
      } finally {
        this.streamControllers.delete(streamId);
      }
    }
  }

  async streamFallback(streamOptions: StreamOptions): Promise<StreamToClientOutput> {
    return this._call({
      streamOptions,
      content: "No relevant content found",
      closeStream: true
    });
  }
}
