'use client'

import { addDoc, collection, serverTimestamp } from 'firebase/firestore'
import { db } from 'components/firebase'
import React from 'react'

interface Source {
  title: string
  page: string | number
  relevance: number
  diversity: number
}

interface Metadata {
  sources: Source[]
  totalTokens: number
  chunkCount: number
  averageRelevance: number
  averageDiversity: number
}

interface LogEntry {
  metadata: Metadata
  userRequest: string
  response: string
}

async function logMetadata(userId: string, logData: LogEntry) {
  try {
    const docRef = await addDoc(collection(db, 'users', userId, 'metadata_logs'), {
      ...logData,
      timestamp: serverTimestamp()
    })
    console.log('Metadata logged successfully with ID:', docRef.id)
    return docRef.id
  } catch (error) {
    console.error('Error logging metadata:', error)
    throw error
  }
}

export default function MetadataLogger({ userId, metadata, userRequest, response }: { userId: string, metadata: Metadata, userRequest: string, response: string }) {
  const handleLog = async () => {
    try {
      const logEntry: LogEntry = {
        metadata,
        userRequest,
        response
      }
      const logId = await logMetadata(userId, logEntry)
      console.log('Metadata log entry created with ID:', logId)
    } catch (error) {
      console.error('Failed to log metadata:', error)
    }
  }

  // Call handleLog immediately when the component mounts
  React.useEffect(() => {
    handleLog()
  }, [])

  // This component doesn't render anything visible
  return null
}