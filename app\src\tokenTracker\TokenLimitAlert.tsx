// src/components/TokenLimitAlert.tsx
import React, { useEffect, useState } from 'react';
import { AlertCircle } from 'lucide-react';

export interface TokenUsage {
  contextTokens: number;
  systemPromptTokens: number;
  chatHistoryTokens: number;
  totalTokens: number;
}

interface TokenLimitAlertProps {
  tokenUsage: TokenUsage;
  maxTokens?: number;
  warningThreshold?: number;
  onNewChatClick?: () => void;
}

const TokenLimitAlert = ({
  tokenUsage,
  maxTokens = 5000,
  warningThreshold = 0.7,
  onNewChatClick
}: TokenLimitAlertProps) => {
  const [showWarning, setShowWarning] = useState(false);
  const [remainingTokens, setRemainingTokens] = useState(maxTokens);
  const [usagePercentage, setUsagePercentage] = useState(0);

  useEffect(() => {
    const remaining = maxTokens - tokenUsage.totalTokens;
    const percentage = (tokenUsage.totalTokens / maxTokens) * 100;
    
    setRemainingTokens(remaining);
    setUsagePercentage(percentage);
    setShowWarning(percentage >= (warningThreshold * 100));
  }, [tokenUsage, maxTokens, warningThreshold]);

  if (!showWarning) return null;

  return (
    <div className="mb-4 p-4 bg-ike-dark-purple border border-amber-600 rounded-lg">
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <AlertCircle className="h-5 w-5 text-amber-500" />
        </div>
        <div className="flex-1">
          <h3 className="text-amber-500 font-semibold text-lg mb-2 flex items-center gap-2">
            Token Limit Warning
          </h3>
          <div className="text-amber-200 text-sm">
            <p className="mb-2">
              This chat is using {usagePercentage.toFixed(1)}% of available tokens
              ({remainingTokens.toLocaleString()} tokens remaining).
            </p>
            <div className="flex items-center gap-2">
              <span>Please start a</span>
              <button
                onClick={onNewChatClick}
                className="text-amber-500 hover:text-amber-400 font-semibold underline focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 rounded px-1"
              >
                New Chat
              </button>
              <span>to continue the conversation.</span>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-3 w-full h-2 bg-ike-message-ai rounded-full overflow-hidden">
        <div 
          className="h-full bg-amber-500 transition-all duration-300 ease-in-out"
          style={{ width: `${Math.min(usagePercentage, 100)}%` }}
        />
      </div>
    </div>
  );
};

export default TokenLimitAlert;