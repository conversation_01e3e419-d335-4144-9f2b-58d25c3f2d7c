// components/dashboard.tsx

"use client"; // Add this directive to mark as a client component

// Import necessary libraries
import React, { ReactNode } from 'react';
import { SessionProvider } from 'next-auth/react'; // Ensure this is imported
import { SelectedDocProvider } from 'components/SelectedDocContext';
import { useSession } from 'next-auth/react'; // Import useSession for session management
import SideBar from 'components/SideBar';
// import Header from 'components/Header';

// Define the type for the props
interface FileManagerProps {
    children: ReactNode;
}

// Functional component for FileManager using function keyword
function FileManager({ children }: FileManagerProps) {
    const { data: session } = useSession(); // Get the current session

    return (
        <SessionProvider session={session}> {/* Pass the session to SessionProvider */}
            <SelectedDocProvider> {/* Wrap with SelectedDocProvider */}
                <div className="flex flex-col min-h-screen text-slate-900">
                    {/* <Header /> */}
                    <div className="flex flex-row h-screen overflow-y-auto"> {/* Adjusted layout here */}
                        <div className="bg-gray-900 w-64"> {/* Ensure this matches the sidebar width */}
                            <SideBar />
                        </div>
                        <main className="flex-1 overflow-y-auto bg-white text-slate-900">
                            {children}
                        </main>
                    </div>
                </div>
            </SelectedDocProvider>
        </SessionProvider>
    );
}

export default FileManager;
