// app/questionnaire/layout.tsx
'use client';

import React, { ReactNode } from 'react';
import { SessionProvider } from 'next-auth/react';
import { ThemeProvider } from 'contexts/ThemeContext';

interface QuestionnaireLayoutProps {
  children: ReactNode;
}

export default function QuestionnaireLayout({ children }: QuestionnaireLayoutProps) {
  return (
    <SessionProvider>
      <ThemeProvider>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-950 transition-colors duration-300">
          {children}
        </div>
      </ThemeProvider>
    </SessionProvider>
  );
}
