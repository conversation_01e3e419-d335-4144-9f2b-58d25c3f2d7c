import { Pinecone } from "@pinecone-database/pinecone";
import { ChatOpenAI } from "@langchain/openai";
import { SystemMessagePromptTemplate, HumanMessagePromptTemplate, ChatPromptTemplate } from "@langchain/core/prompts";
import { Document as CustomDocument } from "langchain/document";
import { FirestoreStore } from "./FirestoreStore"; // Ensure correct import path

// Initialize Pinecone
const pinecone = new Pinecone();
const pineconeIndex = pinecone.Index(process.env.PINECONE_INDEX!);



let concatenatedPageNumbers = '';
let concatenatedPageContent = '';
let concatenatedPageTitle = '';
let relevantDocsForAI = '';

// Helper function to stream data to the client
async function streamToClient(controller: ReadableStreamDefaultController<any>, chunk: string) {
  const encoder = new TextEncoder();
  controller.enqueue(encoder.encode(chunk));
}

const FALLBACK_MESSAGE = "I'm sorry, I couldn't find any information to answer your request. Please try again with a different question.";

/**
 * Main function to query Pinecone and process AI response.
 */
export async function queryOpenAIAcrossNamespacesAndProcessAI(
    controller: ReadableStreamDefaultController<any>,
    queryVector: number[],
    namespaces: string[] | null,
    userQuery: string,
    chatHistory: string,
    category: string | null,
    userId: string
) {
    console.log("User question:", userQuery);

    // Initialize FirestoreStore instance
const byteCollection = `users/${userId}/byteStoreCollection`;
const firestoreStore = new FirestoreStore({ collectionPath: byteCollection }); // FirestoreStore initialized with the correct path


    // Validate the query vector
    if (!Array.isArray(queryVector) || queryVector.length === 0 || !queryVector.every(num => typeof num === 'number')) {
      console.error("Invalid query vector provided.");
      await streamToClient(controller, FALLBACK_MESSAGE + '\nEND_METADATA\n');
      controller.close();
      return;
    }

    try {
        console.log(`Namespaces to query: ${namespaces?.join(', ') || 'No specific namespaces'}`);

        let concatenatedDocuments = '';

        const queryPromises = namespaces?.map(async (namespace) => {
            console.log(`Querying Pinecone for namespace: ${namespace}`);
            try {
                // Perform a broader similarity search without filters
                const queryResponse = await pineconeIndex.namespace(namespace).query({
                    vector: queryVector,
                    topK: 3,
                    includeValues: true,
                    includeMetadata: true,
                });

                // Extract document IDs from the matches
                return queryResponse.matches.map((match: any) => match.metadata.documentId);
            } catch (error) {
                console.error(`Error querying namespace ${namespace}:`, error);
                return [];
            }
        }) || [];

        const allResults = await Promise.all(queryPromises);
        const uniqueDocumentIds = [...new Set(allResults.flat())].filter(id => id !== undefined && id !== '');

        console.log("Unique Document IDs to fetch from Firestore:", uniqueDocumentIds);

        if (uniqueDocumentIds.length === 0) {
          console.warn("No valid Document IDs found to fetch from Firestore.");
          await streamToClient(controller, FALLBACK_MESSAGE  + '\nEND_METADATA\n');
          controller.close();
          return;
        }

        // Fetch document chunks using fetchDocumentChunksByDocId
        for (const documentId of uniqueDocumentIds) {
            const retrievedDocs = await firestoreStore.fetchDocumentChunksByDocId(documentId);
            retrievedDocs.forEach((doc: CustomDocument) => {
              if (doc) {
                // Extract metadata fields
                const metadata = doc.metadata || {};
                const content = metadata.text || doc.pageContent || 'No content available';
                const documentTitle = metadata.document_title || 'Unknown Title';
                const pageNumber = metadata.page_number || 'Unknown Page';

                // Logging for debugging purposes
                console.log(`Match metadata for document:`, metadata);
                console.log(`Match content:`, content);
                console.log(`Match documentTitle:`, documentTitle);
                console.log(`Match pageNumber:`, pageNumber);

                // Concatenate the page numbers and content
                concatenatedPageNumbers += `${pageNumber}, `;
                concatenatedPageContent += `Title: ${documentTitle} - Page: '${pageNumber}'\n\n\n${content}\n\n`;
                concatenatedPageTitle += `${documentTitle}\n`;
                relevantDocsForAI += `\nDocument (Title: ${documentTitle}, Page: ${pageNumber}): ${content}`;

                // Concatenate to the main documents for AI context
                concatenatedDocuments += `\n${content}\n`;
            }
        });
      }

      if (!concatenatedDocuments) {
        console.error('No documents retrieved to pass to the AI model.');
        await streamToClient(controller, FALLBACK_MESSAGE  + '\nEND_METADATA\n');
        controller.close();
        return;
      }

      console.log("Sample of Concatenated Documents:\n", concatenatedDocuments.slice(0, 1000)); // Adjust slice as needed

      // Pass metadata (concatenatedPageNumbers, concatenatedPageContent) to the client
      const metadataPayload = JSON.stringify({
        pageContent: concatenatedPageContent,
        pageNumber: concatenatedPageNumbers.slice(0, -2), // Remove trailing comma and space
        pageTitle: concatenatedPageTitle.slice(0, -2), // Remove trailing comma and space
      });

      // Send metadata to client
      await streamToClient(controller, metadataPayload);

      // Streaming the AI response
      const streamingComplete = new Promise<void>(async (resolve, reject) => {
        const model = new ChatOpenAI({
          streaming: true,
          temperature: 0.1,
          model: process.env.OPENAI_MODEL!,
          apiKey: process.env.OPENAI_API_KEY!,
          callbacks: [
            {
              handleLLMNewToken(token: any) {
                if (controller) {
                  controller.enqueue(new TextEncoder().encode(token));
                }
              },
              handleLLMEnd() {
                // Close the stream when the model signals the end
                controller.close();
                resolve();
              },
              handleLLMError(err: any) {
                console.error('Error during LLM streaming:', err);
                controller.error(new Error('Error during LLM streaming.'));
                reject(err);
              }
            },
          ],
        });

        let systemPromptTemplate: SystemMessagePromptTemplate;

        // Provide a fallback template if `category` is not "Recipes" or "Anthropometry"
        if (category) {
          systemPromptTemplate = SystemMessagePromptTemplate.fromTemplate(
                        `You are an expert in the ${category} field. Please:
            Identify the Target Audience:
            Determine the likely audience for the document, considering their knowledge level and background.
            Assess Expertise Level:
            Evaluate the probable expertise of individuals in this subject area and adopt the role of such an expert.
            Provide a Comprehensive Response:
            Using the provided context and any available chat history, answer the user's question with detailed and thorough information.`
          );
        } else {
          systemPromptTemplate = SystemMessagePromptTemplate.fromTemplate(
            `Given the context provided,
            Determine the likely audience for the document, considering their knowledge level and background.
            Assess Expertise Level:
            Evaluate the probable expertise of individuals in this subject area and adopt the role of such an expert.
            Provide a Comprehensive Response:
            Using the provided context and any available chat history, answer the user's question with detailed and thorough information.`
          );
        }

        const contextMessageTemplate = SystemMessagePromptTemplate.fromTemplate(
          `{context}`
        );

        const chatHistoryTemplate = SystemMessagePromptTemplate.fromTemplate(
          `{chatHistory}`
        );

        const translationPrompt = ChatPromptTemplate.fromMessages([
          systemPromptTemplate,
          chatHistoryTemplate,
          contextMessageTemplate,
          HumanMessagePromptTemplate.fromTemplate("{Prompt}"),
        ]);

        const formattedPrompt = await translationPrompt.formatPromptValue({
          context: concatenatedDocuments,
          chatHistory: chatHistory,
          Prompt: `User Message: ${userQuery}`,
        });

        console.log("Formatted AI Prompt:", formattedPrompt);

        model.generatePrompt([formattedPrompt]);

      });

      // Await the completion of streaming
      await streamingComplete;

    } catch (error) {
      console.error('Error querying and processing AI response:', error);
      await streamToClient(controller, FALLBACK_MESSAGE  + '\nEND_METADATA\n');
      controller.close();
    }
}
