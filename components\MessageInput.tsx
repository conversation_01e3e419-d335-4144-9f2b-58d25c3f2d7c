'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Send, StopCircle, ImagePlus, Mic, BarChart } from 'lucide-react';
import ImageGenerationModal from './image-generation/ImageGenerationModal';
import ChartGenerationModal from './Visualization/ChartGenerationModal';

interface MessageInputProps {
  onSendMessage: (message: string) => void;
  onStopProcessing: () => void;
  isPending: boolean;
  isFirstMessage?: boolean;
  onResetChat?: () => void;
  onToggleVoice: (active: boolean) => void;
  isVoiceActive: boolean;
}

export default function MessageInput({
  onSendMessage,
  onStopProcessing,
  isPending,
  isFirstMessage = false,
  onResetChat,
  onToggleVoice,
  isVoiceActive,
}: MessageInputProps) {
  const [message, setMessage] = useState('');
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isChartModalOpen, setIsChartModalOpen] = useState(false);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !isPending) {
      onSendMessage(message);
      setMessage('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleOpenImageModal = () => {
    if (onResetChat) {
      onResetChat();
    }
    setIsImageModalOpen(true);
  };

  const handleCloseImageModal = () => {
    setIsImageModalOpen(false);
  };

    const handleOpenChartModal = () => {
        if (onResetChat) {
            onResetChat();
        }
        setIsChartModalOpen(true);
    };

    const handleCloseChartModal = () => {
        setIsChartModalOpen(false);
    };


  const handleToggleVoice = () => {
    onToggleVoice(!isVoiceActive);
  };


  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="flex justify-between w-full max-w-2xl mx-auto gap-4 mt-6 -mb-4">
        <button
          type="button"
          onClick={handleOpenImageModal}
          className="px-4 py-2 ml-2 text-sm font-normal rounded bg-[#2d1d3a] text-blue-200 shadow-md hover:bg-[#3d2d4a] transition-colors flex items-center gap-2"
        >
          <ImagePlus className="w-4 h-4" />
          Create an image
        </button>
        <button
          type="button"
          onClick={handleToggleVoice}
          className={`px-4 py-2 text-sm font-normal rounded ${
            isVoiceActive
              ? 'bg-amber-600 text-white'
              : 'bg-[#2d1d3a] text-blue-200'
          } shadow-md hover:bg-[#3d2d4a] transition-colors flex items-center gap-2`}
        >
          <Mic className="w-4 h-4" />
          {isVoiceActive ? 'Voice Active' : 'Speak to iKe'}
        </button>
        <button
          type="button"
          onClick={handleOpenChartModal}
          className="px-4 py-2 mr-2 text-sm font-normal rounded bg-[#2d1d3a] text-blue-200 shadow-md hover:bg-[#3d2d4a] transition-colors flex items-center gap-2"
          disabled={isPending || isImageModalOpen || isChartModalOpen}
        >
          <BarChart className="w-4 h-4" />
          Create charts
      </button>
      </div>

      <form onSubmit={handleSubmit} className="relative space-y-2">
            <div className="relative">
              <textarea
                ref={textareaRef}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={
                  isFirstMessage 
                    ? "Ask your first question..." 
                    : "Message iKe..."
                }
                className={`w-full min-h-[98px] max-h-[200px] py-3 pr-12 pl-3 mt-3 text-sm text-gray-400 bg-ike-purple rounded-t-2xl shadow-sm shadow-black border border-amber-500 focus:ring-ike-purple resize-none dark:bg-ike-dark-purple dark:border-ike-purple_b ${isPending || isImageModalOpen ? 'cursor-not-allowed' : ''}`}
                rows={1}
                disabled={isPending || isImageModalOpen}
                aria-label={
                  isFirstMessage 
                    ? "Ask your first question" 
                    : "Message iKe"
                }
              />
              {message.trim() && !isPending && (
                <button
                  type="submit"
                  className="absolute right-2 top-[32%] transform -translate-y-1/2 p-2 rounded-full bg-ike-purple text-amber-500 hover:bg-ike-dark-purple transition-colors duration-200"
                  aria-label="Send message"
                >
                  <Send className="h-3 w-3" />
                </button>
              )}
              {isPending && ( 
                <button
                  type="button"
                  onClick={onStopProcessing}
                  className="absolute right-2 top-[32%] transform -translate-y-1/2 p-2 rounded-full bg-red-500 text-white hover:bg-red-600 transition-colors duration-200"
                  aria-label="Stop processing"
                >
                  <StopCircle className="h-3 w-3" />
                </button>
              )}
            </div>
          </form>

      {isImageModalOpen && (
        <ImageGenerationModal
          isOpen={isImageModalOpen}
          onClose={handleCloseImageModal}
          onImageGenerated={() => {}}
        />
      )}
        {isChartModalOpen && (
            <ChartGenerationModal
                isOpen={isChartModalOpen}
                onClose={handleCloseChartModal}
                onChartGenerated={() => {}}
            />
        )}
    </div>
  );
}