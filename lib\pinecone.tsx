import { Pinecone } from "@pinecone-database/pinecone";
import { PineconeStore } from "@langchain/pinecone";
import { OpenAIEmbeddings } from "@langchain/openai";

export async function getPineconeDocs(
  pineconeIndexName: string,
  namespace: string,
  message: string,
  filters: {
    category?: string;
    document_title?: string;  // Can now be a comma-separated list of titles
    page_number?: number;
  } = {}
) {
  const pinecone = new Pinecone();
  const pineconeIndex = pinecone.Index(pineconeIndexName);
  
  // Build the filter object dynamically based on the provided filters
  const pineconeFilters: any = { namespace };

  if (filters.category) {
    pineconeFilters.category = { "$eq": filters.category };
  }
  if (filters.document_title) {
    const titlesArray = filters.document_title.split(",").map(title => title.trim()); // Split the string into an array of titles
    pineconeFilters.document_title = { "$in": titlesArray };  // Use "$in" to match any of the titles
  }
  if (filters.page_number) {
    pineconeFilters.page_number = { "$eq": filters.page_number };
  }

  const vectorStore = await PineconeStore.fromExistingIndex(
    new OpenAIEmbeddings(),
    { pineconeIndex }
  );

  console.log("Applying filters to Pinecone query:", pineconeFilters);

  return await vectorStore
    .asRetriever({
      filter: pineconeFilters,
    })
    .getRelevantDocuments(message);
}
