"use client"

import React, { useEffect, useRef } from 'react';
import VisualizationRenderer from './VisualizationRenderer';

interface VisualizationPopupProps {
  isOpen: boolean;
  onClose: () => void;
  visualizationData: any;  // Replace 'any' with a more specific type if available
}

const VisualizationPopup: React.FC<VisualizationPopupProps> = ({ isOpen, onClose, visualizationData }) => {
  const popupRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div ref={popupRef} className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold text-gray-800">Visualization</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 transition-colors duration-200"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <p className="text-gray-600 mb-4">Here's the visualization you requested.</p>
          <div className="mt-4">
            <VisualizationRenderer data={visualizationData} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default VisualizationPopup;

