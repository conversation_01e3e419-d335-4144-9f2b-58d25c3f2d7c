import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { loadSummarizationChain } from "langchain/chains";
import { PromptTemplate } from "@langchain/core/prompts";
import { ChatOpenAI } from "@langchain/openai";
import { FirestoreStore } from "./FirestoreStore";
import { adminDb } from "components/firebase-admin";
import { PDFLoader } from "@langchain/community/document_loaders/fs/pdf";
import pLimit from "p-limit";
import { Document } from "langchain/document";

interface ChunkEntry {
  pageContent: string;
  metadata: {
    doc_id: string;
    chunk_id: string;
    category: string;
    document_title: string;
    sectionTitle?: string;
    questions?: string[];
    is_summary?: boolean; // Flag to indicate if it's a summary
    [key: string]: any;
  };
}

interface DocumentData {
  downloadUrl: string;
  name?: string;
  category?: string;
}

function parseSummaryText(summaryText: string): {
  title: string;
  summary: string;
  questions: string[];
} {
  const titleMatch = summaryText.match(/1\s*-\s*TITLE\s*:\s*(.+?)\n/);
  const summaryMatch = summaryText.match(
    /2\s*-\s*SUMMARY\s*:\s*([\s\S]+?)\n3\s*-\s*QUESTIONS\s*:/m
  );
  const questionsMatch = summaryText.match(/3\s*-\s*QUESTIONS\s*:\s*([\s\S]+)/);

  const title = titleMatch ? titleMatch[1].trim() : "";
  const summary = summaryMatch ? summaryMatch[1].trim() : "";
  const questionsText = questionsMatch ? questionsMatch[1].trim() : "";
  const questions = questionsText
    ? questionsText
        .split("\n")
        .map((q) => q.trim())
        .filter((q) => q)
    : [];

  return { title, summary, questions };
}

async function saveChunkToFirestore(
  chunkEntry: ChunkEntry,
  byteStore: FirestoreStore
) {
  try {
    const entry: [string, ChunkEntry] = [chunkEntry.metadata.chunk_id, chunkEntry];
    await byteStore.mset([entry]);
    console.log(`Saved chunk ${chunkEntry.metadata.chunk_id} to Firestore.`);
  } catch (error) {
    console.error(
      `Error saving chunk ${chunkEntry.metadata.chunk_id} to Firestore:`,
      error
    );
    throw new Error(
      `Failed to save chunk ${chunkEntry.metadata.chunk_id} to Firestore.`
    );
  }
}

export async function generateDocs(
  userId: string,
  docId: string
): Promise<ChunkEntry[]> {
  if (!userId) {
    throw new Error("User not signed in");
  }

  const byteCollection = `users/${userId}/byteStoreCollection`;
  const byteStore = new FirestoreStore({ collectionPath: byteCollection });

  const firebaseRef = await adminDb
    .collection("users")
    .doc(userId)
    .collection("files")
    .doc(docId)
    .get();

  const documentData = firebaseRef.data() as DocumentData | undefined;

  if (!documentData) {
    throw new Error(`No document data found for docId: ${docId}`);
  }

  const downloadUrl = documentData.downloadUrl;
  const documentName = documentData.name || "Untitled Document";
  const category = documentData.category || "Uncategorized";

  if (!downloadUrl) {
    throw new Error(`Cannot locate the file from the URL: ${downloadUrl}`);
  }

  console.log(`Download URL fetched successfully: ${downloadUrl}`);

  const response = await fetch(downloadUrl);
  if (!response.ok) {
    throw new Error(`Failed to fetch PDF from URL: ${downloadUrl}`);
  }
  const arrayBuffer = await response.arrayBuffer();

  const pdfLoader = new PDFLoader(new Blob([arrayBuffer]));
  const pdfDocument = await pdfLoader.load();

  const splitter = new RecursiveCharacterTextSplitter({
    chunkSize: 10000,
    chunkOverlap: 250,
  });

  // Split the document into large chunks
  const splitPdfDocs = await splitter.splitDocuments(pdfDocument);

  console.log(`Splitting document into ${splitPdfDocs.length} parts...`);

  // Initialize summarization chain
  const llmSummary = new ChatOpenAI({
    openAIApiKey: process.env.OPENAI_API_KEY!,
    temperature: 0.3,
  });

  const summaryTemplate = `
You are an expert in summarizing documents which can be books, publications, or programming code.
Your goal is to create a summary of the input.
Below you find the content:
--------
{text}
--------

The summary will also be used as the basis for a question and answer bot.
Provide some example questions that could be asked about the document. Make these questions very specific.

The output will be a summary of the document, a list of example questions the user could ask of the source document,
and metadata that will be uploaded to Pinecone vector store. The Metadata will follow the following format:

1 - TITLE :
2 - SUMMARY :
3 - QUESTIONS :
`;

  const SUMMARY_PROMPT = PromptTemplate.fromTemplate(summaryTemplate);

  const summarizeChain = loadSummarizationChain(llmSummary, {
    type: "stuff",
    verbose: true,
    prompt: SUMMARY_PROMPT,
  });

  const limit = pLimit(5);

  const docsWithMetadata: ChunkEntry[] = [];

  const chunkPromises = splitPdfDocs.map((chunk: Document, index: number) =>
    limit(async () => {
      const chunkId = `${docId}_${index + 1}`;

      // Create metadata for the original chunk
      const originalMetadata = {
        doc_id: docId,
        chunk_id: chunkId,
        category: category,
        document_title: documentName,
        // Add other metadata if needed
      };

      // Create ChunkEntry for the original chunk
      const originalChunkEntry: ChunkEntry = {
        pageContent: chunk.pageContent, // Original chunk content
        metadata: originalMetadata,
      };

      // Save the original chunk to Firestore
      await saveChunkToFirestore(originalChunkEntry, byteStore);

      // Generate summary for the chunk
      const summary = await summarizeChain.call({
        input_documents: [chunk],
      });

      // The summary will be in summary.text
      const summaryText = summary.text;

      // Parse the summaryText to extract TITLE, SUMMARY, and QUESTIONS
      const parsedSummary = parseSummaryText(summaryText);

      // Create metadata for the summary
      const summaryMetadata = {
        ...originalMetadata, // Use the same chunk_id and other metadata
        sectionTitle: parsedSummary.title || `Section ${index + 1}`,
        questions: parsedSummary.questions,
        is_summary: true, // Indicate that this is a summary
        // Add other metadata if needed
      };

      // Create final chunk entry for the summary
      const summaryChunkEntry: ChunkEntry = {
        pageContent: parsedSummary.summary, // Use the parsed summary
        metadata: summaryMetadata,
      };

      // Collect the summaryChunkEntry to return later for Pinecone processing
      docsWithMetadata.push(summaryChunkEntry);

      // Note: Do not save the summary to Firestore to prevent overwriting the original chunk

      return;
    })
  );

  await Promise.all(chunkPromises);
  console.log(`Processed all chunks. Original chunks saved to Firestore.`);

  return docsWithMetadata;
}
