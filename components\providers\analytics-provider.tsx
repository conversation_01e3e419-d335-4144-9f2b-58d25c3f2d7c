'use client';

import { useState, useEffect } from 'react';
import { AnalyticsMiddleware } from '../../lib/analytics/AnalyticsMiddleware';
import { AnalyticsContext, AnalyticsContextType } from '../../lib/analytics/context';
import { initializeFirebase } from 'components/firebase';

export function AnalyticsProvider({
  children
}: {
  children: React.ReactNode;
}) {
  const [analyticsState, setAnalyticsState] = useState<AnalyticsContextType>({
    analyticsMiddleware: null,
    isInitialized: false
  });

  useEffect(() => {
    const initAnalytics = async () => {
      try {
        const { db } = initializeFirebase();
        const middleware = new AnalyticsMiddleware();

        setAnalyticsState({
          analyticsMiddleware: middleware,
          isInitialized: true
        });
      } catch (error) {
        console.error('Failed to initialize analytics:', error);
      }
    };

    initAnalytics();
  }, []);

  return (
    <AnalyticsContext.Provider value={analyticsState}>
      {children}
    </AnalyticsContext.Provider>
  );
}