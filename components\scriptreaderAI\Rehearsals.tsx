import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>c<PERSON>ff, Volume2, VolumeX, <PERSON>tings, WifiOff, CheckCircle, X, ChevronLeft, ChevronRight, MessageCircle, Briefcase, Book } from 'lucide-react';
import { useSession } from 'next-auth/react';
import VoiceCarousel from './VoiceCarousel';
import { getVoiceById } from './voiceUtils';
import { type AgentModalityType } from './useAgentModality';

interface RehearsalsProps {
  apiConfigStatus: 'unchecked' | 'valid' | 'invalid' | 'connecting';
  detailedErrorInfo: string | null;
  isListening: boolean;
  voiceStatus: string;
  isMuted: boolean;
  isSpeaking: boolean;
  hasPermission: boolean;
  voiceErrorMessage: string;
  toggleMute: () => Promise<void>;
  handleEndConversation: () => Promise<void>;
  handleStartConversation: () => Promise<void>;
  setVoiceErrorMessage: (message: string) => void;
  selectedScriptName?: string;
  selectedVoiceId: string | null;
  onVoiceSelect: (voiceId: string) => void;
  isUpdatingVoice?: boolean;
  agentModality: AgentModalityType;
  onAgentModalityChange: (modality: AgentModalityType) => void;
  onSwitchToScriptTab: () => void;
  conversationMessages: Array<{
    id: string;
    type: 'user' | 'assistant';
    content: string;
    timestamp: Date;
  }>;
}

const Rehearsals: React.FC<RehearsalsProps> = ({
  apiConfigStatus,
  detailedErrorInfo,
  isListening,
  voiceStatus,
  isMuted,
  isSpeaking,
  hasPermission,
  voiceErrorMessage,
  toggleMute,
  handleEndConversation,
  handleStartConversation,
  setVoiceErrorMessage,
  selectedScriptName,
  selectedVoiceId,
  onVoiceSelect,
  isUpdatingVoice = false,
  agentModality,
  onAgentModalityChange,
  onSwitchToScriptTab,
  conversationMessages
}) => {
  const { data: session } = useSession();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [shouldFlashStartButton, setShouldFlashStartButton] = useState(false);

  // Handle voice selection to automatically open settings panel
  const handleVoiceSelection = (voiceId: string) => {
    console.log(`[REHEARSALS] Voice selected: ${voiceId}, opening settings panel...`);

    // Call the parent's voice selection handler
    onVoiceSelect(voiceId);

    // Automatically open the settings panel
    setIsSidebarOpen(true);

    // Start flashing the Start Rehearsing button
    setShouldFlashStartButton(true);

    // Stop flashing after 10 seconds
    setTimeout(() => {
      setShouldFlashStartButton(false);
    }, 10000);
  };

  // Enhanced agent modality change handler with logging
  const handleAgentModalityChange = (newModality: AgentModalityType) => {
    console.log(`[AGENT_MODALITY] User clicked to change modality from "${agentModality}" to "${newModality}"`);
    console.log(`[AGENT_MODALITY] Script context:`, {
      scriptName: selectedScriptName || 'No script selected',
      isListening: isListening,
      voiceStatus: voiceStatus,
      hasSelectedVoice: !!selectedVoiceId
    });

    if (newModality === agentModality) {
      console.log(`[AGENT_MODALITY] Modality "${newModality}" already active, no change needed`);
      return;
    }

    console.log(`[AGENT_MODALITY] Activating ${newModality} mode...`);
    onAgentModalityChange(newModality);
    console.log(`[AGENT_MODALITY] ✅ Modality changed to "${newModality}" successfully`);

    // Provide user feedback about when the modality will be applied
    if (isListening && voiceStatus === 'connected') {
      console.log(`[AGENT_MODALITY] ⚠️ Active conversation detected - new modality will take effect on next conversation start`);
      setVoiceErrorMessage(`${newModality === 'direct' ? 'Direct' : 'Conversational'} mode selected. Stop and restart conversation to apply.`);
      setTimeout(() => setVoiceErrorMessage(""), 4000);
    } else {
      console.log(`[AGENT_MODALITY] No active conversation - new modality will be applied when conversation starts`);
      setVoiceErrorMessage(`${newModality === 'direct' ? 'Direct' : 'Conversational'} mode selected. Will be applied when you start rehearsing.`);
      setTimeout(() => setVoiceErrorMessage(""), 3000);
    }
  };

  // Stop flashing when conversation starts
  React.useEffect(() => {
    if (voiceStatus === 'connected' || isListening) {
      setShouldFlashStartButton(false);
    }
  }, [voiceStatus, isListening]);

  return (
    <div className="relative h-full courier-font">
      {/* Chevron icon in top-right corner that toggles based on connection state */}
      <div className="absolute top-4 right-4 z-10">
        <motion.button
          onClick={() => setIsSidebarOpen(true)}
          className={`w-8 h-8 sm:w-24 flex items-center justify-center rounded-lg border transition-colors ${
            selectedVoiceId && !isSidebarOpen
              ? "bg-blue-600/20 border-blue-500/30 hover:bg-blue-600/30"
              : "bg-gray-600/20 border-gray-500/30 hover:bg-gray-600/30"
          }`}
          animate={selectedVoiceId && !isSidebarOpen ? {
            scale: [1, 1.05, 1],
            borderColor: ["rgba(59, 130, 246, 0.3)", "rgba(59, 130, 246, 0.6)", "rgba(59, 130, 246, 0.3)"]
          } : {}}
          transition={selectedVoiceId && !isSidebarOpen ? {
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          } : {}}
        >
          <ChevronLeft className={`w-4 h-4 ${
            selectedVoiceId && !isSidebarOpen ? "text-blue-400" : "text-gray-400"
          }`} />
          <span className={`text-sm hidden sm:inline ${
            selectedVoiceId && !isSidebarOpen ? "text-blue-400" : "text-gray-400"
          }`}>
            {selectedVoiceId ? "Settings" : "Connect"}
          </span>
        </motion.button>
      </div>

      {/* Main Content Area */}
      <div className="flex flex-col h-full overflow-y-auto custom-scrollbar">
        {/* Top Section with Logo and Script Info */}
        <div className="flex flex-col items-center justify-center py-8 space-y-6">
          {/* Logo Images */}
          {/* <div className="flex items-center space-x-4">
            <Image
              src="/Clogo6A.png"
              alt="CastMate Logo 1"
              width={60}
              height={60}
              className="object-contain"
            />
            <Image
              src="/Clogo7A.png"
              alt="CastMate Logo 2"
              width={60}
              height={60}
              className="object-contain"
            />
          </div> */}

          {/* Script Ready Text */}
          <div className="text-center space-y-4 -mt-10">
            <h2 className="text-xl font-bold text-white">
              Script ready for rehearsal
            </h2>

            {/* Selected Script Name Container */}
            <div className="bg-blue-50 dark:bg-black/60 border border-blue-200 dark:border-white/10 rounded-lg p-4 min-w-[300px] transition-all duration-300 shadow-sm">
              <div className="flex items-center justify-between">
                <p className="text-blue-700 dark:text-blue-400 text-lg font-medium">
                  {selectedScriptName || "No script selected"}
                </p>
                {selectedScriptName && (
                  <button
                    onClick={() => {
                      // Handle script deselection if needed
                      // This would typically call a parent function to clear the selected script
                    }}
                    className="ml-3 p-1 hover:bg-blue-100 dark:hover:bg-white/10 rounded-full transition-colors"
                    title="Clear script selection"
                  >
                    <X className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </button>
                )}
              </div>
            </div>

            {/* Connection Status Container */}
            <div className={`backdrop-blur-sm rounded-lg p-3 min-w-[300px] mt-3 transition-all duration-300 ${
              voiceStatus === "connected"
                ? "bg-green-50 dark:bg-green-500/10 border border-green-200 dark:border-green-500/20"
                : voiceStatus === "connecting"
                  ? "bg-yellow-50 dark:bg-yellow-500/10 border border-yellow-200 dark:border-yellow-500/20"
                  : "bg-gray-50 dark:bg-gray-500/10 border border-gray-200 dark:border-gray-500/20"
            }`}>
              <div className="flex items-center justify-center gap-3">
                {/* Connection Status Icon */}
                {voiceStatus === "connected" ? (
                  <motion.div
                    animate={{
                      opacity: [1, 0.6, 1],
                      scale: [1, 1.05, 1]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="flex items-center"
                  >
                    <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                  </motion.div>
                ) : voiceStatus === "connecting" ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                    className="flex items-center"
                  >
                    <Loader className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
                  </motion.div>
                ) : (
                  <WifiOff className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                )}

                {/* Connection Status Text */}
                <span className={`text-sm font-medium transition-colors duration-300 ${
                  voiceStatus === "connected"
                    ? "text-green-700 dark:text-green-300"
                    : voiceStatus === "connecting"
                      ? "text-yellow-700 dark:text-yellow-300"
                      : "text-gray-600 dark:text-gray-400"
                }`}>
                  {(() => {
                    const selectedVoice = selectedVoiceId ? getVoiceById(selectedVoiceId) : null;
                    const voiceName = selectedVoice ? selectedVoice.name : "Voice";

                    if (voiceStatus === "connected") {
                      return `${voiceName} AI Connected`;
                    } else if (voiceStatus === "connecting") {
                      return `Connecting to ${voiceName} AI...`;
                    } else {
                      return `${voiceName} AI Disconnected`;
                    }
                  })()}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Voice Selection Section */}
        <div className="flex-1 px-6 pb-8">
          <VoiceCarousel
            selectedVoiceId={selectedVoiceId}
            onVoiceSelect={handleVoiceSelection}
            isUpdatingVoice={isUpdatingVoice}
          />
        </div>
      </div>

      {/* Sliding Sidebar */}
      <AnimatePresence>
        {isSidebarOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 z-40"
              onClick={() => setIsSidebarOpen(false)}
            />

            {/* Sidebar */}
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="fixed right-0 top-0 h-full w-80 bg-white/95 dark:bg-black/95 backdrop-blur-sm border-l border-gray-200 dark:border-white/10 z-50 overflow-y-auto scrollbar-thin scrollbar-track-gray-100 dark:scrollbar-track-white/5 scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-500/20 transition-colors duration-300"
            >


              <div className="p-6">
                {/* Sidebar Header */}
                <div className="flex items-center mb-6">
                  <Settings className="w-5 h-5 text-gray-600 dark:text-gray-400 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Settings</h3>
                </div>

                {/* Connection Status */}
                <div className="mb-6 mt-3">
                  <h4 className="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-3">1. Castmate Status</h4>
                  <div className="flex items-center space-x-3">
                    {/* Close icon to the left of status box */}
                    <button
                      onClick={() => setIsSidebarOpen(false)}
                      className="w-8 h-8 flex items-center justify-center rounded-lg bg-gray-200 dark:bg-gray-600/20 border border-gray-300 dark:border-gray-500/30 hover:bg-gray-300 dark:hover:bg-gray-600/30 transition-colors flex-shrink-0"
                      title="Close sidebar"
                    >
                      <ChevronRight className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                    </button>

                    {/* Status box */}
                    <div className="bg-gray-100 dark:bg-black/60 rounded-lg p-3 border border-gray-200 dark:border-white/10 flex-1">
                      <div className="flex items-center space-x-2">
                        <span className={`w-2 h-2 rounded-full ${
                          voiceStatus === "connected"
                            ? "bg-green-400"
                            : voiceStatus === "connecting"
                              ? "bg-yellow-400"
                              : "bg-red-400"
                        }`}></span>
                        <span className={`text-sm ${
                          voiceStatus === "connected"
                            ? "text-green-400"
                            : voiceStatus === "connecting"
                              ? "text-yellow-400"
                              : "text-red-400"
                        }`}>
                          {(() => {
                            const selectedVoice = selectedVoiceId ? getVoiceById(selectedVoiceId) : null;
                            const voiceName = selectedVoice ? selectedVoice.name : "Voice";

                            if (voiceStatus === "connected") {
                              return `${voiceName} Connected`;
                            } else if (voiceStatus === "connecting") {
                              return `Connecting to ${voiceName}...`;
                            } else {
                              return `${voiceName} Disconnected`;
                            }
                          })()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
      <hr />
                {/* Agent Modality Settings */}
                <div className="mb-6 mt-3">
                  <h4 className="text-sm font-semibold text-gray-600 font dark:text-gray-400 mb-3">2 - Select Castmate Mode</h4>
                  <div className="space-y-3">

                    {/* Conversational Mode */}
                    <div className={`p-3 rounded-lg border transition-all duration-200 cursor-pointer ${
                      agentModality === 'conversational'
                        ? 'bg-blue-50 dark:bg-blue-500/10 border-blue-200 dark:border-blue-500/20'
                        : 'bg-gray-50 dark:bg-gray-700/30 border-gray-200 dark:border-gray-600/30 hover:bg-gray-100 dark:hover:bg-gray-700/50'
                    }`}
                    onClick={() => handleAgentModalityChange('conversational')}
                    >
                      <div className="flex items-start space-x-3">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAgentModalityChange('conversational');
                          }}
                          className={`mt-0.5 w-4 h-4 rounded-full border-2 flex items-center justify-center transition-colors ${
                            agentModality === 'conversational'
                              ? 'border-blue-500 bg-blue-500'
                              : 'border-gray-300 dark:border-gray-500 hover:border-blue-400'
                          }`}
                        >
                          {agentModality === 'conversational' && (
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                          )}
                        </button>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <MessageCircle className={`w-4 h-4 ${
                              agentModality === 'conversational'
                                ? 'text-blue-600 dark:text-blue-400'
                                : 'text-gray-500 dark:text-gray-400'
                            }`} />
                            <span className={`font-medium text-sm ${
                              agentModality === 'conversational'
                                ? 'text-blue-700 dark:text-blue-300'
                                : 'text-gray-700 dark:text-gray-300'
                            }`}>
                              Conversational
                            </span>
                            {agentModality === 'conversational' && (
                              <span className="text-xs bg-green-100 dark:bg-green-500/20 text-green-700 dark:text-green-300 px-2 py-0.5 rounded-full">
                                Active
                              </span>
                            )}
                          </div>
                          <p className={`text-xs ${
                            agentModality === 'conversational'
                              ? 'text-blue-600 dark:text-blue-400'
                              : 'text-gray-500 dark:text-gray-400'
                          }`}>
                            Fully interactive coaching with supportive feedback and performance enhancement
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Direct Mode */}
                    <div className={`p-3 rounded-lg border transition-all duration-200 cursor-pointer ${
                      agentModality === 'direct'
                        ? 'bg-orange-50 dark:bg-orange-500/10 border-orange-200 dark:border-orange-500/20'
                        : 'bg-gray-50 dark:bg-gray-700/30 border-gray-200 dark:border-gray-600/30 hover:bg-gray-100 dark:hover:bg-gray-700/50'
                    }`}
                    onClick={() => handleAgentModalityChange('direct')}
                    >
                      <div className="flex items-start space-x-3">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAgentModalityChange('direct');
                          }}
                          className={`mt-0.5 w-4 h-4 rounded-full border-2 flex items-center justify-center transition-colors ${
                            agentModality === 'direct'
                              ? 'border-orange-500 bg-orange-500'
                              : 'border-gray-300 dark:border-gray-500 hover:border-orange-400'
                          }`}
                        >
                          {agentModality === 'direct' && (
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                          )}
                        </button>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <Briefcase className={`w-4 h-4 ${
                              agentModality === 'direct'
                                ? 'text-orange-600 dark:text-orange-400'
                                : 'text-gray-500 dark:text-gray-400'
                            }`} />
                            <span className={`font-medium text-sm ${
                              agentModality === 'direct'
                                ? 'text-orange-700 dark:text-orange-300'
                                : 'text-gray-700 dark:text-gray-300'
                            }`}>
                              Direct Mode
                            </span>
                            {agentModality === 'direct' && (
                              <span className="text-xs bg-green-100 dark:bg-green-500/20 text-green-700 dark:text-green-300 px-2 py-0.5 rounded-full">
                                Active
                              </span>
                            )}
                          </div>
                          <p className={`text-xs ${
                            agentModality === 'direct'
                              ? 'text-orange-600 dark:text-orange-400'
                              : 'text-gray-500 dark:text-gray-400'
                          }`}>
                            Formal, minimal interaction focused on precision and technical accuracy
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Memorization Mode */}
                    <div className={`p-3 rounded-lg border transition-all duration-200 cursor-pointer ${
                      agentModality === 'memorization'
                        ? 'bg-purple-50 dark:bg-purple-500/10 border-purple-200 dark:border-purple-500/20'
                        : 'bg-gray-50 dark:bg-gray-700/30 border-gray-200 dark:border-gray-600/30 hover:bg-gray-100 dark:hover:bg-gray-700/50'
                    }`}
                    onClick={() => handleAgentModalityChange('memorization')}
                    >
                      <div className="flex items-start space-x-3">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAgentModalityChange('memorization');
                          }}
                          className={`mt-0.5 w-4 h-4 rounded-full border-2 flex items-center justify-center transition-colors ${
                            agentModality === 'memorization'
                              ? 'border-purple-500 bg-purple-500'
                              : 'border-gray-300 dark:border-gray-500 hover:border-purple-400'
                          }`}
                        >
                          {agentModality === 'memorization' && (
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                          )}
                        </button>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <Book className={`w-4 h-4 ${
                              agentModality === 'memorization'
                                ? 'text-purple-600 dark:text-purple-400'
                                : 'text-gray-500 dark:text-gray-400'
                            }`} />
                            <span className={`font-medium text-sm ${
                              agentModality === 'memorization'
                                ? 'text-purple-700 dark:text-purple-300'
                                : 'text-gray-700 dark:text-gray-300'
                            }`}>
                              Memorization
                            </span>
                            {agentModality === 'memorization' && (
                              <span className="text-xs bg-green-100 dark:bg-green-500/20 text-green-700 dark:text-green-300 px-2 py-0.5 rounded-full">
                                Active
                              </span>
                            )}
                          </div>
                          <p className={`text-xs ${
                            agentModality === 'memorization'
                              ? 'text-purple-600 dark:text-purple-400'
                              : 'text-gray-500 dark:text-gray-400'
                          }`}>
                            Specialized line runner for high-repetition memorization and recall training
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <hr />
                {/* Voice Controls */}
                <div className="mb-6 mt-3">
                  <h4 className="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-3">3 - Speak to Castmate</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                    <button
                      onClick={toggleMute}
                      disabled={voiceStatus !== "connected"}
                      className={`w-full p-2 sm:p-3 rounded-lg flex items-center justify-center space-x-1 sm:space-x-2 ${
                        voiceStatus === "connected"
                          ? (isMuted ? "bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600" : "bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-700")
                          : "bg-gray-200 dark:bg-gray-700 opacity-50 cursor-not-allowed"
                      } transition-colors`}
                      title={isMuted ? "Unmute" : "Mute"}
                    >
                      {isMuted ? (
                        <VolumeX className="h-8 w-8 text-gray-700 dark:text-white" />
                      ) : (
                        <Volume2 className="h-8 w-8 text-gray-700 dark:text-white" />
                      )}
                      <span className="text-xs sm:text-sm text-gray-700 dark:text-white">{isMuted ? "Unmute" : "Mute"}</span>
                    </button>

                    {voiceStatus === "connected" ? (
                      <button
                        onClick={handleEndConversation}
                        className="w-full flex items-center justify-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg shadow-lg shadow-red-600/20 transition-colors"
                      >
                        <MicOff className="h-8 w-8" />
                        <span className="text-xs sm:text-sm">Stop Rehearsing</span>
                      </button>
                    ) : (
                      <motion.button
                        onClick={handleStartConversation}
                        className={`w-full flex items-center justify-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 text-white rounded-lg shadow-lg transition-colors ${
                          !hasPermission || apiConfigStatus === 'invalid' || !session?.user?.email || apiConfigStatus === 'connecting'
                            ? "bg-gray-400 dark:bg-gray-700 opacity-50 cursor-not-allowed"
                            : shouldFlashStartButton
                              ? "bg-blue-600 hover:bg-blue-700 shadow-blue-600/20"
                              : "bg-gray-600 hover:bg-gray-700 shadow-gray-600/20"
                        }`}
                        disabled={!hasPermission || apiConfigStatus === 'invalid' || !session?.user?.email || apiConfigStatus === 'connecting'}
                        animate={shouldFlashStartButton ? {
                          scale: [1, 1.05, 1],
                          boxShadow: [
                            "0 10px 25px -5px rgba(59, 130, 246, 0.2), 0 10px 10px -5px rgba(59, 130, 246, 0.04)",
                            "0 20px 35px -5px rgba(59, 130, 246, 0.4), 0 15px 15px -5px rgba(59, 130, 246, 0.08)",
                            "0 10px 25px -5px rgba(59, 130, 246, 0.2), 0 10px 10px -5px rgba(59, 130, 246, 0.04)"
                          ]
                        } : {}}
                        transition={shouldFlashStartButton ? {
                          duration: 1.5,
                          repeat: Infinity,
                          ease: "easeInOut"
                        } : {}}
                      >
                        {apiConfigStatus === 'connecting' ? (
                          <Loader className="h-8 w-8 animate-spin" />
                        ) : (
                          <Mic className="h-8 w-8" />
                        )}
                        <span className="text-xs sm:text-sm">Rehearse</span>
                      </motion.button>
                    )}
                  </div>
                </div>

                <hr />

                {/* Script Tab Navigation */}
                 <h4 className="text-sm font-semibold text-gray-600 font dark:text-gray-400 mb-3 mt-3">4 - Script Tab</h4>
                <div className="mb-6 mt-3">
                  <button
                    onClick={() => {
                      console.log('[SCRIPT_NAV] User clicked to switch to Script tab');
                      onSwitchToScriptTab();
                    }}
                    className="w-full p-3 bg-blue-50 dark:bg-blue-500/10 border border-blue-200 dark:border-blue-500/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-500/20 transition-colors"
                  >
                    <div className="flex items-center justify-center space-x-2">
                      <Book className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                      <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                        Switch to Script Tab
                      </span>
                    </div>
                  </button>
                </div>



                {/* Voice Status Indicators */}
                {voiceStatus === "connected" && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">Status</h4>
                    <div className="bg-blue-50 dark:bg-black/60 rounded-lg p-3 border border-blue-200 dark:border-white/10">
                      {isSpeaking ? (
                        <span className="text-green-600 dark:text-green-400 text-sm">
                          {(() => {
                            const selectedVoice = selectedVoiceId ? getVoiceById(selectedVoiceId) : null;
                            const voiceName = selectedVoice ? selectedVoice.name : "AI Assistant";
                            return `${voiceName} is speaking...`;
                          })()}
                        </span>
                      ) : isListening ? (
                        <span className="text-gray-600 dark:text-gray-400 text-sm">Listening to your lines...</span>
                      ) : (
                        <span className="text-gray-600 dark:text-gray-400 text-sm">Ready to listen</span>
                      )}
                    </div>
                  </div>
                )}

                {/* Error Messages */}
                {voiceErrorMessage && (
                  <div className="mb-6">
                    <div className="text-red-600 dark:text-red-400 text-sm bg-red-50 dark:bg-red-500/10 p-3 rounded-lg border border-red-200 dark:border-red-500/20">
                      {voiceErrorMessage}
                      <button
                        onClick={() => setVoiceErrorMessage("")}
                        className="ml-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 underline"
                      >
                        Dismiss
                      </button>
                    </div>
                  </div>
                )}

                {!hasPermission && (
                  <div className="mb-6">
                    <div className="text-yellow-700 dark:text-yellow-400 text-sm bg-yellow-50 dark:bg-yellow-500/10 p-3 rounded-lg border border-yellow-200 dark:border-yellow-500/20">
                      Please allow microphone access to use rehearsal mode
                    </div>
                  </div>
                )}

                {/* API Configuration Issues */}
                {apiConfigStatus === 'invalid' && (
                  <div className="mb-6">
                    <div className="bg-red-50 dark:bg-red-500/10 border border-red-200 dark:border-red-500/20 rounded-lg p-3">
                      <h4 className="text-red-700 dark:text-red-400 font-medium mb-2">ElevenLabs API Configuration Issue</h4>
                      <p className="text-red-600 dark:text-red-300 text-xs mb-3">Voice features are unavailable due to API configuration issues.</p>

                      {detailedErrorInfo && (
                        <div className="mt-2 p-2 bg-red-50 dark:bg-black/60 rounded border border-red-200 dark:border-red-500/10 text-xs text-red-700 dark:text-red-300 font-mono overflow-x-auto custom-scrollbar">
                          {detailedErrorInfo}
                        </div>
                      )}

                      <div className="mt-3 text-xs text-gray-600 dark:text-gray-400">
                        <p>Recommended troubleshooting steps:</p>
                        <ol className="list-decimal ml-4 mt-1 space-y-1">
                          <li>Verify ElevenLabs API key is set in environment variables</li>
                          <li>Ensure the Agent ID is correct and active</li>
                          <li>Check network connectivity to ElevenLabs services</li>
                          <li>Verify account subscription status and API limits</li>
                        </ol>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Rehearsals;