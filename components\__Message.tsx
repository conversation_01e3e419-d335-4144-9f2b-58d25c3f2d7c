"use client"

import React, { useEffect, useState } from 'react';
import { Rotate<PERSON><PERSON>, Copy, Check, Download, FileText, ThumbsUp, ThumbsDown } from 'lucide-react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer } from 'recharts';
import { sendFollowUp } from './sendFollowUp';
import { dark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import EnhancedMarkdownContent from './EnhancedMarkdownContent';
import { jsPDF } from "jspdf";

interface VisualizationData {
  type: 'chart' | 'table';
  config: any;
  data: any[];
}

interface ExtendedMessageProps extends MessageProps {
  onListItemClick?: (text: string) => void;
  onFollowUpClick?: (text: string) => void;
  visualization?: VisualizationData;
}

type MessageProps = {
  message: {
    text: string;
    userId: string;
    createdAt?: {
      seconds: number;
      nanoseconds: number;
    };
  };
  session: {
    user: {
      email: string | null;
      image?: string | null;
    };
  };
  isStreaming: boolean;
  streamedData: string;
  isLastAIMessage: boolean;
  isLastUserMessage: boolean;
  hasAIResponded: boolean;
  onFollowUpClick?: (question: string) => void;
  onReprocess?: (message: string) => void;
};

const customTheme = {
  ...dark,
  'code[class*="language-"]': {
    ...dark['code[class*="language-"]'],
    color: '#cbd5e1',
    fontSize: '14px',
    fontFamily: '"Courier New", Courier, monospace',
    textShadow: 'none'
  },
  'pre[class*="language-"]': {
    ...dark['pre[class*="language-"]'],
    background: 'none',
    border: 'none',
    boxShadow: 'none',
    textShadow: 'none'
  },
  'comment': {
    color: '#6b7280',
    fontStyle: 'italic'
  },
  'string': {
    color: '#fbbf24'
  },
  'keyword': {
    color: '#c084fc',
    fontWeight: 'bold'
  },
  'function': {
    color: '#60a5fa'
  },
  'number': {
    color: '#f97316'
  },
  'operator': {
    color: '#94a3b8'
  },
  'punctuation': {
    color: '#94a3b8'
  },
  'class-name': {
    color: '#22d3ee'
  },
  'builtin': {
    color: '#a78bfa'
  },
  'variable': {
    color: '#cbd5e1'
  },
  'property': {
    color: '#93c5fd'
  }
};

const VisualizationRenderer: React.FC<{ data: VisualizationData }> = ({ data }) => {
  if (data.type === 'chart') {
    return (
      <div className="w-full h-64 mt-4 bg-ike-message-bg p-4 rounded-lg">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data.data}>
            <CartesianGrid strokeDasharray="3 3" stroke="#444" />
            <XAxis
              dataKey={data.config.xAxis}
              stroke="#999"
            />
            <YAxis stroke="#999" />
            <Tooltip
              contentStyle={{
                backgroundColor: '#1a1a1a',
                border: '1px solid #333',
                borderRadius: '4px'
              }}
            />
            <Legend />
            {data.config.lines?.map((line: string, index: number) => (
              <Line
                key={line}
                type="monotone"
                dataKey={line}
                stroke={`hsl(${(index * 60) % 360}, 70%, 50%)`}
                dot={false}
              />
            ))}
          </LineChart>
        </ResponsiveContainer>
      </div>
    );
  }

  if (data.type === 'table') {
    return (
      <div className="w-full mt-4 overflow-x-auto">
        <table className="min-w-full bg-ike-message-bg rounded-lg">
          <thead>
            <tr>
              {Object.keys(data.data[0]).map((header) => (
                <th key={header} className="px-4 py-2 text-left text-sm text-amber-400 border-b border-gray-700">
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.data.map((row, i) => (
              <tr key={i}>
                {Object.values(row).map((cell: any, j) => (
                  <td key={j} className="px-4 py-2 text-sm text-gray-300 border-b border-gray-700">
                    {typeof cell === 'number' ? cell.toLocaleString() : cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  }

  return null;
};

export default function Message({
  message,
  session,
  isStreaming,
  streamedData,
  isLastAIMessage,
  isLastUserMessage,
  hasAIResponded,
  onFollowUpClick,
  onReprocess,
  onListItemClick,
  visualization,
}: ExtendedMessageProps) {
  const [displayedText, setDisplayedText] = useState(message?.text ?? '');
  const [showFollowUpQuestions, setShowFollowUpQuestions] = useState(isLastAIMessage);
  const [followUpQuestions, setFollowUpQuestions] = useState<string[]>([]);
  const [lastUserMessage, setLastUserMessage] = useState<string>('');
  const [isStreamClosed, setIsStreamClosed] = useState(false);
  const [hasFetched, setHasFetched] = useState(false);
  const [isFetchingFollowUp, setIsFetchingFollowUp] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [isPdfCopied, setIsPdfCopied] = useState(false);
  const [isCsvCopied, setIsCsvCopied] = useState(false);
  const [copiedCodeBlock, setCopiedCodeBlock] = useState<string | null>(null);
  const [isLiked, setIsLiked] = useState(false);
  const [isDisliked, setIsDisliked] = useState(false);

  const isUser = message.userId === session.user.email;
  const isAI = message.userId === 'ai-response';
  const aiImage = process.env.NEXT_PUBLIC_AI_IMAGE || '/favicon_message.png';
  const userImage = session.user.image;

  useEffect(() => {
    if (isAI && isStreaming && streamedData && !isStreamClosed) {
      try {
        const parsedData = JSON.parse(streamedData);
        if (parsedData.type === 'visualization') {
          setDisplayedText('');
        } else {
          setDisplayedText((prevText) => {
            if (prevText.includes('<span class="dot">')) {
              return streamedData;
            }
            return prevText + streamedData.replace(prevText, '');
          });
        }
      } catch {
        setDisplayedText((prevText) => {
          if (prevText.includes('<span class="dot">')) {
            return streamedData;
          }
          return prevText + streamedData.replace(prevText, '');
        });
      }
    }

    if (isAI && !isStreaming && streamedData && !isStreamClosed) {
      setIsStreamClosed(true);
    }
  }, [isAI, isStreaming, streamedData, isStreamClosed]);

  useEffect(() => {
    if (isUser && isLastUserMessage) {
      setLastUserMessage(message.text);
    }
  }, [isUser, isLastUserMessage, message.text]);

  useEffect(() => {
    if (
      isAI &&
      isLastAIMessage &&
      isStreamClosed &&
      !isStreaming &&
      !hasFetched &&
      !displayedText.includes('<span class="dot">')
    ) {
      const combinedData = `${lastUserMessage} ${displayedText}`;

      const generateQuestions = async () => {
        try {
          setIsFetchingFollowUp(true);
          const result = await sendFollowUp(combinedData);

          if (result && result.json) {
            setFollowUpQuestions([
              result.json.followup1 ?? 'No follow-up question 1',
              result.json.followup2 ?? 'No follow-up question 2',
              result.json.followup3 ?? 'No follow-up question 3',
            ]);
          } else {
            setFollowUpQuestions(['No follow-up questions available.']);
          }
          setHasFetched(true);
        } catch (error) {
          console.error('Error generating follow-up questions:', error);
        } finally {
          setIsFetchingFollowUp(false);
        }
      };

      generateQuestions();
    }
  }, [isAI, isLastAIMessage, isStreamClosed, isStreaming, hasFetched, displayedText, lastUserMessage]);

  const handleFollowUpClick = (question: string) => {
    setShowFollowUpQuestions(false);
    onFollowUpClick?.(question);
  };

  const handleReprocess = () => {
    onReprocess?.(message.text);
  };

  const handleCopyText = async () => {
    try {
      await navigator.clipboard.writeText(isAI ? displayedText : message.text);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const handleCopyCodeBlock = async (codeContent: string) => {
    try {
      await navigator.clipboard.writeText(codeContent);
      setCopiedCodeBlock(codeContent);
      setTimeout(() => setCopiedCodeBlock(null), 2000);
    } catch (err) {
      console.error('Failed to copy code block: ', err);
    }
  };

  const handlePdfExport = () => {
    const doc = new jsPDF();

    doc.setFontSize(16);
    doc.text("Chat Export", 20, 20);

    doc.setFontSize(12);
    doc.text(`Date: ${messageDate}`, 20, 40);
    doc.text(`From: ${isAI ? 'AI Assistant' : 'User'}`, 20, 50);

    doc.setFontSize(14);
    doc.text("Message Content:", 20, 70);
    doc.setFontSize(12);
    const contentText = doc.splitTextToSize(isAI ? displayedText : message.text, 170);
    doc.text(contentText, 20, 80);

    doc.save(`chat-export-${messageDate}.pdf`);
    setIsPdfCopied(true);
    setTimeout(() => setIsPdfCopied(false), 2000);
  };

  const handleCsvExport = () => {
    const csvData = [
      ["Date", "From", "Message"],
      [
        messageDate,
        isAI ? 'AI Assistant' : 'User',
        isAI ? displayedText : message.text
      ]
    ];

    const csvString = csvData.map(row => row.map(cell =>
      `"${cell.replace(/"/g, '""')}"`
    ).join(",")).join("\n");

    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `chat-export-${messageDate}.csv`;
    link.click();
    setIsCsvCopied(true);
    setTimeout(() => setIsCsvCopied(false), 2000);
  };

  const handleLike = () => {
    setIsLiked(true);
    setIsDisliked(false);
  };

  const handleDislike = () => {
    setIsDisliked(true);
    setIsLiked(false);
    onReprocess?.('ok');
  };

  const isDancingDots = displayedText.includes('<span class="dot">');
  const messageDate = message.createdAt
    ? new Date(message.createdAt.seconds * 1000).toLocaleString()
    : 'Unknown date';

  return (
    <div className="flex flex-col items-center justify-center w-full">
      <div className="flex justify-center mb-4 w-full max-w-7xl">
        <div className="w-full md:w-4/5 lg:w-3/4 xl:w-2/3">
          {isAI ? (
            <div className="flex items-start">
              <div className={`p-3 flex-grow overflow-x-auto ${isDancingDots ? 'bg-ike-message-bg' : 'bg-ike-purple_b bg-opacity-75 rounded-lg'}`}>
                <div className="flex items-center mb-2">
                  <img
                    src={aiImage}
                    alt="AI"
                    className={`w-6 h-6 rounded-full mr-2 ${isStreaming ? 'animate-pulse' : ''}`}
                  />
                  <span className="text-xs text-gray-400">iKe</span>
                </div>
                {isDancingDots ? (
                  <div className="flex">
                    <span className="dots"></span>
                    <span className="dots"></span>
                    <span className="dots"></span>
                  </div>
                ) : (
                  <>
                    <EnhancedMarkdownContent
                      content={displayedText}
                      onItemClick={(enrichedText) => {
                        setShowFollowUpQuestions(false);
                        onFollowUpClick?.(enrichedText);
                      }}
                      customTheme={customTheme}
                      onCopyCode={handleCopyCodeBlock}
                    />
                    {visualization && <VisualizationRenderer data={visualization} />}
                  </>
                )}
                {!isDancingDots && (
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-xs text-gray-400">{messageDate}</span>
                    <div className="flex space-x-2">
                      <button
                        onClick={handleLike}
                        className={`p-1 rounded-full ${isLiked ? 'bg-green-500' : 'bg-gray-600 hover:bg-green-700'} transition-colors duration-200`}
                        title="Like"
>
                        <ThumbsUp className="w-4 h-4 text-white" />
                      </button>
                      <button
                        onClick={handleDislike}
                        className={`p-1 rounded-full ${isDisliked ? 'bg-red-500' : 'bg-gray-600 hover:bg-red-700'} transition-colors duration-200`}
                        title="Dislike"
                      >
                        <ThumbsDown className="w-4 h-4 text-white" />
                      </button>
                    </div>
                  </div>
                )}
                {isLastAIMessage && !isStreaming && showFollowUpQuestions && (
                  <div className="mt-4">
                    {isFetchingFollowUp ? (
                      <p className="animate-pulse text-amber-300 text-sm">Loading follow-up questions...</p>
                    ) : followUpQuestions.length > 0 ? (
                      <>
                        <p className="text-amber-300 mb-2 text-xs mt-6">Follow-Up Questions loaded</p>
                        {followUpQuestions.map((question, index) => (
                          <button
                            key={index}
                            className="w-full text-left text-sm text-amber-600 bg-ike-purple hover:bg-ike-dark-purple hover:text-amber-100 px-3 py-2 rounded mb-2"
                            onClick={() => handleFollowUpClick(question)}
                          >
                            {question}
                          </button>
                        ))}
                      </>
                    ) : null}
                  </div>
                )}
              </div>
              {!isStreaming && (
                <div className="flex flex-col ml-2 mt-2 space-y-2">
                  <button
                    onClick={handleCopyText}
                    className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-500 transition-colors duration-200"
                    title="Copy message"
                  >
                    {isCopied ? (
                      <Check className="w-3 h-3 text-white" />
                    ) : (
                      <Copy className="w-3 h-3 text-white" />
                    )}
                  </button>
                  <button
                    onClick={handlePdfExport}
                    className="w-5 h-5 bg-indigo-800 rounded-full flex items-center justify-center hover:bg-indigo-700 transition-colors duration-200"
                    title="Export as PDF"
                  >
                    {isPdfCopied ? (
                      <Check className="w-3 h-3 text-white" />
                    ) : (
                      <Download className="w-3 h-3 text-white" />
                    )}
                  </button>
                  <button
                    onClick={handleCsvExport}
                    className="w-5 h-5 bg-pink-500 rounded-full flex items-center justify-center hover:bg-pink-400 transition-colors duration-200"
                    title="Export as CSV"
                  >
                    {isCsvCopied ? (
                      <Check className="w-3 h-3 text-white" />
                    ) : (
                      <FileText className="w-3 h-3 text-white" />
                    )}
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-start">
              <div className="bg-gradient-to-r from-ike-dark-purple to-ike-purple rounded-lg p-2 mt-5 flex-grow">
                <div className="flex flex-col justify-between items-start mb-2">
                  <div className="flex items-center mb-2">
                    {userImage ? (
                      <img
                        src={userImage}
                        alt="User"
                        className="w-6 h-6 rounded-full mr-2"
                      />
                    ) : (
                      <span className="text-xs text-gray-300 mr-2">You</span>
                    )}
                  </div>
                  <p className="text-white whitespace-pre-wrap">{message.text}</p>
                </div>
                <span className="text-xs text-gray-500">{messageDate}</span>
              </div>
              {!isStreaming && (
                <div className="flex flex-col ml-2 mt-7 space-y-2">
                  <button
                    onClick={handleReprocess}
                    className="w-5 h-5 bg-amber-600 rounded-full flex items-center justify-center hover:bg-amber-500 transition-colors duration-200"
                    title="Resend message"
                  >
                    <RotateCw className="w-3 h-3 text-white" />
                  </button>
                  <button
                    onClick={handleCopyText}
                    className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-500 transition-colors duration-200"
                    title="Copy message"
                  >
                    {isCopied ? (
                      <Check className="w-3 h-3 text-white" />
                    ) : (
                      <Copy className="w-3 h-3 text-white" />
                    )}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

