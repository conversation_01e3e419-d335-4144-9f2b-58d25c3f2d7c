"use client";

import React, { useEffect, useState } from "react";
import {
  RotateCw,
  Copy,
  Check,
  Download,
  FileText,
  ThumbsUp,
  ThumbsDown
} from "lucide-react";
import { sendFollowUp } from "./sendFollowUp";
import { dark } from "react-syntax-highlighter/dist/esm/styles/prism";
import EnhancedMarkdownContent from "./EnhancedMarkdownContent";
import { jsPDF } from "jspdf";
import VisualizationPopup from "./Visualization/VisualizationPopup";

// Visualization types
type VisualizationConfig = {
  type: string; // e.g. "stacked-bar"
  stacked?: boolean;
  xAxis: string;
  yAxis?: string;
  series: string[];
  colors?: string[];
};

interface VisualizationData {
  type: "visualization";
  content: {
    type: "chart" | "table";
    config: VisualizationConfig;
    data: any[];
    metadata?: {
      type: "time-series" | "categorical" | "distribution" | "comparison";
      metrics: string[];
      timeUnit?: "day" | "month" | "quarter" | "year";
    };
    confidence?: number;
    reasoning?: string;
  };
}

interface MessageProps {
  message: {
    text: string;
    userId: string;
    createdAt?: {
      seconds: number;
      nanoseconds: number;
    };
  };
  session: {
    user: {
      email: string | null;
      image?: string | null;
    };
  };
  isStreaming: boolean;
  streamedData: string;
  isLastAIMessage: boolean;
  isLastUserMessage: boolean;
  hasAIResponded: boolean;
  onFollowUpClick?: (question: string) => void;
  onReprocess?: (message: string) => void;

  // Potential link /visualization?data=... from ChatArea
  visualizationUrl?: string;
  onListItemClick?: (text: string) => void;
}

const customTheme = {
  ...dark,
  'code[class*="language-"]': {
    ...dark['code[class*="language-"]'],
    color: '#cbd5e1',
    fontSize: '14px',
    fontFamily: 'monospace',
    textShadow: 'none'
  },
  'pre[class*="language-"]': {
    ...dark['pre[class*="language-"]'],
    background: 'none',
    border: 'none',
    boxShadow: 'none',
    textShadow: 'none'
  }
};

export default function Message({
  message,
  session,
  isStreaming,
  streamedData,
  isLastAIMessage,
  isLastUserMessage,
  hasAIResponded,
  onFollowUpClick,
  onReprocess,
  onListItemClick,
  visualizationUrl
}: MessageProps) {
  const [displayedText, setDisplayedText] = useState(message?.text ?? "");
  const [showFollowUpQuestions, setShowFollowUpQuestions] = useState(isLastAIMessage);
  const [followUpQuestions, setFollowUpQuestions] = useState<string[]>([]);
  const [lastUserMessage, setLastUserMessage] = useState<string>("");
  const [isStreamClosed, setIsStreamClosed] = useState(false);
  const [hasFetched, setHasFetched] = useState(false);
  const [isFetchingFollowUp, setIsFetchingFollowUp] = useState(false);

  const [isCopied, setIsCopied] = useState(false);
  const [isPdfCopied, setIsPdfCopied] = useState(false);
  const [isCsvCopied, setIsCsvCopied] = useState(false);
  const [copiedCodeBlock, setCopiedCodeBlock] = useState<string | null>(null);

  const [isLiked, setIsLiked] = useState(false);
  const [isDisliked, setIsDisliked] = useState(false);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [decodedVisualization, setDecodedVisualization] = useState<VisualizationData | null>(null);

  const isUser = message.userId === session.user.email;
  const isAI = message.userId === "ai-response";
  const aiImage = process.env.NEXT_PUBLIC_AI_IMAGE || "/favicon_message.png";
  const userImage = session.user.image;

  // Show partial text if streaming
  useEffect(() => {
    if (isStreaming) {
      setDisplayedText(streamedData);
    } else {
      setDisplayedText(message.text);
    }
  }, [isStreaming, streamedData, message.text]);

  // Track last user message
  useEffect(() => {
    if (isUser && isLastUserMessage) {
      setLastUserMessage(message.text);
    }
  }, [isUser, isLastUserMessage, message.text]);

  // If we got a /visualization? link, decode it
  useEffect(() => {
    if (visualizationUrl) {
      try {
        // e.g. "/visualization?data=%7B%22charttype%22%3A..."
        const parts = visualizationUrl.split("?data=");
        if (parts.length > 1) {
          const encoded = parts[1];
          const decoded = decodeURIComponent(encoded);
          const parsed = JSON.parse(decoded);
          // e.g. { charttype: "visualization", content: {...} }
          if (parsed.charttype === "visualization") {
            setDecodedVisualization({
              type: "visualization",
              content: parsed.content
            });
          }
        }
      } catch (err) {
        console.error("Failed to decode visualizationUrl:", err);
      }
    }
  }, [visualizationUrl]);

  // Generate follow-up questions once stream is done
  useEffect(() => {
    if (
      isAI &&
      isLastAIMessage &&
      !isStreaming &&
      !isStreamClosed &&
      !hasFetched &&
      !displayedText.includes("<span class=\"dot\">")
    ) {
      setIsStreamClosed(true);
      const combinedData = `${lastUserMessage} ${displayedText}`;

      const generateQuestions = async () => {
        try {
          setIsFetchingFollowUp(true);
          const result = await sendFollowUp(combinedData);
          if (result && result.json) {
            setFollowUpQuestions([
              result.json.followup1 ?? "No follow-up question 1",
              result.json.followup2 ?? "No follow-up question 2",
              result.json.followup3 ?? "No follow-up question 3",
            ]);
          } else {
            setFollowUpQuestions(["No follow-up questions available."]);
          }
          setHasFetched(true);
        } catch (error) {
          console.error("Error generating follow-up questions:", error);
        } finally {
          setIsFetchingFollowUp(false);
        }
      };
      generateQuestions();
    }
  }, [
    isAI,
    isLastAIMessage,
    isStreaming,
    isStreamClosed,
    hasFetched,
    displayedText,
    lastUserMessage
  ]);

  // Handlers
  const handleFollowUpClick = (question: string) => {
    setShowFollowUpQuestions(false);
    onFollowUpClick?.(question);
  };

  const handleReprocess = () => {
    onReprocess?.(message.text);
  };

  const handleCopyText = async () => {
    try {
      await navigator.clipboard.writeText(isAI ? displayedText : message.text);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy text:", err);
    }
  };

  const handleCopyCodeBlock = async (codeContent: string) => {
    try {
      await navigator.clipboard.writeText(codeContent);
      setCopiedCodeBlock(codeContent);
      setTimeout(() => setCopiedCodeBlock(null), 2000);
    } catch (err) {
      console.error("Failed to copy code block:", err);
    }
  };

  const handlePdfExport = () => {
    const doc = new jsPDF();
    const messageDate = message.createdAt
      ? new Date(message.createdAt.seconds * 1000).toLocaleString()
      : "Unknown date";

    doc.setFontSize(16);
    doc.text("Chat Export", 20, 20);

    doc.setFontSize(12);
    doc.text(`Date: ${messageDate}`, 20, 40);
    doc.text(`From: ${isAI ? "AI Assistant" : "User"}`, 20, 50);

    doc.setFontSize(14);
    doc.text("Message Content:", 20, 70);
    doc.setFontSize(12);
    const contentText = doc.splitTextToSize(isAI ? displayedText : message.text, 170);
    doc.text(contentText, 20, 80);

    doc.save(`chat-export-${messageDate}.pdf`);
    setIsPdfCopied(true);
    setTimeout(() => setIsPdfCopied(false), 2000);
  };

  const handleCsvExport = () => {
    const messageDate = message.createdAt
      ? new Date(message.createdAt.seconds * 1000).toLocaleString()
      : "Unknown date";

    const csvData = [
      ["Date", "From", "Message"],
      [messageDate, isAI ? "AI Assistant" : "User", isAI ? displayedText : message.text]
    ];

    const csvString = csvData
      .map((row) =>
        row.map((cell) => `"${cell.replace(/"/g, '""')}"`).join(",")
      )
      .join("\n");

    const blob = new Blob([csvString], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = `chat-export-${messageDate}.csv`;
    link.click();
    setIsCsvCopied(true);
    setTimeout(() => setIsCsvCopied(false), 2000);
  };

  const handleLike = () => {
    setIsLiked(true);
    setIsDisliked(false);
  };

  const handleDislike = () => {
    setIsDisliked(true);
    setIsLiked(false);
    onReprocess?.("ok");
  };

  const isDancingDots = displayedText.includes('<span class="dot">');
  const messageDate = message.createdAt
    ? new Date(message.createdAt.seconds * 1000).toLocaleString()
    : "Unknown date";

  // Render a link or inline chart from decodedVisualization
  const renderDecodedChartLinkOrInline = () => {
    if (!decodedVisualization) return null;
    return (
      <div className="mt-4">
        <button
          onClick={() => setIsPopupOpen(true)}
          className="text-blue-400 underline hover:text-blue-300"
        >
          View chart inline
        </button>
        <br />
        {visualizationUrl && (
          <a
            href={visualizationUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="text-amber-400 hover:text-amber-300 underline"
          >
            Open chart in new tab
          </a>
        )}
      </div>
    );
  };

  return (
    <div className="flex flex-col items-center justify-center w-full">
      <div className="flex justify-center mb-4 w-full max-w-7xl">
        <div className="w-full md:w-4/5 lg:w-3/4 xl:w-2/3">
          {isAI ? (
            <div className="flex items-start">
              <div
                className={`p-3 flex-grow overflow-x-auto ${
                  isDancingDots ? "bg-ike-message-bg" : "bg-ike-purple_b bg-opacity-75 rounded-lg"
                }`}
              >
                <div className="flex items-center mb-2">
                  <img
                    src={aiImage}
                    alt="AI"
                    className={`w-6 h-6 rounded-full mr-2 ${isStreaming ? "animate-pulse" : ""}`}
                  />
                  <span className="text-xs text-gray-400">iKe</span>
                </div>
                {isDancingDots ? (
                  <div className="flex">
                    <span className="dots"></span>
                    <span className="dots"></span>
                    <span className="dots"></span>
                  </div>
                ) : (
                  <>
                    <EnhancedMarkdownContent
                      content={displayedText}
                      onItemClick={(enrichedText) => {
                        setShowFollowUpQuestions(false);
                        onFollowUpClick?.(enrichedText);
                      }}
                      customTheme={customTheme}
                      onCopyCode={handleCopyCodeBlock}
                    />
                    {renderDecodedChartLinkOrInline()}
                    {decodedVisualization && (
                      <VisualizationPopup
                        isOpen={isPopupOpen}
                        onClose={() => setIsPopupOpen(false)}
                        visualizationData={decodedVisualization.content}
                      />
                    )}
                  </>
                )}

                {!isDancingDots && (
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-xs text-gray-400">{messageDate}</span>
                    <div className="flex space-x-2">
                      <button
                        onClick={handleLike}
                        className={`p-1 rounded-full ${
                          isLiked ? "bg-green-500" : "bg-gray-600 hover:bg-green-700"
                        } transition-colors duration-200`}
                        title="Like"
                      >
                        <ThumbsUp className="w-4 h-4 text-white" />
                      </button>
                      <button
                        onClick={handleDislike}
                        className={`p-1 rounded-full ${
                          isDisliked ? "bg-red-500" : "bg-gray-600 hover:bg-red-700"
                        } transition-colors duration-200`}
                        title="Dislike"
                      >
                        <ThumbsDown className="w-4 h-4 text-white" />
                      </button>
                    </div>
                  </div>
                )}

                {isLastAIMessage && !isStreaming && showFollowUpQuestions && (
                  <div className="mt-4">
                    {isFetchingFollowUp ? (
                      <p className="animate-pulse text-amber-300 text-sm">
                        Loading follow-up questions...
                      </p>
                    ) : followUpQuestions.length > 0 ? (
                      <>
                        <p className="text-amber-300 mb-2 text-xs mt-6">Follow-Up Questions loaded</p>
                        {followUpQuestions.map((question, index) => (
                          <button
                            key={index}
                            className="w-full text-left text-sm text-amber-600 bg-ike-purple hover:bg-ike-dark-purple hover:text-amber-100 px-3 py-2 rounded mb-2"
                            onClick={() => handleFollowUpClick(question)}
                          >
                            {question}
                          </button>
                        ))}
                      </>
                    ) : null}
                  </div>
                )}
              </div>

              {!isStreaming && (
                <div className="flex flex-col ml-2 mt-2 space-y-2">
                  <button
                    onClick={handleCopyText}
                    className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-500 transition-colors duration-200"
                    title="Copy message"
                  >
                    {isCopied ? <Check className="w-3 h-3 text-white" /> : <Copy className="w-3 h-3 text-white" />}
                  </button>
                  <button
                    onClick={handlePdfExport}
                    className="w-5 h-5 bg-indigo-800 rounded-full flex items-center justify-center hover:bg-indigo-700 transition-colors duration-200"
                    title="Export as PDF"
                  >
                    {isPdfCopied ? <Check className="w-3 h-3 text-white" /> : <Download className="w-3 h-3 text-white" />}
                  </button>
                  <button
                    onClick={handleCsvExport}
                    className="w-5 h-5 bg-pink-500 rounded-full flex items-center justify-center hover:bg-pink-400 transition-colors duration-200"
                    title="Export as CSV"
                  >
                    {isCsvCopied ? <Check className="w-3 h-3 text-white" /> : <FileText className="w-3 h-3 text-white" />}
                  </button>
                </div>
              )}
            </div>
          ) : (
            // USER message
            <div className="flex items-start">
              <div className="bg-gradient-to-r from-ike-dark-purple to-ike-purple rounded-lg p-2 mt-5 flex-grow">
                <div className="flex flex-col justify-between items-start mb-2">
                  <div className="flex items-center mb-2">
                    {userImage ? (
                      <img src={userImage} alt="User" className="w-6 h-6 rounded-full mr-2" />
                    ) : (
                      <span className="text-xs text-gray-300 mr-2">You</span>
                    )}
                  </div>
                  <p className="text-white whitespace-pre-wrap">{message.text}</p>
                </div>
                <span className="text-xs text-gray-500">
                  {messageDate}
                </span>
              </div>
              {!isStreaming && (
                <div className="flex flex-col ml-2 mt-7 space-y-2">
                  <button
                    onClick={handleReprocess}
                    className="w-5 h-5 bg-amber-600 rounded-full flex items-center justify-center hover:bg-amber-500 transition-colors duration-200"
                    title="Resend message"
                  >
                    <RotateCw className="w-3 h-3 text-white" />
                  </button>
                  <button
                    onClick={handleCopyText}
                    className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-500 transition-colors duration-200"
                    title="Copy message"
                  >
                    {isCopied ? <Check className="w-3 h-3 text-white" /> : <Copy className="w-3 h-3 text-white" />}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
