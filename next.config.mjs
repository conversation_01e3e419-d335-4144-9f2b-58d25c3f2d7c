import NodePolyfillPlugin from 'node-polyfill-webpack-plugin';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  webpack: (config, { isServer, webpack }) => {
    // Enable WebAssembly and layers
    config.experiments = {
      asyncWebAssembly: true,
      layers: true,
    };

    // Add rule to handle `.wasm` files as WebAssembly modules
    config.module.rules.push({
      test: /\.wasm$/,
      type: "webassembly/async",
    });

    // Add Node polyfill plugin
    config.plugins.push(new NodePolyfillPlugin());

    // Alias 'node:util' to 'util' to handle the scheme issue
    config.resolve.alias = {
      ...config.resolve.alias,
      'node:util': 'util',
    };

    // Handle @langchain/community module
    config.module.rules.push({
      test: /\.m?js$/,
      type: "javascript/auto",
      resolve: {
        fullySpecified: false,
      },
    });

    // Suppress specific Webpack warnings
    config.ignoreWarnings = [
      (warning) =>
        warning.message.includes("Critical dependency: the request of a dependency is an expression"),
    ];

    // Handle PDF-related issues
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };
    }

    // Ignore the specific problematic file
    config.plugins.push(
      new webpack.IgnorePlugin({
        resourceRegExp: /test\/data\/05-versions-space\.pdf$/,
      })
    );

    // Resolve paths for better compatibility
    config.resolve.modules.push(path.resolve(__dirname, 'node_modules'));

    return config;
  },
  // Add custom headers for security
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
    ];
  },
  // Optimize images
  images: {
    remotePatterns: process.env.NODE_ENV === 'production'
      ? [
          { protocol: 'https', hostname: 'cheri-ai.com' },
          { protocol: 'https', hostname: 'www.cheri-ai.com' },
          { protocol: 'https', hostname: 'ike-ai.com' },
          { protocol: 'https', hostname: 'www.ike-ai.com' },
          { protocol: 'https', hostname: 'lh3.googleusercontent.com' },
        ]
      : [
          { protocol: 'http', hostname: 'localhost' },
          { protocol: 'https', hostname: 'lh3.googleusercontent.com' },
        ],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  // Enable SWC minification
  swcMinify: true,
};

export default nextConfig;