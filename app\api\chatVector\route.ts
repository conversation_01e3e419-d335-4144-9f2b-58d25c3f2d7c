// src/app/api/chatVector/route.ts

import { NextResponse } from 'next/server';
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";
import { createVectorService } from 'components/ChatExplorer/ChatVectorService';
import { ChatData } from 'types';
import type {
  ChatVectorContext,
  VectorServiceError,
  ChatVectorRequest
} from 'components/ChatExplorer/ChatVectorTypes';

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json() as ChatVectorRequest;
    const { userId, category, chatId, userMessage, aiMessage } = body;

    // Validate required fields
    if (!userId || !category || !chatId || !userMessage || !aiMessage) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Create context object
    const context: ChatVectorContext = {
      selectedDocId: chatId,
      documentNamespace: chatId,
      category
    };

    try {
      const vectorService = await createVectorService(session.user.email);
      const result = await vectorService.saveChatInteraction(
        userMessage as ChatData,
        aiMessage as ChatData,
        context
      );

      return NextResponse.json({
        success: true,
        vectorId: result.vectorId,
        namespace: result.namespace,
        status: result.status
      });
    } catch (error) {
      console.error('Vector service error:', error);
      throw error;
    }

  } catch (error) {
    console.error('API route error:', error);
    const status = (error as VectorServiceError).status || 500;
    return NextResponse.json(
      { success: false, error: (error as Error).message },
      { status }
    );
  }
}