// SignOutModal.tsx
import React from 'react';
import { LogOut } from 'lucide-react';

interface SignOutModalProps {
  onSignOut: () => void;
  onCancel: () => void;
}

const SignOutModal: React.FC<SignOutModalProps> = ({
  onSignOut,
  onCancel,
}) => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white text-gray-700 p-6 rounded-lg shadow-lg max-w-sm w-full mx-4">
        <h2 className="text-xl font-bold mb-4">Sign Out</h2>
        <p className="mb-6 text-gray-600">Are you sure you want to sign out?</p>
        <div className="flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            onClick={onSignOut}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors duration-200 flex items-center"
          >
            <LogOut className="mr-2 h-4 w-4" />
            Sign Out
          </button>
        </div>
      </div>
    </div>
  );
};

export default SignOutModal;