import { FormattedScript } from '../tools/scriptFormatter';

/**
 * Converts a FormattedScript object to markdown format
 * that can be rendered by ScriptMarkdownContent.tsx
 */
export function convertFormattedScriptToMarkdown(formattedScript: FormattedScript): string {
  const { metadata, lines } = formattedScript;
  
  let markdown = '';
  
  // Add title
  if (metadata.title) {
    markdown += `# ${metadata.title}\n\n`;
  }
  
  // Add author
  if (metadata.author) {
    markdown += `### By ${metadata.author}\n\n`;
  }
  
  // Add characters list
  if (metadata.characters && metadata.characters.length > 0) {
    markdown += `## Characters\n\n`;
    metadata.characters.forEach(character => {
      markdown += `- ${character}\n`;
    });
    markdown += '\n';
  }
  
  // Add summary
  if (metadata.summary) {
    markdown += `## Summary\n\n${metadata.summary}\n\n`;
  }
  
  // Add script content
  markdown += `## Script\n\n`;
  
  let currentCharacter = '';
  
  lines.forEach(line => {
    // Only add character name if it's different from the previous line
    if (line.character !== currentCharacter) {
      currentCharacter = line.character;
      markdown += `## ${line.character}\n\n`;
    }
    
    // Add dialogue text
    if (line.text) {
      markdown += `${line.text}\n\n`;
    }
    
    // Add notes/stage directions as italics
    if (line.notes) {
      markdown += `*${line.notes}*\n\n`;
    }
  });
  
  return markdown;
}

/**
 * Alternative converter that includes line numbers
 */
export function convertFormattedScriptToMarkdownWithLineNumbers(formattedScript: FormattedScript): string {
  const { metadata, lines } = formattedScript;
  
  let markdown = '';
  
  // Add title
  if (metadata.title) {
    markdown += `# ${metadata.title}\n\n`;
  }
  
  // Add author
  if (metadata.author) {
    markdown += `### By ${metadata.author}\n\n`;
  }
  
  // Add characters list
  if (metadata.characters && metadata.characters.length > 0) {
    markdown += `## Characters\n\n`;
    metadata.characters.forEach(character => {
      markdown += `- ${character}\n`;
    });
    markdown += '\n';
  }
  
  // Add summary
  if (metadata.summary) {
    markdown += `## Summary\n\n${metadata.summary}\n\n`;
  }
  
  // Add script content with line numbers
  markdown += `## Script\n\n`;
  
  lines.forEach(line => {
    // Add character name as heading
    markdown += `## ${line.character}\n\n`;
    
    // Add line number and dialogue text
    if (line.text) {
      markdown += `**${line.lineNumber}.** ${line.text}\n\n`;
    }
    
    // Add notes/stage directions as italics
    if (line.notes) {
      markdown += `*${line.notes}*\n\n`;
    }
  });
  
  return markdown;
}

/**
 * Compact converter that groups consecutive lines by the same character
 */
export function convertFormattedScriptToMarkdownCompact(formattedScript: FormattedScript): string {
  const { metadata, lines } = formattedScript;
  
  let markdown = '';
  
  // Add title
  if (metadata.title) {
    markdown += `# ${metadata.title}\n\n`;
  }
  
  // Add author
  if (metadata.author) {
    markdown += `### By ${metadata.author}\n\n`;
  }
  
  // Add characters list
  if (metadata.characters && metadata.characters.length > 0) {
    markdown += `## Characters\n\n`;
    metadata.characters.forEach(character => {
      markdown += `- ${character}\n`;
    });
    markdown += '\n';
  }
  
  // Add summary
  if (metadata.summary) {
    markdown += `## Summary\n\n${metadata.summary}\n\n`;
  }
  
  // Add script content
  markdown += `## Script\n\n`;
  
  let currentCharacter = '';
  let characterLines: string[] = [];
  
  const flushCharacterLines = () => {
    if (currentCharacter && characterLines.length > 0) {
      markdown += `## ${currentCharacter}\n\n`;
      characterLines.forEach(line => {
        markdown += `${line}\n\n`;
      });
      characterLines = [];
    }
  };
  
  lines.forEach(line => {
    if (line.character !== currentCharacter) {
      // Flush previous character's lines
      flushCharacterLines();
      currentCharacter = line.character;
    }
    
    // Collect lines for current character
    if (line.text) {
      let lineText = line.text;
      if (line.notes) {
        lineText += ` *${line.notes}*`;
      }
      characterLines.push(lineText);
    } else if (line.notes) {
      characterLines.push(`*${line.notes}*`);
    }
  });
  
  // Flush remaining lines
  flushCharacterLines();
  
  return markdown;
}
