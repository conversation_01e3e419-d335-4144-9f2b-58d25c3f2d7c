import React from 'react';
import { X } from 'lucide-react';

interface ImageModalProps {
  image: {
    url: string;
    description: string;
  };
  onClose: () => void;
}

const ImageModal: React.FC<ImageModalProps> = ({ image, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-4 max-w-3xl w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-gray-800">Image Details</h3>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={24} />
          </button>
        </div>
        <img src={image.url} alt={image.description} className="w-full h-auto max-h-[70vh] object-contain mb-4" />
        <p className="text-gray-600">{image.description}</p>
      </div>
    </div>
  );
};

export default ImageModal;

