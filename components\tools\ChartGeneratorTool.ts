// components/tools/ChartGeneratorTool.ts

interface ChartConfiguration {
    type: string;
    data: any[];
    options: {
      features?: string[];
      formatting?: Record<string, any>;
      axes?: {
        x?: {
          type: string;
          field: string;
          label?: string;
        };
        y?: {
          type: string;
          field: string;
          label?: string;
        };
      };
      title?: string;
      colors?: string[];
      legend?: boolean;
      animations?: boolean;
      tooltips?: boolean;
      stacked?: boolean;
      showGrid?: boolean;
      aspectRatio?: number;
    };
  }
  
  interface ChartResponse {
    success: boolean;
    error?: string;
    config?: {
      type: string;
      data: any;
      options: any;
      plugins?: any[];
    };
  }
  
  export class ChartGeneratorTool {
    static description = `
      ChartGeneratorTool creates optimized chart configurations based on data characteristics
      and visualization requirements. It supports:
      
      1. Chart Types:
         - Line charts for time series and trends
         - Bar charts for categorical comparisons
         - Pie charts for proportional data
         - Scatter plots for correlation analysis
      
      2. Features:
         - Automatic data formatting
         - Responsive sizing
         - Interactive tooltips
         - Customizable legends
         - Grid lines
         - Animations
         - Multiple series support
         - Stacking options
         - Color schemes
      
      3. Data Processing:
         - Handles time series data
         - Supports categorical grouping
         - Manages numerical formatting
         - Deals with missing values
         - Processes multiple series
      
      Usage:
      const config = await chartTool.call(JSON.stringify({
        type: 'line',
        data: yourData,
        options: {
          features: ['tooltips', 'legend'],
          axes: {
            x: { type: 'time', field: 'date' },
            y: { type: 'linear', field: 'value' }
          }
        }
      }));
    `;
  
    private readonly defaultColors = [
      '#4299E1', '#48BB78', '#ED8936', '#667EEA',
      '#F56565', '#9F7AEA', '#ED64A6', '#38B2AC'
    ];
  
    private readonly chartTypeConfigs = {
      line: {
        tension: 0.4,
        fill: false,
        pointRadius: 4,
        pointHoverRadius: 6
      },
      bar: {
        borderWidth: 1,
        borderRadius: 4,
        maxBarThickness: 50
      },
      pie: {
        rotation: -90,
        circumference: 360,
        cutout: '0%',
        radius: '90%'
      },
      scatter: {
        pointRadius: 6,
        pointHoverRadius: 8
      }
    };
  
    async call(input: string): Promise<ChartResponse> {
      try {
        const config = JSON.parse(input) as ChartConfiguration;
        
        if (!this.validateConfig(config)) {
          throw new Error('Invalid chart configuration');
        }
  
        const chartConfig = await this.generateChartConfig(config);
        
        return {
          success: true,
          config: chartConfig
        };
  
      } catch (error) {
        console.error('ChartGeneratorTool error:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        };
      }
    }
  
    private validateConfig(config: ChartConfiguration): boolean {
      if (!config.type || !config.data || !Array.isArray(config.data)) {
        return false;
      }
  
      if (!['line', 'bar', 'pie', 'scatter'].includes(config.type)) {
        return false;
      }
  
      if (config.data.length === 0) {
        return false;
      }
  
      return true;
    }
  
    private async generateChartConfig(config: ChartConfiguration) {
      const baseConfig = {
        type: config.type,
        data: this.processData(config),
        options: this.generateOptions(config)
      };
  
      return this.applyTypeSpecificConfig(baseConfig, config);
    }
  
    private processData(config: ChartConfiguration) {
      const datasets: ({ tension: number; fill: boolean; pointRadius: number; pointHoverRadius: number; label: string; data: number[]; backgroundColor: string; borderColor: string | undefined; } | { borderWidth: number; borderRadius: number; maxBarThickness: number; label: string; data: number[]; backgroundColor: string; borderColor: string | undefined; } | { rotation: number; circumference: number; cutout: string; radius: string; label: string; data: number[]; backgroundColor: string; borderColor: string | undefined; } | { pointRadius: number; pointHoverRadius: number; label: string; data: number[]; backgroundColor: string; borderColor: string | undefined; })[] = [];
      const typeConfig = this.chartTypeConfigs[config.type as keyof typeof this.chartTypeConfigs];
  
      if (config.type === 'pie') {
        const data = this.processPieData(config.data, config.options.axes);
        return {
          labels: data.labels,
          datasets: [{
            data: data.values,
            backgroundColor: this.generateColors(data.values.length),
            ...typeConfig
          }]
        };
      }
  
      if (config.type === 'scatter') {
        return {
          datasets: [{
            data: this.processScatterData(config.data, config.options.axes),
            backgroundColor: this.defaultColors[0],
            ...typeConfig
          }]
        };
      }
  
      // Handle line and bar charts
      const { labels, series } = this.processSeriesData(config.data, config.options.axes);
      
      Object.entries(series).forEach(([key, values], index) => {
        datasets.push({
          label: key,
          data: values,
          backgroundColor: this.defaultColors[index % this.defaultColors.length],
          borderColor: config.type === 'line' ? this.defaultColors[index % this.defaultColors.length] : undefined,
          ...typeConfig
        });
      });
  
      return {
        labels,
        datasets
      };
    }
  
    private processPieData(data: any[], axes?: ChartConfiguration['options']['axes']) {
      const labelField = axes?.x?.field || Object.keys(data[0])[0];
      const valueField = axes?.y?.field || Object.keys(data[0])[1];
  
      const aggregated = data.reduce((acc, item) => {
        const label = item[labelField];
        acc[label] = (acc[label] || 0) + Number(item[valueField]);
        return acc;
      }, {});
  
      return {
        labels: Object.keys(aggregated),
        values: Object.values(aggregated)
      };
    }
  
    private processScatterData(data: any[], axes?: ChartConfiguration['options']['axes']) {
      const xField = axes?.x?.field || Object.keys(data[0])[0];
      const yField = axes?.y?.field || Object.keys(data[0])[1];
  
      return data.map(item => ({
        x: Number(item[xField]),
        y: Number(item[yField])
      }));
    }
  
    private processSeriesData(data: any[], axes?: ChartConfiguration['options']['axes']) {
      const xField = axes?.x?.field || Object.keys(data[0])[0];
      const yField = axes?.y?.field || Object.keys(data[0])[1];
  
      const labels = [...new Set(data.map(item => item[xField]))];
      const series: Record<string, number[]> = {};
  
      if (data[0].hasOwnProperty('series')) {
        // Handle multiple series
        const seriesSet = new Set(data.map(item => item.series));
        
        seriesSet.forEach(seriesName => {
          series[seriesName as string] = labels.map(label => {
            const matchingItem = data.find(item => 
              item[xField] === label && item.series === seriesName
            );
            return matchingItem ? Number(matchingItem[yField]) : 0;
          });
        });
      } else {
        // Single series
        series[yField] = labels.map(label => {
          const matchingItem = data.find(item => item[xField] === label);
          return matchingItem ? Number(matchingItem[yField]) : 0;
        });
      }
  
      return { labels, series };
    }
  
    private generateOptions(config: ChartConfiguration) {
      const options: any = {
        responsive: true,
        maintainAspectRatio: true,
        aspectRatio: config.options.aspectRatio || 2,
        plugins: {
          legend: {
            display: config.options.legend !== false,
            position: 'top'
          },
          tooltip: {
            enabled: config.options.tooltips !== false
          }
        },
        animation: {
          duration: config.options.animations === false ? 0 : 1000
        }
      };
  
      if (config.type !== 'pie') {
        options.scales = this.generateScales(config);
      }
  
      if (config.options.stacked && (config.type === 'bar' || config.type === 'line')) {
        options.scales.x.stacked = true;
        options.scales.y.stacked = true;
      }
  
      return options;
    }
  
    private generateScales(config: ChartConfiguration) {
      const scales: any = {
        x: {
          display: true,
          grid: {
            display: config.options.showGrid !== false
          },
          title: {
            display: !!config.options.axes?.x?.label,
            text: config.options.axes?.x?.label
          }
        },
        y: {
          display: true,
          grid: {
            display: config.options.showGrid !== false
          },
          title: {
            display: !!config.options.axes?.y?.label,
            text: config.options.axes?.y?.label
          }
        }
      };
  
      if (config.options.axes?.x?.type === 'time') {
        scales.x.type = 'time';
        scales.x.time = {
          unit: this.determineTimeUnit(config.data)
        };
      }
  
      if (config.options.axes?.y?.type === 'logarithmic') {
        scales.y.type = 'logarithmic';
      }
  
      return scales;
    }
  
    private determineTimeUnit(data: any[]): string {
      const dates = data.map(item => new Date(item.date).getTime());
      const timeSpan = Math.max(...dates) - Math.min(...dates);
      const daySpan = timeSpan / (1000 * 60 * 60 * 24);
  
      if (daySpan <= 2) return 'hour';
      if (daySpan <= 31) return 'day';
      if (daySpan <= 365) return 'month';
      return 'year';
    }
  
    private generateColors(count: number): string[] {
      const colors = [...this.defaultColors];
      while (colors.length < count) {
        colors.push(this.defaultColors[colors.length % this.defaultColors.length]);
      }
      return colors.slice(0, count);
    }
  
    private applyTypeSpecificConfig(baseConfig: any, userConfig: ChartConfiguration) {
      switch (userConfig.type) {
        case 'pie':
          baseConfig.options.layout = {
            padding: 20
          };
          break;
        
        case 'scatter':
          baseConfig.options.scales.x.type = 'linear';
          baseConfig.options.scales.y.type = 'linear';
          break;
        
        case 'bar':
          if (userConfig.options.stacked) {
            baseConfig.options.scales.x.stacked = true;
            baseConfig.options.scales.y.stacked = true;
          }
          break;
        
        case 'line':
          baseConfig.options.elements = {
            line: {
              tension: 0.4
            }
          };
          break;
      }
  
      return baseConfig;
    }
  }