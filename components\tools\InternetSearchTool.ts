// components/tools/InternetSearchTool.ts
import { DynamicTool } from 'langchain/tools';
import { TokenManagement } from "@/src/tokenTracker/tokenManagement";

interface BraveSearchResult {
  title: string;
  description: string;
  url: string;
}

interface SearchToolConfig {
  maxResults?: number;
  tokenManager?: TokenManagement;
}

/**
 * InternetSearchTool
 * 
 * A tool for performing internet searches using the Brave Search API.
 * This tool should be used when current or real-time information is needed
 * that might not be available in the existing knowledge base.
 */
export class InternetSearchTool extends DynamicTool {
  private tokenManager?: TokenManagement;
  private maxResults: number;
  private apiKey: string;

  static description = `Use this tool to search the internet for current information when:
  - The query requires up-to-date or real-time information
  - The information needed is not likely to be in the existing knowledge base
  - The user explicitly requests internet search or current information
  - The query involves recent events, news, or developments
  
  Input should be a specific search query.
  Output will contain relevant search results including titles, snippets, and URLs.`;

  constructor(config: SearchToolConfig) {
    super({
      name: 'internet_search',
      description: InternetSearchTool.description,
      func: async (query: string) => await this.searchBrave(query)
    });

    // Get API key from environment using the correct variable name
    const apiKey = process.env.SEARCH_API;
    if (!apiKey) {
      throw new Error('SEARCH_API environment variable is not set');
    }

    this.apiKey = apiKey;
    this.maxResults = config.maxResults || 3;
    this.tokenManager = config.tokenManager;
  }

  private async searchBrave(query: string) {
    try {
      const startTime = Date.now();

      const response = await fetch(
        `https://api.search.brave.com/res/v1/web/search?q=${encodeURIComponent(query)}`, 
        {
          headers: {
            'Accept': 'application/json',
            'X-Subscription-Token': this.apiKey
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Brave Search API error: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Extract and process results
      const webResults = data.web?.results?.slice(0, this.maxResults) || [];
      const snippets = webResults.map((result: BraveSearchResult) => ({
        title: result.title,
        snippet: result.description,
        link: result.url
      }));

      // Calculate token usage if token manager is provided
      if (this.tokenManager) {
        const estimatedTokens = snippets.reduce((total: number, result: { title: string | any[]; snippet: string | any[]; }) => {
          return total + 
            (result.title?.length || 0) / 4 + 
            (result.snippet?.length || 0) / 4;
        }, 0);
        const currentTokens = this.tokenManager.getCurrentUsage();
      }

      return {
        success: true,
        results: snippets,
        metadata: {
          source: 'brave_search',
          searchTime: Date.now() - startTime,
          resultCount: snippets.length
        }
      };

    } catch (error) {
      console.error('Brave Search API error:', error);
      return {
        success: false,
        results: [],
        metadata: {
          source: 'brave_search',
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        }
      };
    }
  }
}