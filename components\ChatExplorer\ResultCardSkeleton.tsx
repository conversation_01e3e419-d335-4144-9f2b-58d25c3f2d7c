import React from 'react';

const ResultCardSkeleton = () => {
  return (
    <div className="rounded-lg bg-ike-dark-purple border border-ike-purple shadow-lg animate-pulse">
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div>
            <div className="h-7 w-32 bg-gray-700 rounded mb-2"></div>
            <div className="flex gap-2">
              <div className="w-24 h-6 bg-gray-700 rounded-full"></div>
              <div className="w-28 h-6 bg-blue-500 bg-opacity-20 rounded-full"></div>
            </div>
          </div>
          <div className="w-24 h-6 bg-gray-700 rounded"></div>
        </div>

        <div className="space-y-4">
          <div>
            <div className="h-5 w-20 bg-gray-700 rounded mb-2"></div>
            <div className="h-16 bg-gray-700 rounded"></div>
          </div>

          <div>
            <div className="h-5 w-32 bg-gray-700 rounded mb-2"></div>
            <div className="bg-ike-message-bg rounded-lg p-4">
              <div className="h-12 bg-gray-700 rounded"></div>
              <div className="h-6 w-16 bg-gray-700 rounded mt-2"></div>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-2">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-gray-900 rounded-lg p-4">
                <div className="h-5 w-24 bg-gray-700 rounded mb-2"></div>
                <div className="h-4 bg-gray-700 rounded"></div>
              </div>
            ))}
          </div>

          <div className="bg-gray-900 rounded-lg p-4 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-700 rounded mx-auto mb-2"></div>
              <div className="h-4 w-32 bg-gray-700 rounded mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const ResultsGridSkeleton = () => {
  return (
    <div className="grid lg:grid-cols-3 gap-6">
      {[1, 2, 3].map((i) => (
        <ResultCardSkeleton key={i} />
      ))}
    </div>
  );
};

export { ResultCardSkeleton, ResultsGridSkeleton };