'use client'

import React from 'react';
import { useSearchParams } from 'next/navigation';
import VisualizationPage from '../../components/tools/VisualizationPage';

export default function VisualizationRoute() {
    const searchParams = useSearchParams();
    const dataParam = searchParams.get('data');

    let parsedContent = null;

    if (dataParam) {
        try {
            parsedContent = JSON.parse(decodeURIComponent(dataParam));
        } catch (error) {
            console.error('Error parsing data from URL:', error);
        }
    }

    if (!parsedContent) {
        return <div>Error: Unable to parse visualization data</div>;
    }

    return <VisualizationPage parsedContent={parsedContent} />;
}

