// components/DeleteChatRow.tsx

'use client';

/*
  Purpose/Summary:
  The DeleteChatRow component provides a trash icon to delete a chat from Firestore.
  When the trash icon is clicked, a modal confirmation dialog is displayed.
  If the user confirms, the chat is deleted.

  Key Features:
  - Displays a trash icon.
  - Shows a modal confirmation dialog before deleting the chat.
  - Deletes the chat from Firestore if the user confirms.
  - Displays the chat message and date created in the modal.

  Dependencies:
  - Requires Firebase Firestore for database operations.
  - Requires NextAuth for session management.
  - Requires Heroicons for UI icons.
  - Uses Tailwind CSS for styling.
*/

import { useState } from 'react';
import { TrashIcon } from '@heroicons/react/24/outline';
import { deleteDoc, doc } from 'firebase/firestore';
import { db } from '../components/firebase';
import { useSession } from 'next-auth/react';

type Props = {
  id: string;
  message: string;
  dateCreated: string;
  onDelete?: () => void;
};

function DeleteChatRow({ id, message, dateCreated, onDelete }: Props) {
  const { data: session } = useSession();
  const [showModal, setShowModal] = useState(false);

  const deleteChat = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!session) return;

    const chatDocRef = doc(db, 'users', session.user?.email!, 'chats', id);

    try {
      await deleteDoc(chatDocRef);
      if (onDelete) {
        onDelete();
      }
    } catch (error) {
      console.error('Error deleting chat:', error);
    }
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowModal(true);
  };

  const handleConfirm = (e: React.MouseEvent) => {
    deleteChat(e);
    setShowModal(false);
  };

  const handleCancel = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowModal(false);
  };

  return (
    <>
      <TrashIcon 
        onClick={handleDeleteClick} 
        className="h-4 w-4 text-red-600 hover:text-red-800 flex-shrink-0 cursor-pointer"
      />
      {showModal && (
        <div 
          className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="bg-white text-gray-700 p-4 rounded shadow-lg">
            <p>Are you sure you want to delete this chat?</p>
            <div className="mt-4">
              <p><strong>Message:</strong> {message}</p>
              <p><strong>Date Created:</strong> {dateCreated}</p>
            </div>
            <div className="mt-4 flex justify-end space-x-2">
              <button onClick={handleConfirm} className="px-4 py-2 bg-red-500 text-white rounded">Yes</button>
              <button onClick={handleCancel} className="px-4 py-2 bg-blue-500 text-white rounded">No</button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default DeleteChatRow;
