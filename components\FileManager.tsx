"use client"

import React, { ReactNode, useState } from 'react'
import { useSession } from 'next-auth/react'
import SideBar from 'components/SideBar'

interface FileManagerProps {
  children: ReactNode
}

export default function FileManager({ children }: FileManagerProps) {
  const { status } = useSession()
  const [sidebarOpen, setSidebarOpen] = useState(false)

  if (status === "loading") {
    return <div>Loading...</div>
  }

  return (
    <div className="flex flex-col min-h-screen text-slate-900">
      <div className="flex flex-row h-screen overflow-y-auto">
        {/* Sidebar for desktop */}
        <div className="hidden md:block bg-ike-dark-purple w-64">
          <SideBar />
        </div>

        {/* Mobile sidebar */}
        <div
          className={`md:hidden fixed inset-y-0 left-0 z-50 w-64 bg-ike-dark-purple transform ${
            sidebarOpen ? 'translate-x-0' : '-translate-x-full'
          } transition-transform duration-300 ease-in-out`}
        >
          {/* <SideBar /> */}
        </div>

        {/* Main content */}
        <main className="flex-1 overflow-y-auto bg-white text-slate-900">
          {/* Mobile menu button */}
          <div className="md:hidden p-4">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 rounded-md text-gray-500 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ike-dark-purple"
              aria-label="Toggle sidebar"
            >
              <svg
                className="h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
          {children}
        </main>
      </div>
    </div>
  )
}