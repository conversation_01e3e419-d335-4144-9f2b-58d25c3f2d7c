// app/layout.tsx
import "styles/globals.css";
import { ReactNode } from "react";
import { Providers } from "./providers";

export default function RootLayout({
  children,
}: {
  children: ReactNode,
}) {
  return (
    <html lang="en">
      <body className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-950 text-gray-900 dark:text-white transition-colors duration-300">
        <Providers>
          <div className="flex flex-row w-full min-h-screen">
            <div className="h-screen">
              {/* <SideBar /> */}
            </div>
            <div className="flex-grow">
              <div className="max-w-full">
                {children}
              </div>
            </div>
          </div>
        </Providers>
      </body>
    </html>
  );
}