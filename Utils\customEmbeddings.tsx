//utils/customEmbeddings.ts

import { Pinecone } from "@pinecone-database/pinecone";

async function generateDocEmbeddings(text: string): Promise<number[]> {
  const client = new Pinecone({ apiKey: process.env.PINECONE_API_KEY! });
  const model = 'multilingual-e5-large';

  try {
    const response = await client.inference.embed(
      model,
      [text],
      { inputType: 'passage', truncate: 'END' }
    );

    // Ensure that the response contains embeddings
    const embeddings = response.data?.[0]?.values;
    if (!embeddings) {
      throw new Error('Failed to generate embeddings: No embeddings returned');
    }

    return embeddings;
  } catch (error) {
    console.error('Error generating embeddings:', error);
    throw new Error('Failed to generate embeddings');
  }
}

export class CustomEmbeddings {
  private model: string;

  constructor(model: string) {
    this.model = model;
  }

  async embedDocuments(texts: string[]): Promise<number[][]> {
    const embeddings = await Promise.all(texts.map(text => generateDocEmbeddings(text)));
    return embeddings;
  }

  async embedQuery(text: string): Promise<number[]> {
    return generateDocEmbeddings(text);
  }
}
