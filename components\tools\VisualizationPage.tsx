'use client'

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Line } from 'recharts';

interface VisualizationPageProps {
  parsedContent: {
    type: string;
    content: {
      type: string;
      config: {
        type: string;
        stacked: boolean;
        xAxis: string;
        yAxis: string;
        series: string[];
        colors: string[];
      };
      data: any[];
      metadata: {
        type: string;
        metrics: string[];
      };
      confidence: number;
      reasoning: string;
    };
  } | null;
}

const determineChartType = (type: string) => {
  switch (type) {
    case 'bar':
    case 'stacked-bar':
      return 'bar';
    case 'line':
      return 'line';
    default:
      return 'bar';
  }
};

const VisualizationPage: React.FC<VisualizationPageProps> = ({ parsedContent }) => {
  if (!parsedContent || !parsedContent.content) {
    return <div>No valid data to display</div>;
  }

  const { content } = parsedContent;
  const chartType = determineChartType(content.config.type);
  const xAxis = content.config.xAxis;
  const yAxis = content.config.yAxis;
  const series = content.config.series;
  const colors = content.config.colors;
  const data = content.data;

  const renderChart = () => {
    if (chartType === 'line') {
      return (
        <LineChart data={data}>
          <XAxis dataKey={xAxis} />
          <YAxis label={{ value: yAxis, angle: -90, position: 'insideLeft' }} />
          <Tooltip />
          <Legend />
          {series.map((item: string, index: number) => (
            <Line key={item} type="monotone" dataKey={`metrics.${item}`} stroke={colors[index]} />
          ))}
        </LineChart>
      );
    } else {
      return (
        <BarChart data={data}>
          <XAxis dataKey={xAxis} />
          <YAxis label={{ value: yAxis, angle: -90, position: 'insideLeft' }} />
          <Tooltip />
          <Legend />
          {series.map((item: string, index: number) => (
            <Bar key={item} dataKey={`metrics.${item}`} stackId={content.config.stacked ? "a" : undefined} fill={colors[index]} />
          ))}
        </BarChart>
      );
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Visualization: {content.type}</h1>
      <h2 className="text-xl font-semibold mb-2">{yAxis} over {xAxis}</h2>
      <p className="mb-4">Confidence: {content.confidence.toFixed(2)}</p>
      <p className="mb-4">Reasoning: {content.reasoning}</p>
      <ResponsiveContainer width="100%" height={400}>
        {renderChart()}
      </ResponsiveContainer>
    </div>
  );
};

export default VisualizationPage;

