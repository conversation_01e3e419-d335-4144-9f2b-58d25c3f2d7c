'use client';

import React, { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from 'components/firebase';
import Image from 'next/image';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ArrowLeft } from 'lucide-react';
import DashboardModal from 'components/Visualization/dashboardModal';

interface ChartData {
  url: string;
  description: string;
  id: string;
  type: string;
}

const chartIcons: { [key: string]: React.ReactNode } = {
  'bar': <BarChart className="w-12 h-12 text-blue-200" />,
  'pie': <PieChart className="w-12 h-12 text-blue-200" />,
  'line': <LineChart className="w-12 h-12 text-blue-200" />,
};

export default function ChartGalleryPage() {
  const { data: session } = useSession();
  const userId = session?.user?.email ?? null;
  const [charts, setCharts] = useState<ChartData[]>([]);
  const [selectedChart, setSelectedChart] = useState<ChartData | null>(null);
  const [showSignOutModal, setShowSignOutModal] = useState(false);

  useEffect(() => {
    if (!userId) return;

    const fetchCharts = async () => {
      try {
        const qRef = query(
          collection(db, 'users', userId, 'files'),
          where('category', '==', 'Charts'), 
          orderBy('createdAt', 'desc')
        );
        const snapshot = await getDocs(qRef);

        const temp: ChartData[] = [];
        snapshot.forEach((docSnap) => {
          const data = docSnap.data();
          temp.push({
            url: data.downloadUrl,
            description: data.description || 'No description available',
            id: docSnap.id,
            type: data.chartType || 'bar',
          });
        });

        setCharts(temp);
      } catch (error) {
        console.error('Error fetching charts for gallery:', error);
      }
    };

    fetchCharts();
  }, [userId]);

  if (!userId) {
    return <div className="text-white">Please log in to view your charts.</div>;
  }

  return (
    <div className="min-h-screen bg-ike-message-bg">
      <div className="flex">
        <div className="flex-1 container mx-auto px-4 py-8">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-4 -mt-10">
              <BarChart className="w-18 h-18 text-blue-200" />
              <h1 className="text-3xl font-bold text-blue-200">My Chart Gallery</h1>
            </div>
            {session?.user && (
              <div className="flex items-center">
                <span className="text-white mr-2">{session.user.name}</span>
                <button
                  onClick={() => setShowSignOutModal(true)}
                  className="flex items-center"
                >
                  <Image
                    src={session.user.image || '/placeholder-user.jpg'}
                    alt="User profile"
                    width={40}
                    height={40}
                    className="rounded-full"
                  />
                </button>
              </div>
            )}
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {charts.map((chart) => (
              <div key={chart.id} className="flex flex-col items-center" onClick={() => setSelectedChart(chart)}>
                <div className="w-full h-48 border border-gray-300 rounded cursor-pointer bg-white flex items-center justify-center">
                  {chartIcons[chart.type] || <BarChart className="w-12 h-12 text-blue-200" />}
                </div>
                <span className="text-sm mt-2 truncate text-blue-200">
                  {chart.description.length > 26 
                    ? `${chart.description.substring(0, 26)}...` 
                    : chart.description}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
      {selectedChart && (
        <DashboardModal
          chart={selectedChart}
          onClose={() => setSelectedChart(null)}
        />
      )}
      {/* TODO: Implement SignOut modal */}
    </div>
  );
}

