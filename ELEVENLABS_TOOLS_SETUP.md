# ElevenLabs Tools Configuration Guide

This guide explains how to properly configure tools for ElevenLabs Conversational AI agents, addressing the "I don't have access to the necessary script management tools" error.

## Problem Analysis

The error occurs because ElevenLabs Conversational AI supports two types of tools:

1. **Server Tools (Webhooks)** - External API calls to your server
2. **Client Tools** - Functions that run in the client-side code

Your `switch_to_script_tab` function is a **Client Tool** that acknowledges user readiness without automatically switching UI tabs. It requires both:
- Configuration in the ElevenLabs agent (via dashboard or API)
- Registration in your client-side code

## Solution Implemented

### 1. Programmatic Client Tool Configuration

**File: `components/scriptreaderAI/elevenlabs.ts`**

Added `configureAgentClientTools()` function that:
- Defines the `switch_to_script_tab` client tool for readiness acknowledgment
- Updates the agent configuration via API
- Ensures proper tool structure with `type: "client"`
- Sets `wait_for_response: true` for proper response handling
- **Note**: <PERSON><PERSON> no longer automatically switches UI tabs

### 2. Client-Side Tool Registration

**File: `components/scriptreaderAI/Reader-modal.tsx`**

Added client tools registration:
- Creates a `clientTools` object with registered functions
- Passes `clientTools` to the `useConversation` hook
- Handles tool calls with proper response formatting
- **Note**: Tool responses inform users to manually navigate to Script tab

### 3. Automatic Tool Configuration

The system now automatically:
- Configures client tools when the modal opens
- Verifies tool configuration during connection testing
- Re-configures tools if they're missing

## Usage Instructions

### Option 1: Automatic Configuration (Recommended)

The tools are now configured automatically when you:
1. Open the Script Reader modal
2. Start a conversation
3. The system detects missing tools

### Option 2: Manual Configuration via Dashboard

If you prefer manual configuration:

1. Go to your ElevenLabs agent dashboard
2. Navigate to the "Tools" section
3. Click "Add Tool"
4. Select "Client" as Tool Type
5. Configure:
   - **Name**: `switch_to_script_tab`
   - **Description**: "Call this function when the user confirms they are ready to begin rehearsal. Acknowledges readiness and informs user to manually navigate to Script tab."
   - **Parameters**:
     - `ready` (boolean, required): "Set to true when user has confirmed they are ready"

### Option 3: Webhook Configuration (Alternative)

For server-side handling, you can configure a webhook:

1. In ElevenLabs dashboard, select "Webhook" as Tool Type
2. Configure:
   - **Name**: `switch_to_script_tab`
   - **Method**: POST
   - **URL**: `https://yourdomain.com/api/elevenlabs-webhook`
   - **Description**: Same as above

## Debugging Tools

### Browser Console Commands

Open browser console and run:

```javascript
// Test the entire tool system
await debugToolSystem()

// Test API connection and tool configuration
await testElevenLabsConnection()

// Manually configure client tools
await configureAgentClientTools()

// Test voice selection
await debugVoiceSelection('rCuVrCHOUMY3OwyJBJym')
```

### Expected Console Output

When working correctly, you should see:
```
[TOOL_DEBUG] 🏁 Tool system debugging completed: {
  success: true,
  apiConnection: true,
  toolsConfigured: true,
  clientToolsRegistered: true,
  totalTools: 1,
  scriptToolDetails: { name: "switch_to_script_tab", type: "client", ... }
}
```

## Troubleshooting

### Issue: "I don't have access to the necessary script management tools"

**Cause**: Tools not properly configured or registered

**Solution**:
1. Run `debugToolSystem()` in console
2. Check if `toolsConfigured: true` and `clientToolsRegistered: true`
3. If false, run `configureAgentClientTools()`
4. Wait 30 seconds for ElevenLabs to propagate changes
5. Restart the conversation
6. **Note**: Tool will acknowledge readiness but won't automatically switch tabs

### Issue: Tool calls not being triggered

**Cause**: Tool description or parameters unclear to AI

**Solution**:
1. Update the system prompt to be more explicit about when to call tools
2. Ensure tool descriptions are clear and specific
3. Use high-intelligence models (GPT-4o mini, Claude 3.5 Sonnet)

### Issue: Connection drops frequently

**Cause**: Network issues or API limits

**Solution**:
1. Check ElevenLabs service status
2. Verify API key validity
3. Monitor network connection stability
4. Check API usage limits

## File Structure

```
components/scriptreaderAI/
├── elevenlabs.ts              # Tool configuration functions
├── Reader-modal.tsx           # Client tool registration
└── useAgentModality.tsx       # Tool definitions

app/api/
└── elevenlabs-webhook/
    └── route.ts               # Webhook endpoint (optional)
```

## Next Steps

1. Test the automatic configuration by opening the Script Reader
2. Use the debugging commands to verify tool setup
3. Monitor console logs for any configuration issues
4. Consider implementing additional tools for enhanced functionality
