import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: NextRequest) {
  const body = await request.json(); // Parse the request body
  const { email, verificationToken } = body;

  // Validate request body
  if (!email || !verificationToken) {
    return NextResponse.json({ message: 'Missing email or verification token' }, { status: 400 });
  }

  try {
    // Create a nodemailer transporter
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: parseInt(process.env.EMAIL_PORT || '587'),
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    // Send the verification email
    await transporter.sendMail({
      from: process.env.EMAIL_FROM,
      to: email,
      subject: 'Verify Your iKe Account',
      html: `
        <h1>Welcome to iKe!</h1>
        <p>Please click the link below to verify your account:</p>
        <a href="${process.env.NEXT_PUBLIC_BASE_URL}/verify-email?token=${verificationToken}">Verify Your Account</a>
      `,
    });

    // Return success response
    return NextResponse.json({ message: 'Verification email sent successfully' }, { status: 200 });
  } catch (error) {
    console.error('Error sending verification email:', error);
    return NextResponse.json({ message: 'Failed to send verification email' }, { status: 500 });
  }
}
