// src/types/chat.ts

import { Session } from 'next-auth';
import { FieldValue, serverTimestamp } from 'firebase/firestore';

// Separate type for server-side timestamp
export interface FirestoreTimestamp {
  seconds: number;
  nanoseconds: number;
}

// Separate interfaces for creating vs reading chat data
export interface ChatDataCreate {
  id: string;
  text: string;
  userId: string;
  role: 'user' | 'ai';
  createdAt: FieldValue;  // For when creating new messages
  fileDocumentId?: string | null;
  stopped?: boolean;
}

export interface ChatData {
  id: string;
  text: string;
  userId: string;
  role: 'user' | 'ai';
  createdAt: FirestoreTimestamp;  // For when reading messages
  fileDocumentId?: string | null;
  stopped?: boolean;
}

export interface TokenUsage {
  contextTokens: number;
  systemPromptTokens: number;
  chatHistoryTokens: number;
  totalTokens: number;
}

export interface TokenMetadata {
  tokenUsage: {
    contextTokens: number;
    chatHistoryTokens: number;
  };
  pageContent: string;
  pageNumber: string | null;
  pageTitle: string;
}

export interface MessageInputProps {
  onSendMessage: (message: string) => void;
  onStopProcessing: () => void;
  isPending: boolean;
  isFirstMessage: boolean;
}

export interface PdfDocumentAreaProps {
  pageContent: string;
  pageNumber: string | null;
  docTitle: string;
  onPageContentUpdate: (content: string) => void;
  onPageNumberUpdate: (pageNumber: string | null) => void;
}

export interface InitialResponsesProps {
  initialResponses: Array<{
    title: string;
    prompt: string;
  }>;
  onPromptClick: (prompt: string) => void;
  showInitialResponses: boolean;
  documentName: string;
}

export interface ChatAreaProps {
  initialMessages: ChatData[];
  session: Session | null;
  loadingMessages: boolean;
  errorMessages: string | null;
  isPending: boolean;
  selectedDocId: string | null;
  maxTokens?: number;
}

// Helper function to type-check session
export function isValidSession(session: any): session is Session {
  return session && 
         typeof session === 'object' && 
         session.user?.email && 
         'expires' in session;
}

// Utility type for document fetching
export interface DocumentFetchResult {
  documentId: string | null;
  error?: string;
}

export interface MessageMetadata {
  pageContent: string;
  pageNumber: string | null;
  pageTitle: string;
  tokenUsage?: {
    contextTokens: number;
    chatHistoryTokens: number;
  };
}

// Helper function to create a chat message
export function createChatMessage(
  text: string, 
  userId: string, 
  role: 'user' | 'ai', 
  fileDocumentId?: string | null,
  stopped?: boolean
): ChatDataCreate {
  return {
    id: `${Date.now()}`,
    text,
    userId,
    role,
    createdAt: serverTimestamp(),
    fileDocumentId,
    stopped
  };
}