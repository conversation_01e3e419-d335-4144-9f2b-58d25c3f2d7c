import { Timestamp } from 'firebase/firestore';

/**
 * Interface for formatted script documents stored in Firebase
 */
export interface FormattedScriptDocument {
  id: string;
  originalFileId: string; // Reference to the original file in "files" collection
  originalNamespace: string; // Namespace from the original "files" collection
  formattedData: {
    metadata: {
      title: string;
      author: string;
      characters: string[];
      summary: string;
    };
    lines: Array<{
      lineNumber: number;
      character: string;
      text: string;
      notes?: string;
    }>;
  }; // The structured script data
  formattedMarkdown?: string; // Optional fallback markdown content
  createdAt: Timestamp;
  updatedAt: Timestamp;
  userId: string; // For user-specific access control
  formattingStatus: 'pending' | 'completed' | 'failed' | 'retrying';
  formattingError?: string; // Error message if formatting failed
  formattingAttempts: number; // Number of formatting attempts
  lastFormattingAttempt?: Timestamp;
}

/**
 * Interface for creating new formatted script documents
 */
export interface CreateFormattedScriptData {
  originalFileId: string;
  originalNamespace: string;
  formattedData: {
    metadata: {
      title: string;
      author: string;
      characters: string[];
      summary: string;
    };
    lines: Array<{
      lineNumber: number;
      character: string;
      text: string;
      notes?: string;
    }>;
  };
  formattedMarkdown?: string; // Optional fallback
  userId: string;
  formattingStatus: 'completed' | 'failed';
  formattingError?: string;
  formattingAttempts: number;
}

/**
 * Interface for updating formatted script documents
 */
export interface UpdateFormattedScriptData {
  formattedMarkdown?: string;
  metadata?: {
    title: string;
    author: string;
    characters: string[];
    summary: string;
  };
  formattingStatus?: 'pending' | 'completed' | 'failed' | 'retrying';
  formattingError?: string;
  formattingAttempts?: number;
  lastFormattingAttempt?: Timestamp;
  updatedAt: Timestamp;
}

/**
 * Interface for script file data from the files collection
 */
export interface ScriptFileData {
  id: string;
  name: string;
  namespace: string;
  category: string;
  downloadUrl: string;
  createdAt: any; // Firebase Timestamp
  size: number;
  type: string;
}

/**
 * Interface for formatted script retrieval response
 */
export interface FormattedScriptResponse {
  success: boolean;
  data?: FormattedScriptDocument;
  error?: string;
  fallbackToRaw?: boolean;
}

/**
 * Interface for script formatting job status
 */
export interface ScriptFormattingJob {
  fileId: string;
  namespace: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  startedAt?: Timestamp;
  completedAt?: Timestamp;
  error?: string;
  attempts: number;
}
