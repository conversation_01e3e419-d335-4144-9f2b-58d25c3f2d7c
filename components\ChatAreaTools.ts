import { collection, addDoc, serverTimestamp, doc, getDoc, updateDoc, getDocs, query, orderBy } from 'firebase/firestore'
import { db } from 'components/firebase'
import { Session } from 'next-auth'
import { ChatData } from '@/src/types/chat'


// Types


interface ChatMetadata {
  pageContent: string
  pageNumber: string | null
  pageTitle: string
}

interface ChatAreaState {
  setIsPending: (isPending: boolean) => void
  setError: (error: string | null) => void
  setMessages: (messages: ChatData[] | ((prev: ChatData[]) => ChatData[])) => void
  setMessage: (message: string) => void
  setStreamingMessageId: (id: string | null) => void
  setStreamingText: (text: string) => void
  firstValidUserMessage?: string | null;
  firstValidAIResponse?: string | null;
  setFirstValidUserMessage?: (message: string | null) => void;
  setFirstValidAIResponse?: (message: string | null) => void;  
  setIsFirstMessage: (isFirst: boolean) => void
  setIsStreaming: (isStreaming: boolean) => void
  setPageContent: (content: string) => void
  setPageNumber: (number: string | null) => void
  setDocTitle: (title: string) => void
  setAbortController: (controller: AbortController | null) => void

}

export class ChatAreaTools {
  private session: Session | null
  private selectedDocId: string | null
  private fileDocumentId: string | null
  private state: ChatAreaState
  private abortController: AbortController | null = null

  constructor(
    session: Session | null, 
    selectedDocId: string | null, 
    fileDocumentId: string | null,
    state: ChatAreaState
  ) {
    this.session = session
    this.selectedDocId = selectedDocId
    this.fileDocumentId = fileDocumentId
    this.state = state
  }

  // Static utility methods
  static async fetchCurrentDocumentId(session: Session | null, chatId: string | null): Promise<string | null> {
    if (!session?.user?.email || !chatId) return null

    const chatDocRef = doc(db, 'users', session.user.email, 'chats', chatId)
    const chatDocSnap = await getDoc(chatDocRef)

    return chatDocSnap.exists() ? chatDocSnap.data()?.fileDocumentId || null : null
  }

  static getIsLastAIMessage(messages: ChatData[], index: number): boolean {
    const aiMessages = messages.filter(msg => msg.userId === 'ai-response')
    return aiMessages.length > 0 && aiMessages[aiMessages.length - 1] === messages[index]
  }

  static getIsLastUserMessage(messages: ChatData[], index: number, userEmail: string | null): boolean {
    const userMessages = messages.filter(msg => msg.userId === userEmail)
    return userMessages.length > 0 && userMessages[userMessages.length - 1] === messages[index]
  }

  static createAIMessage(aiMessageId: string, fileDocumentId: string | null): ChatData {
    return {
      id: aiMessageId,
      text: '<span class="dot"></span><span class="dot"></span><span class="dot"></span>',
      userId: 'ai-response',
      role: 'ai',
      createdAt: {
        seconds: Math.floor(Date.now() / 1000),
        nanoseconds: 0,
      },
      fileDocumentId,
      stopped: false
    }
  }

  static createUserMessage(messageText: string, userEmail: string | null, fileDocumentId: string | null): ChatData {
    return {
      id: `${Date.now()}`,
      text: messageText,
      userId: userEmail || 'user',
      role: 'user',
      createdAt: {
        seconds: Math.floor(Date.now() / 1000),
        nanoseconds: 0,
      },
      fileDocumentId,
    }
  }

  // Instance methods for Firebase operations
  async saveUserMessage(userMessage: ChatData): Promise<void> {
    if (!this.session?.user?.email || !this.selectedDocId) return

    await addDoc(
      collection(db, 'users', this.session.user.email, 'chats', this.selectedDocId, 'messages'),
      {
        ...userMessage,
        createdAt: serverTimestamp(),
        fileDocumentId: this.fileDocumentId,
      }
    )
  }

  async saveAIMessage(aiMessage: ChatData): Promise<void> {
    if (!this.session?.user?.email || !this.selectedDocId) return

    await addDoc(
      collection(db, 'users', this.session.user.email, 'chats', this.selectedDocId, 'messages'),
      {
        ...aiMessage,
        createdAt: serverTimestamp(),
        fileDocumentId: this.fileDocumentId,
      }
    )
  }

  async updateFirstMessage(messageText: string): Promise<void> {
    if (!this.session?.user?.email || !this.selectedDocId) return

    try {
        const chatDocRef = doc(
            db,
            'users',
            this.session.user.email,
            'chats',
            this.selectedDocId
        )
        await updateDoc(chatDocRef, {
            firstMessage: messageText,
            updatedAt: serverTimestamp() // Added for tracking
        })
    } catch (error) {
        console.error('Error updating first message:', error)
        throw error // Propagate error for handling in handleSendMessage
    }
}


  // Chat handling methods
  handleReprocess = (messageText: string) => {
    console.log("Reprocessing message:", messageText)
    this.handleSendMessage(messageText, '')
  }

  handleStopProcessing = () => {
    if (this.abortController) {
      this.abortController.abort()
      this.state.setAbortController(null)
      this.abortController = null
    }
    
    this.state.setIsStreaming(false)
    this.state.setIsPending(false)
    
    this.state.setMessages((prevMessages) => {
      const updatedMessages = [...prevMessages]
      const lastMessageIndex = updatedMessages.length - 1
      
      if (lastMessageIndex >= 0 && updatedMessages[lastMessageIndex].role === 'ai') {
        updatedMessages[lastMessageIndex] = {
          ...updatedMessages[lastMessageIndex],
          text: 'Message generation stopped.',
          stopped: true
        }
      }
      return updatedMessages
    })
    
    this.state.setStreamingMessageId(null)
    this.state.setStreamingText('')
  }

  handleSendMessage = async (messageText: string, menuSelected: string) => {
    if (!messageText.trim() || !this.session?.user?.email) return

    this.state.setIsPending(true)
    this.state.setError(null)

    try {
        const chatHistory = await this.getChatHistory()
        const combinedHistory = chatHistory
            .map((msg) => `${msg.role === 'user' ? 'User: ' : 'AI: '}${msg.text}`)
            .join('\n')

        const userMessage = ChatAreaTools.createUserMessage(
            messageText,
            this.session.user.email,
            this.fileDocumentId
        )
        
        await this.saveUserMessage(userMessage)
        this.state.setMessages((prevMessages) => [...prevMessages, userMessage])
        this.state.setMessage('')
        this.state.setStreamingMessageId(null)
        this.state.setStreamingText('')

        // Legacy-style first message handling
        const isFirst = await this.isFirstMessage()
        if (isFirst) {
            await this.updateFirstMessage(messageText)
            this.state.setIsFirstMessage(false)
        }

        // Only set firstValidUserMessage if it hasn't been set yet
        if (this.state.setFirstValidUserMessage && !this.state.firstValidUserMessage) {
            this.state.setFirstValidUserMessage(messageText)
        }

        const newAbortController = new AbortController()
        this.state.setAbortController(newAbortController)
        this.abortController = newAbortController

        await this.fetchAIResponse(combinedHistory, messageText, menuSelected, newAbortController.signal)
    } catch (error) {
        console.error('Error sending message:', error)
        this.state.setError('Failed to send message. Please try again.')
    } finally {
        this.state.setIsPending(false)
        this.state.setAbortController(null)
        this.abortController = null
    }
}


private fetchAIResponse = async (
  combinedHistory: string,
  userPrompt: string,
  menuSelected: string,
  signal: AbortSignal
) => {
  this.initializeAIResponse()

  const aiMessageId = `${Date.now()}`
  const aiMessage = ChatAreaTools.createAIMessage(aiMessageId, this.fileDocumentId)

  this.state.setStreamingMessageId(aiMessageId)
  this.state.setMessages((prevMessages) => [...prevMessages, aiMessage])

  let cumulativeStreamedText = ''

  try {
      const response = await this.makeAIRequest(userPrompt, combinedHistory, menuSelected, signal)
      const reader = response.body?.getReader()

      if (!reader) {
          throw new Error('No reader available')
      }

      let metadataReceived = false

      while (true) {
          const { done, value } = await reader.read()

          if (done) {
              this.finalizeAIResponse()
              break
          }

          const chunk = new TextDecoder().decode(value)

          if (!metadataReceived) {
              metadataReceived = this.handleMetadata(chunk)
          } else {
              cumulativeStreamedText += chunk
              this.updateStreamingResponse(cumulativeStreamedText, aiMessageId)
          }
      }

      if (!signal.aborted) {
          await this.saveAIMessage({
              ...aiMessage,
              text: cumulativeStreamedText,
          })

          // Track first valid AI response
          if (this.state.setFirstValidAIResponse) {
              this.state.setFirstValidAIResponse(cumulativeStreamedText)
          }
      }

  } catch (error) {
      this.handleAIResponseError(error)
  } finally {
      this.state.setIsStreaming(false)
  }
}



  // Private helper methods
  public async getChatHistory(): Promise<ChatData[]> {
    if (!this.session?.user?.email || !this.selectedDocId) return []

    const messagesRef = collection(
      db,
      'users',
      this.session.user.email,
      'chats',
      this.selectedDocId,
      'messages'
    )
    
    const q = query(messagesRef, orderBy('createdAt', 'asc'))
    const querySnapshot = await getDocs(q)
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }) as ChatData)
  }

  private async isFirstMessage(): Promise<boolean> {
    if (!this.session?.user?.email || !this.selectedDocId) return true

    try {
        const chatDocRef = doc(
            db,
            'users',
            this.session.user.email,
            'chats',
            this.selectedDocId
        )
        const chatDocSnap = await getDoc(chatDocRef)
        return !chatDocSnap.exists() || !chatDocSnap.data()?.firstMessage
    } catch (error) {
        console.error('Error checking first message status:', error)
        return false
    }
}


  private initializeAIResponse(): void {
    this.state.setPageContent('Document preview loading')
    this.state.setPageNumber(null)
    this.state.setDocTitle('Unknown Document')
    this.state.setIsStreaming(true)
    this.state.setError(null)
  }

  private finalizeAIResponse(): void {
    this.state.setStreamingMessageId(null)
    this.state.setIsStreaming(false)
  }

  private async makeAIRequest(
    prompt: string,
    history: string,
    menuSelected: string,
    signal: AbortSignal
  ): Promise<Response> {
    const response = await fetch('/api/chatGroq', {
      method: 'POST',
      body: JSON.stringify({ 
        prompt, 
        combinedHistory: history, 
        fileDocumentId: this.fileDocumentId, 
        menuSelected 
      }),
      headers: { 'Content-Type': 'application/json' },
      signal
    })

    if (!response.ok) {
      if (response.status === 413) {
        throw new Error('Request too large. Please reduce your message size and try again.')
      } else if (response.status === 404) {
        throw new Error('AI service not found. Please check your connection and try again.')
      }
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response
  }

  private handleMetadata(chunk: string): boolean {
    try {
      const metadata = JSON.parse(chunk) as ChatMetadata
      this.state.setPageContent(metadata.pageContent || '')
      this.state.setPageNumber(metadata.pageNumber || null)
      this.state.setDocTitle(metadata.pageTitle || 'Unknown Document')
      return true
    } catch (error) {
      console.error('Error parsing metadata:', error)
      return false
    }
  }

  private updateStreamingResponse(text: string, aiMessageId: string): void {
    this.state.setStreamingText(text)
    this.updateAIMessage(aiMessageId, text)
  }

  private updateAIMessage(aiMessageId: string, text: string): void {
    this.state.setMessages((prevMessages) => {
      const updatedMessages = [...prevMessages]
      const aiMessageIndex = updatedMessages.findIndex((msg) => msg.id === aiMessageId)
      if (aiMessageIndex > -1) {
        updatedMessages[aiMessageIndex] = {
          ...updatedMessages[aiMessageIndex],
          text,
          stopped: false
        }
      }
      return updatedMessages
    })
  }

  private handleAIResponseError(error: unknown): void {
    console.error('Error fetching AI response:', error)
    
    if (error instanceof DOMException && error.name === 'AbortError') {
      this.state.setStreamingText('AI response generation was stopped.')
    } else if (error instanceof Error) {
      this.state.setError(error.message)
      this.state.setStreamingText('An error occurred while fetching the AI response. Please try again.')
    } else {
      this.state.setError('An unknown error occurred while fetching the AI response.')
      this.state.setStreamingText('An unknown error occurred. Please try again.')
    }
    
    this.state.setStreamingMessageId(null)
  }
}

export default ChatAreaTools;