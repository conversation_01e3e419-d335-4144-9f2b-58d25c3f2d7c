"use client"

import React, { useEffect, useState } from "react";
import { DownloadIcon, ExpandIcon } from "lucide-react";
import { collection, query, where, getDocs, orderBy } from "firebase/firestore";
import { db } from "components/firebase";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation"; // Import useRouter from next/navigation
import { TrashIcon } from "@heroicons/react/24/outline"; // For delete button

interface FileData {
  id: string;
  name: string;
  category: string | null;
  scenario: string | null;
  chatCount: number;
  createdAt: string;
  downloadUrl: string;
}

const FileStructure: React.FC = () => {
  const [files, setFiles] = useState<FileData[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState<FileData | null>(null); // Store the file to be deleted
  const [isProcessing, setIsProcessing] = useState(false); // State for processing
  const [deletefiledialog, setDeletefiledialog] = useState(''); // Fix: Make delete dialog stateful
  const { data: session } = useSession();
  const router = useRouter(); // Initialize router

  // Fetch the file data and associated chats for each file
  const fetchFiles = async () => {
    if (!session?.user?.email) return;

    const userEmail = session.user.email;
    const filesCollection = collection(db, "users", userEmail, "files");
    const filesQuery = query(filesCollection, orderBy("createdAt", "desc"));
    const filesSnapshot = await getDocs(filesQuery);

    const fileData = await Promise.all(
      filesSnapshot.docs.map(async (doc) => {
        const file = doc.data();

        // Fetch all chats that reference this file by fileDocumentId
        const chatsQuery = query(collection(db, "users", userEmail, "chats"), where("fileDocumentId", "==", doc.id));
        const chatsSnapshot = await getDocs(chatsQuery);

        // Format the date to a 24-hour format (e.g., "28/08/2024, 17:16:32")
        const createdAt = file.createdAt?.toDate().toISOString().replace("T", " ").substring(0, 19).replace(/-/g, "/");

        return {
          id: doc.id, // Use doc.id to get the document ID
          name: file.name,
          category: file.category || "Home",
          scenario: file.scenario || "N/A", // Default to 'N/A' if no scenario
          chatCount: chatsSnapshot.size, // Number of distinct chats associated with the document
          createdAt, // Use formatted date
          downloadUrl: file.downloadUrl || "N/A",
        };
      })
    );

    setFiles(fileData);
  };

  useEffect(() => {
    fetchFiles();
  }, [session]);

  // Handle delete click, show confirmation modal
  const handleDeleteClick = (file: FileData) => {
    setSelectedFile(file);
    setShowModal(true);
  };

  // Confirm delete and call the delete API
  const handleConfirmDelete = async () => {
    if (selectedFile && session?.user?.email) {
      try {
        setIsProcessing(true); // Start processing
        setDeletefiledialog(`File: ${selectedFile.name} is being removed`); // Update dialog state

        const response = await fetch('/api/deleteDocumentAndChats', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            userId: session.user.email,
            namespace: selectedFile.id, // Using the file ID as the namespace
          }),
        });

        if (response.ok) {
          // Re-fetch the files after deletion
          await fetchFiles(); // Re-fetch the data from Firestore
        } else {
          console.error('Failed to delete file:', await response.json());
        }
      } catch (error) {
        console.error('Error deleting file:', error);
      } finally {
        setIsProcessing(false); // End processing
        setShowModal(false);
        setSelectedFile(null);
      }
    }
  };

  // Cancel deletion process
  const handleCancelDelete = () => {
    setShowModal(false);
    setSelectedFile(null);
  };

  return (
    <div className="p-2 ml-3">
      {/* Processing Spinner */}
      {isProcessing && (
        <div className="flex justify-center items-center h-screen bg-transparent">
          <p className="text-sm text-red-600 font-semibold mr-10">{deletefiledialog}</p>
          <ExpandIcon className="animate-spin w-12 h-12 text-gray-500" />
        </div>
      )}

      {/* Header Row */}
      {!isProcessing && (
        <div>
          <div className="flex mb-2 font-bold">
            <div className="flex-1 min-w-[20px] pr-5 mr-25 text-left">Category</div>
            <div className="flex-1 min-w-[250px] pr-10 text-left">Document</div>
            <div className="flex-1 min-w-[80px] ml-10 mr-40 text-left">Scenario</div>
            <div className="flex-1 min-w-[100px] mr-2 text-left">Chats</div>
            <div className="flex-1 min-w-[160px] mr-10 -ml-10 text-left">Created</div>
            <div className="flex-1 min-w-[80px] text-center">Download</div>
            <div className="flex-1 min-w-[80px] text-center">Delete</div> {/* New Delete column */}
          </div>

          {/* File Rows */}
          {files.map((file) => (
            <div key={file.id} className="flex mb-2 text-sm">
              <div className="flex-1 min-w-[20px] pr-5 mr-25 text-left">{file.category}</div>
              <div className="flex-1 min-w-[250px] pr-10 text-left">
                {file.name.length > 35 ? `${file.name.substring(0, 35)}...` : file.name}
              </div>
              <div className="flex-1 min-w-[80px] ml-10 mr-40 text-left">{file.scenario}</div>
              <div className="flex-1 min-w-[100px] mr-2 text-left">{file.chatCount}</div>
              <div className="flex-1 min-w-[160px] mr-10 -ml-10 text-left whitespace-nowrap">{file.createdAt}</div>
              <div className="flex-1 min-w-[80px] text-center">
                {file.downloadUrl !== "N/A" ? (
                  <a href={file.downloadUrl} target="_blank" rel="noopener noreferrer">
                    <button className="hover:bg-gray-300 py-2 px-2 mb-1 flex items-center">
                      <DownloadIcon className="h-4 w-4" />
                    </button>
                  </a>
                ) : (
                  "No download available"
                )}
              </div>
              <div className="flex-1 min-w-[80px] text-center">
                <button onClick={() => handleDeleteClick(file)} className="hover:bg-gray-300 py-2 px-2 mb-1 flex items-center">
                  <TrashIcon className="h-4 w-4 text-red-600 hover:text-red-800" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Confirmation Modal */}
      {showModal && selectedFile && !isProcessing && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white text-gray-700 p-4 rounded shadow-lg">
            <p>Are you sure you want to delete this document and associated chats?</p>
            <div className="mt-4">
              <p><strong>Document:</strong> {selectedFile.name}</p>
              <p><strong>Date Created:</strong> {selectedFile.createdAt}</p>
            </div>
            <div className="mt-4 flex justify-end space-x-2">
              <button onClick={handleConfirmDelete} className="px-4 py-2 bg-red-500 text-white rounded">
                Yes
              </button>
              <button onClick={handleCancelDelete} className="px-4 py-2 bg-blue-500 text-white rounded">
                No
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FileStructure;
