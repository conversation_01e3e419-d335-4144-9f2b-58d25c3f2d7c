// app/api/questionnaire/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from 'components/firebase';
import { QuestionnaireSubmission } from 'types/questionnaire';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { responses } = body;

    if (!responses) {
      return NextResponse.json(
        { error: 'Questionnaire responses are required' },
        { status: 400 }
      );
    }

    // Prepare submission data
    const submission: Omit<QuestionnaireSubmission, 'id'> = {
      userId: session.user.email || 'unknown',
      userEmail: session.user.email || '',
      userName: session.user.name || '',
      responses,
      timestamp: new Date(),
      metadata: {
        userAgent: request.headers.get('user-agent') || 'unknown',
        ipAddress: request.headers.get('x-forwarded-for') || 
                  request.headers.get('x-real-ip') || 
                  'unknown',
        sessionId: session.user.email || 'unknown'
      }
    };

    // Save to Firebase Firestore
    const docRef = await addDoc(collection(db, 'comments'), {
      ...submission,
      timestamp: serverTimestamp()
    });

    console.log('Questionnaire submitted via API with ID:', docRef.id);

    return NextResponse.json(
      { 
        success: true, 
        id: docRef.id,
        message: 'Questionnaire submitted successfully' 
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Error submitting questionnaire via API:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to submit questionnaire' 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // This endpoint could be used to retrieve user's previous submissions
    // For now, just return a success message
    return NextResponse.json(
      { 
        message: 'Questionnaire API is working',
        user: session.user.email 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error in questionnaire API GET:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}
