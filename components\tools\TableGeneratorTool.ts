// components/tools/TableGeneratorTool.ts
import <PERSON> from 'papaparse';
import _ from 'lodash';

interface TableConfiguration {
  data: any[];
  options: {
    maxRows?: number;
    maxColumns?: number;
    groupBy?: string[];
    formatting?: Record<string, any>;
    aggregations?: Array<{
      column: string;
      function: AggregationType;
    }>;
  };
}

interface TableResponse {
  success: boolean;
  error?: string;
  config?: {
    columns: Column[];
    data: any[];
    summary?: string;
    metadata: {
      totalRows: number;
      visibleRows: number;
      totalColumns: number;
    };
  };
}

type DataType = 'text' | 'number' | 'date' | 'currency' | 'percentage';
type AggregationType = 'sum' | 'average' | 'count' | 'min' | 'max';

interface Column {
  field: string;
  header: string;
  dataType: DataType;
  format?: string;
  width?: number;
}

export class TableGeneratorTool {
  static description = `
    TableGeneratorTool creates optimized table configurations based on data characteristics
    and formatting requirements. It supports:
    
    1. Data Processing:
       - Automatic column type inference
       - Smart data formatting
       - Handling of missing values
       - Dynamic column width calculation
       - Support for CSV parsing
    
    2. Data Types:
       - Text (strings)
       - Numbers (integers and decimals)
       - Dates
       - Currency
       - Percentages
    
    3. Features:
       - Automatic header formatting
       - Column width optimization
       - Summary statistics generation
       - Row limiting
       - Column limiting
       - Data aggregation
       - Grouping support
    
    4. Formatting:
       - Currency formatting with locale support
       - Percentage formatting
       - Date localization
       - Number formatting with configurable decimals
       - Header case transformation
    
    Usage:
    const config = await tableTool.call(JSON.stringify({
      data: yourData,
      options: {
        maxRows: 20,
        maxColumns: 10,
        groupBy: ['category'],
        aggregations: [
          { column: 'sales', function: 'sum' }
        ]
      }
    }));
  `;

  private readonly DEFAULT_MAX_ROWS = 20;
  private readonly DEFAULT_MAX_COLUMNS = 10;
  private readonly DEFAULT_COLUMN_WIDTH = 30;

  async call(input: string): Promise<TableResponse> {
    try {
      const config = JSON.parse(input) as TableConfiguration;
      
      if (!this.validateConfig(config)) {
        throw new Error('Invalid table configuration');
      }

      const processedConfig = await this.generateTableConfig(config);
      
      return {
        success: true,
        config: processedConfig
      };

    } catch (error) {
      console.error('TableGeneratorTool error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  private validateConfig(config: TableConfiguration): boolean {
    if (!config.data || !Array.isArray(config.data) || config.data.length === 0) {
      return false;
    }

    if (config.options?.maxRows && typeof config.options.maxRows !== 'number') {
      return false;
    }

    if (config.options?.maxColumns && typeof config.options.maxColumns !== 'number') {
      return false;
    }

    return true;
  }

  private async generateTableConfig(config: TableConfiguration) {
    const options = {
      maxRows: config.options?.maxRows || this.DEFAULT_MAX_ROWS,
      maxColumns: config.options?.maxColumns || this.DEFAULT_MAX_COLUMNS,
      ...config.options
    };

    const columns = this.inferColumns(config.data, options);
    const processedData = this.processData(config.data, columns, options);
    const summary = this.generateSummaryStats(processedData, columns);

    return {
      columns,
      data: processedData,
      summary,
      metadata: {
        totalRows: config.data.length,
        visibleRows: processedData.length,
        totalColumns: columns.length
      }
    };
  }

  private inferColumns(data: any[], options: TableConfiguration['options']): Column[] {
    const sampleRow = data[0];
    const columnFields = Object.keys(sampleRow).slice(0, options.maxColumns);

    return columnFields.map(field => ({
      field,
      header: this.formatHeaderName(field),
      dataType: this.inferDataType(data, field),
      width: this.calculateColumnWidth(data, field)
    }));
  }

  private inferDataType(data: any[], field: string): DataType {
    const values = data.map(row => row[field]).filter(val => val != null);
    if (!values.length) return 'text';

    const sample = values[0];
    if (typeof sample === 'number') {
      if (this.isCurrency(values)) return 'currency';
      if (this.isPercentage(values)) return 'percentage';
      return 'number';
    }
    if (this.isDate(sample)) return 'date';
    return 'text';
  }

  private isCurrency(values: any[]): boolean {
    return values.every(v => 
      typeof v === 'number' && 
      Math.abs(v) >= 0.01 && 
      Math.round(v * 100) / 100 === v
    );
  }

  private isPercentage(values: any[]): boolean {
    return values.every(v => 
      typeof v === 'number' && 
      v >= 0 && 
      v <= 100 && 
      Math.round(v * 100) / 100 === v
    );
  }

  private isDate(value: any): boolean {
    if (typeof value !== 'string') return false;
    const date = new Date(value);
    return date instanceof Date && !isNaN(date.getTime());
  }

  private calculateColumnWidth(data: any[], field: string): number {
    const headerLength = this.formatHeaderName(field).length;
    const maxDataLength = Math.max(
      ...data.map(row => String(row[field] || '').length)
    );
    return Math.min(
      Math.max(headerLength, maxDataLength) + 2,
      this.DEFAULT_COLUMN_WIDTH
    );
  }

  private processData(
    data: any[], 
    columns: Column[], 
    options: TableConfiguration['options']
  ): any[] {
    let processedData = data.slice(0, options.maxRows);

    if (options.groupBy && options.groupBy.length > 0) {
      processedData = this.aggregateData(processedData, options);
    }

    return processedData.map(row => {
      const formattedRow: Record<string, any> = {};
      columns.forEach(column => {
        formattedRow[column.field] = this.formatValue(row[column.field], column.dataType);
      });
      return formattedRow;
    });
  }

  private aggregateData(data: any[], options: TableConfiguration['options']): any[] {
    if (!options.groupBy || !options.aggregations) return data;

    return _.chain(data)
      .groupBy(row => options.groupBy!.map(field => row[field]).join('_'))
      .map(group => {
        const result: Record<string, any> = {};
        
        // Add groupBy fields
        options.groupBy!.forEach(field => {
          result[field] = group[0][field];
        });

        // Add aggregations
        options.aggregations!.forEach(agg => {
          const values = group.map(row => row[agg.column]);
          switch (agg.function) {
            case 'sum':
              result[agg.column] = _.sum(values);
              break;
            case 'average':
              result[agg.column] = _.mean(values);
              break;
            case 'count':
              result[agg.column] = values.length;
              break;
            case 'min':
              result[agg.column] = _.min(values);
              break;
            case 'max':
              result[agg.column] = _.max(values);
              break;
          }
        });

        return result;
      })
      .value();
  }

  private formatValue(value: any, dataType: DataType): string {
    if (value == null) return '';

    switch (dataType) {
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 2
        }).format(value);

      case 'percentage':
        return new Intl.NumberFormat('en-US', {
          style: 'percent',
          minimumFractionDigits: 1,
          maximumFractionDigits: 1
        }).format(value / 100);

      case 'date':
        return new Date(value).toLocaleDateString();

      case 'number':
        return new Intl.NumberFormat('en-US', {
          maximumFractionDigits: 2
        }).format(value);

      default:
        return String(value);
    }
  }

  private formatHeaderName(field: string): string {
    return field
      .split(/(?=[A-Z])|_|-/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  private generateSummaryStats(data: any[], columns: Column[]): string {
    if (!data.length) return 'No data available for summary';

    const stats: string[] = [];

    columns.forEach(column => {
      if (['number', 'currency', 'percentage'].includes(column.dataType)) {
        const values = data.map(row => {
          const rawValue = row[column.field];
          return typeof rawValue === 'string' 
            ? parseFloat(rawValue.replace(/[^0-9.-]+/g, ''))
            : rawValue;
        });

        stats.push(`${column.header}:`);
        stats.push(`Average: ${this.formatValue(_.mean(values), column.dataType)}`);
        stats.push(`Min: ${this.formatValue(_.min(values), column.dataType)}`);
        stats.push(`Max: ${this.formatValue(_.max(values), column.dataType)}`);
        stats.push('');
      }
    });

    return stats.join('\n');
  }
}