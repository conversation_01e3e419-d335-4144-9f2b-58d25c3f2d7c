import { NextRequest, NextResponse } from 'next/server'

/**
 * ElevenLabs webhook endpoint for server tools
 * This can be used as a webhook URL for server tools in ElevenLabs
 */
export async function POST(request: NextRequest) {
  try {
    console.log('[ELEVENLABS_WEBHOOK] 📥 Received webhook call')
    
    // Parse the request body
    const body = await request.json()
    console.log('[ELEVENLABS_WEBHOOK] Request body:', JSON.stringify(body, null, 2))
    
    // Extract tool information
    const { tool_name, parameters, conversation_id, agent_id } = body
    
    console.log('[ELEVENLABS_WEBHOOK] Tool details:', {
      tool_name,
      parameters,
      conversation_id,
      agent_id
    })

    // Handle different tool calls
    switch (tool_name) {
      case 'switch_to_script_tab':
        console.log('[ELEVENLABS_WEBHOOK] 🎬 Handling script tab switch tool')
        
        const isReady = parameters?.ready === true || parameters?.user_ready === true
        
        if (isReady) {
          console.log('[ELEVENLABS_WEBHOOK] ✅ User confirmed ready for script tab switch')
          
          return NextResponse.json({
            success: true,
            message: "Script tab switch initiated successfully",
            action: "switch_to_script_tab",
            timestamp: new Date().toISOString()
          })
        } else {
          console.warn('[ELEVENLABS_WEBHOOK] ⚠️ Ready parameter not confirmed:', parameters)
          
          return NextResponse.json({
            success: false,
            message: "User readiness was not confirmed in the tool parameters",
            received_parameters: parameters
          })
        }

      case 'get_script_info':
        console.log('[ELEVENLABS_WEBHOOK] 📄 Handling script info request')
        
        // This could return information about the current script
        return NextResponse.json({
          success: true,
          script_info: {
            title: "Current Script",
            characters: ["CHARACTER1", "CHARACTER2"],
            scene_count: 5,
            status: "loaded"
          }
        })

      default:
        console.warn('[ELEVENLABS_WEBHOOK] ❓ Unknown tool called:', tool_name)
        
        return NextResponse.json({
          success: false,
          message: `Unknown tool: ${tool_name}`,
          available_tools: ['switch_to_script_tab', 'get_script_info']
        })
    }

  } catch (error) {
    console.error('[ELEVENLABS_WEBHOOK] ❌ Error processing webhook:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * Handle GET requests for webhook verification
 */
export async function GET(request: NextRequest) {
  console.log('[ELEVENLABS_WEBHOOK] 🔍 Webhook verification request')
  
  return NextResponse.json({
    status: 'active',
    webhook_url: request.url,
    supported_tools: ['switch_to_script_tab', 'get_script_info'],
    timestamp: new Date().toISOString()
  })
}
