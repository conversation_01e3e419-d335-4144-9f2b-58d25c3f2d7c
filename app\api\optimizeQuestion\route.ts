// app/api/optimizeQuestion/route.ts
import { ChatGroq } from "@langchain/groq";
import { createGroqClient } from "../../../lib/llms/groq";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { text } = await request.json();

    // Initialize Groq client
    const groqClient = createGroqClient({ userEmail: session.user.email });

    const model = new ChatGroq({
      temperature: 0.2,
      model: process.env.GROQ_MODEL,
      apiKey: groqClient.apiKey!,
    });

    const response = await model.invoke([
      {
        role: "system",
        content: `You are an expert at optimizing questions for document querying.
        Rephrase the given text to be more precise and effective for retrieving
        relevant information. Maintain the core intent but make it more specific and searchable.`
    },
      {
        role: "user",
        content: text
      }
    ]);

    return NextResponse.json({ optimizedText: response.content });

  } catch (error) {
    console.error('Error optimizing question:', error);
    return NextResponse.json(
      { message: 'Error optimizing question' },
      { status: 500 }
    );
  }
}