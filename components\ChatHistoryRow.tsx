import React, { useState, useEffect } from 'react'
import { ChatBubbleLeftIcon } from '@heroicons/react/24/outline'
import { ChevronDown, ChevronRight, Loader2 } from 'lucide-react'
import DeleteChatRow from './DeleteChatRow'
import { isToday, isYesterday, isWithinInterval, subDays, format } from 'date-fns'

interface ChatData {
  id: string
  firstMessage: string
  createdAt: number
}

interface GroupedChats {
  [key: string]: ChatData[]
}

interface Props {
  chats: { id: string; firstMessage: string; createdAt: number; }[];
  selectedChatId: string | null;
  onSelectChat: (id: string | null) => Promise<void>;
  onDeleteChat: (id: string) => Promise<void>;
  menuName: string;
  isLoading: boolean;
  // ... other props

}

// Define the order of groups
const GROUP_ORDER = [
  'Today',
  'Yesterday',
  'Last 7 days',
  'Previous 30 days',
  'Over a month'
]

export default function ChatHistoryRow({
  chats,
  selectedChatId,
  onSelectChat,
  onDeleteChat,
  menuName,
  isLoading = false,
}: Props) {
  const [groupedChats, setGroupedChats] = useState<GroupedChats>({})
  const [expandedGroups, setExpandedGroups] = useState<{ [key: string]: boolean }>({})

  useEffect(() => {
    const grouped = chats.reduce((acc: GroupedChats, chat) => {
      const date = new Date(chat.createdAt)
      let group = ''

      if (isToday(date)) {
        group = 'Today'
      } else if (isYesterday(date)) {
        group = 'Yesterday'
      } else if (isWithinInterval(date, { start: subDays(new Date(), 7), end: subDays(new Date(), 2) })) {
        group = 'Last 7 days'
      } else if (isWithinInterval(date, { start: subDays(new Date(), 30), end: subDays(new Date(), 8) })) {
        group = 'Previous 30 days'
      } else {
        group = 'Over a month'
      }

      if (!acc[group]) {
        acc[group] = []
      }
      acc[group].push(chat)
      return acc
    }, {})

    setGroupedChats(grouped)
    
    // Set default state for filter toggles
    const defaultExpandedState = Object.keys(grouped).reduce((acc, group) => ({
      ...acc,
      [group]: group === 'Today'
    }), {})
    setExpandedGroups(defaultExpandedState)
  }, [chats])

  const toggleGroup = (group: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [group]: !prev[group]
    }))
  }

  // Sort groups according to GROUP_ORDER
  const sortedGroups = Object.entries(groupedChats).sort((a, b) => {
    return GROUP_ORDER.indexOf(a[0]) - GROUP_ORDER.indexOf(b[0])
  })

  return (
    <div className="space-y-1">
      {sortedGroups.map(([group, groupChats]) => (
        <div key={group} className="space-y-0.5">
          <button
            onClick={() => toggleGroup(group)}
            className="flex items-center gap-1 px-1 py-0.5 text-xs font-medium text-muted-foreground hover:text-foreground"
          >
            {expandedGroups[group] ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
            {group}
          </button>
          
          {expandedGroups[group] && groupChats.map(chat => (
            <div
              key={chat.id}
              onClick={() => onSelectChat(chat.id)} // Pass only chat.id
              className={`flex items-center p-1 space-x-2 cursor-pointer rounded-lg transition-all duration-200 ease-out ${
                selectedChatId === chat.id
                  ? 'bg-ike-purple bg-opacity-50'
                  : 'hover:bg-gray-700 hover:bg-opacity-50 hover:text-gray-400'
              }`}
            >
              {isLoading && selectedChatId === chat.id ? (
                <Loader2 className="h-4 w-4 text-white animate-spin" />
              ) : (
                <ChatBubbleLeftIcon className="h-4 w-4 text-green-600" />
              )}
              <div className="flex flex-col flex-1">
                <p className="text-white">{chat.firstMessage?.substring(0, 20) || 'No messages yet'}</p>
                <span className="text-xs">{format(chat.createdAt, 'MMM d, yyyy HH:mm')}</span>
              </div>
              <DeleteChatRow
                id={chat.id}
                message={chat.firstMessage}
                dateCreated={format(chat.createdAt, 'MMM d, yyyy HH:mm')}
                onDelete={() => onDeleteChat(chat.id)}
              />
            </div>
          ))}
        </div>
      ))}
    </div>
  )
}