export const SUPPORTED_IMAGE_TYPES = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp'
  ] as const;
  
  export const SUPPORTED_DOCUMENT_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'application/rtf',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv'
  ] as const;
  
  export type SupportedImageType = typeof SUPPORTED_IMAGE_TYPES[number];
  export type SupportedDocumentType = typeof SUPPORTED_DOCUMENT_TYPES[number];
  export type SupportedFileType = SupportedImageType | SupportedDocumentType;
  
  export const MAX_IMAGE_SIZE = 4 * 1024 * 1024; // 4MB
  export const MAX_DOCUMENT_SIZE = 50 * 1024 * 1024; // 50MB
  
  export enum StatusText {
    IDLE = 'IDLE',
    UPLOADING = 'UPLOADING',
    PROCESSING = 'PROCESSING',
    GENERATING = 'GENERATING',
    UPLOADED = 'UPLOADED',
    SAVING = 'SAVING',
    COMPLETED = 'COMPLETED',
    ERROR = 'ERROR'
  }