// components/DocViewer/FileTable.tsx
import React from 'react';
import { FileData } from './FileManagerstructure'; // Adjusted import to use named export
import { Folder, File, Download, Trash, ChevronDown, ChevronRight } from 'lucide-react';
import { Timestamp } from '@google-cloud/firestore';

// Updated the type references to use the correct interface
interface FileTableProps {
  files: FileData[]; // Changed to use the correct type reference
  expandedCategory: string | null;
  toggleCategory: (category: string) => void;
  handleDeleteClick: (file: FileData) => void; // Changed to use the correct type reference
  showSearchResults: boolean;
  loading: boolean;
  filteredFiles: FileData[]; // Changed to use the correct type reference
  groupedFiles: { category: string; files: FileData[]; isExpanded: boolean }[]; // Changed to use the correct type reference
}

// Added the formatTimestamp function
const formatTimestamp = (timestamp: Timestamp | undefined): string => {
  if (!timestamp || !(timestamp instanceof Timestamp)) {
    return 'N/A';
  }
  const date = timestamp.toDate();
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
};

const FileTable: React.FC<FileTableProps> = ({
  toggleCategory,
  handleDeleteClick,
  showSearchResults,
  groupedFiles,
}) => {
  return (
    <div className={`transition-all duration-300 ease-in-out ${showSearchResults ? 'w-[75%]' : 'w-full'}`}>
      <div className="bg-slate-800 rounded-lg overflow-hidden w-full">
        <div className="grid grid-cols-6 gap-4 p-2 text-white">
          <div className="col-span-2"></div>
          <div className="col-span-1">Files</div>
          <div>Chats</div>
          <div>Created Date</div>
          <div>Type</div>
        </div>

        <div className="divide-y divide-gray-700">
          {groupedFiles.map(({ category, files, isExpanded }) => (
            <div key={category}>
              <div className="grid grid-cols-6 gap-4 p-1 text-xs text-left ml-2 mr-2 rounded-xl bg-slate-700 bg-opacity-65 text-white items-center mb-1 hover:bg-slate-600">
                <div className="col-span-2 flex items-center">
                  <Folder className="inline mr-2 text-amber-500" />
                  {category}
                </div>
                <div className="flex items-center justify-center">{files.length}</div>
                <div className="flex items-center justify-center">{files.reduce((sum, file) => sum + file.chatCount, 0)}</div>
                <div></div>
                <div className="flex items-center justify-between">
                  <span></span>
                  <button
                    onClick={() => toggleCategory(category)}
                    className="focus:outline-none"
                    aria-label={isExpanded ? `Collapse ${category}` : `Expand ${category}`}
                  >
                    {isExpanded ? (
                      <ChevronDown className="h-5 w-5 text-amber-500" />
                    ) : (
                      <ChevronRight className="h-5 w-5 text-amber-500" />
                    )}
                  </button>
                </div>
              </div>
              {isExpanded && files.map(file => (
                <div key={file.id} className="grid grid-cols-6 text-xs gap-4 justify-center bg-gray-800 rounded-md bg-opacity-65 mb-1 border-b-2 border-gray-900 p-1 mr-2 ml-4 text-blue-200 hover:bg-slate-900 transition-colors duration-200 ease-in-out">
                  <div className="col-span-2 font-normal flex items-center">
                    <File className="h-4 w-4 inline mr-4 text-blue-400" />
                    <span className="hover:text-blue-400 transition-colors text-left duration-200 ease-in-out">{file.name}</span>
                  </div>
                  <div className="flex items-center">
                    <a href={file.downloadUrl} download className="mr-2 hover:text-blue-400 transition-colors duration-200 ease-in-out">
                      <Download className="h-4 w-4 ml-9" />
                    </a>
                  </div>
                  <div className="hover:text-blue-400 transition-colors duration-200 ease-in-out">{file.chatCount}</div>
                  <div className="hover:text-blue-400 transition-colors duration-200 ease-in-out">{formatTimestamp(file.createdAt)}</div>
                  <div className="hover:text-blue-400 transition-colors duration-200 ease-in-out">{file.type}</div>
                  <Trash className="inline mr-2 h-4 w-4 cursor-pointer text-red-600" onClick={() => handleDeleteClick(file)} />
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FileTable;
