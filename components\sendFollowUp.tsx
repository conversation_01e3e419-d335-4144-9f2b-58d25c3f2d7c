// sendFollowUp.tsx
import { API_CONFIG } from './Config';

// sendFollowUp.tsx
export async function sendFollowUp(data: string) {
    try {
        // Prepare the payload as per the API format
        const payload = { question: data };

        const response = await fetch(API_CONFIG.endpoint , {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(payload) // Send the payload as JSON
        });

        if (!response.ok) {
            throw new Error(`Error: ${response.status}`);
          }
      
          // Return the full JSON response
          const jsonResponse = await response.json();
          console.log('API Response:', jsonResponse); // Add this line

          return jsonResponse;
        } catch (error) {
          console.error('Error in sendFollowUp:', error);
          return null; // Handle error case by returning null or an empty object
        }
    
    }

