import { motion, AnimatePresence } from "framer-motion";
import { RefObject, useState, useEffect, useRef } from "react";
import { Play, Pause, Volume2 } from "lucide-react";
import EnhancedMarkdown from "./EnhancedMarkdownContent"; // Update the import path as needed

interface ChatMessage {
  id?: string;
  role: "user" | "assistant";
  content: string;
  timestamp: string;
  audioUrl?: string;
}

interface ChatMessagesProps {
  chatMessages: ChatMessage[];
  error: string | null;
  isLoading: boolean;
  isStreaming: boolean;
  messagesEndRef: RefObject<HTMLDivElement>;
}


export function ChatMessages({ chatMessages, error, isLoading, isStreaming, messagesEndRef }: ChatMessagesProps) {
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);
  const [audioElements, setAudioElements] = useState<{[key: string]: HTMLAudioElement}>({});

  const prevMessagesCountRef = useRef<number>(0);
  const [waitingForResponse, setWaitingForResponse] = useState<boolean>(false);
  const lastUserMessageRef = useRef<{timestamp: string} | null>(null);
  const waitingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const trackNewUserMessage = (messages: ChatMessage[]) => {
    if (messages.length <= prevMessagesCountRef.current) {
      prevMessagesCountRef.current = messages.length;
      return;
    }

    const lastMessage = messages[messages.length - 1];

    if (lastMessage.role === "user") {
      console.log("New user message detected, entering waiting state");

      if (waitingTimeoutRef.current) {
        clearTimeout(waitingTimeoutRef.current);
        waitingTimeoutRef.current = null;
      }

      lastUserMessageRef.current = {
        timestamp: lastMessage.timestamp
      };

      setWaitingForResponse(true);

      waitingTimeoutRef.current = setTimeout(() => {
        console.log("Safety timeout reached, clearing waiting state");
        setWaitingForResponse(false);
        waitingTimeoutRef.current = null;
      }, 60000);
    }
    else if (lastMessage.role === "assistant" && lastUserMessageRef.current) {
      console.log("Assistant response received, clearing waiting state");

      setWaitingForResponse(false);
      lastUserMessageRef.current = null;

      if (waitingTimeoutRef.current) {
        clearTimeout(waitingTimeoutRef.current);
        waitingTimeoutRef.current = null;
      }
    }

    prevMessagesCountRef.current = messages.length;
  };

  useEffect(() => {
    trackNewUserMessage(chatMessages);

    return () => {
      if (waitingTimeoutRef.current) {
        clearTimeout(waitingTimeoutRef.current);
      }
    };
  }, [chatMessages]);

  const shouldShowLoadingIndicator =
    (isLoading &&
     !isStreaming &&
     chatMessages.length > 0 &&
     chatMessages[chatMessages.length - 1].role === "user") ||
    waitingForResponse;

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [chatMessages, messagesEndRef]);

  useEffect(() => {
    const newAudioElements: {[key: string]: HTMLAudioElement} = {};

    chatMessages.forEach((message, index) => {
      if (message.role === "assistant" && message.audioUrl) {
        const messageKey = message.id || `${message.role}-${index}`;

        if (!audioElements[messageKey]) {
          const audio = new Audio(message.audioUrl);
          audio.onended = () => setPlayingAudio(null);
          newAudioElements[messageKey] = audio;
        } else {
          newAudioElements[messageKey] = audioElements[messageKey];
        }
      }
    });

    Object.values(audioElements).forEach(audio => {
      if (!Object.values(newAudioElements).includes(audio)) {
        audio.pause();
        audio.src = "";
      }
    });

    setAudioElements(newAudioElements);

    return () => {
      Object.values(newAudioElements).forEach(audio => {
        audio.pause();
        audio.src = "";
      });
    };
  }, [chatMessages]);

  const toggleAudio = (messageKey: string) => {
    const audio = audioElements[messageKey];
    if (!audio) return;

    if (playingAudio === messageKey) {
      audio.pause();
      setPlayingAudio(null);
    } else {
      if (playingAudio && audioElements[playingAudio]) {
        audioElements[playingAudio].pause();
      }

      audio.currentTime = 0;
      audio.play().catch(e => console.error("Error playing audio:", e));
      setPlayingAudio(messageKey);
    }
  };

  useEffect(() => {
    if (chatMessages.length === 0) return;

    const latestMessage = chatMessages[chatMessages.length - 1];
    if (latestMessage?.role === "assistant" && latestMessage.audioUrl) {
      const messageKey = latestMessage.id || `${latestMessage.role}-${chatMessages.length - 1}`;

      if (audioElements[messageKey]) {
        setTimeout(() => {
          const playPromise = audioElements[messageKey].play();
          if (playPromise !== undefined) {
            playPromise
              .then(() => setPlayingAudio(messageKey))
              .catch(e => console.error("Error auto-playing audio:", e));
          }
        }, 300);
      }
    }
  }, [chatMessages, audioElements]);

  const handleMarkdownItemClick = (text: string) => {
    console.log("List item clicked:", text);
  };

  const handleCopyCode = (code: string) => {
    console.log("Code copied:", code);
    navigator.clipboard.writeText(code);
  };

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4">
      {error && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="w-full p-3 bg-red-500/20 border border-red-500/50 text-white rounded-lg"
        >
          <p className="text-sm font-medium">Error: {error}</p>
        </motion.div>
      )}

      {chatMessages.length === 0 && (
        <div className="text-gray-400 text-sm text-center">Start a conversation with your Castmate Tutor</div>
      )}

      {chatMessages.map((message, index) => {
        const messageKey = message.id || `${message.role}-${index}`;
        const hasAudio = message.role === "assistant" && message.audioUrl;

        return (
          <motion.div
            key={messageKey}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
          >
            <div
              className={`max-w-[80%] p-3 ${
                message.role === "user" ? "bg-blue-600 dark:bg-purple-600" : "bg-gray-100 dark:bg-black/30"
              } ${
                message.role === "user" ? "text-white" : "text-gray-900 dark:text-white"
              } rounded-2xl shadow-lg transition-colors duration-300`}
            >
              {message.role === "assistant" ? (
                <EnhancedMarkdown
                  content={message.content}
                  onItemClick={handleMarkdownItemClick}
                  onCopyCode={handleCopyCode}
                />
              ) : (
                <div dangerouslySetInnerHTML={{ __html: message.content.replace(/\n/g, '<br>') }} />
              )}

              {hasAudio && (
                <div className="mt-2 flex items-center border-t border-white/10 pt-2">
                  <button
                    onClick={() => toggleAudio(messageKey)}
                    className={`p-2 rounded-full ${
                      playingAudio === messageKey ? "bg-red-500" : "bg-purple-500"
                    } hover:opacity-90 transition-colors`}
                  >
                    {playingAudio === messageKey ? (
                      <Pause className="w-4 h-4" />
                    ) : (
                      <Play className="w-4 h-4" />
                    )}
                  </button>
                  <div className="ml-2 text-xs text-gray-400 flex items-center">
                    <Volume2 className="w-3 h-3 mr-1" />
                    {playingAudio === messageKey ? "Playing audio..." : "Play audio"}
                  </div>
                </div>
              )}

              <p className="text-xs text-gray-400 mt-1 text-right">
                {new Date(message.timestamp).toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>
          </motion.div>
        );
      })}

      <AnimatePresence>
        {shouldShowLoadingIndicator && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ exit: { duration: 0.2 } }}
            className="flex justify-start"
          >
            <div className="p-3 rounded-2xl bg-gray-100 dark:bg-black/30 transition-colors duration-300">
              <div className="flex flex-col space-y-2">
                <div className="flex space-x-1">
                  <span className="w-2 h-2 bg-blue-600 dark:bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: "0ms" }}></span>
                  <span className="w-2 h-2 bg-blue-600 dark:bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: "150ms" }}></span>
                  <span className="w-2 h-2 bg-blue-600 dark:bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: "300ms" }}></span>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <div ref={messagesEndRef} />
    </div>
  );
}