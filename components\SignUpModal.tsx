'use client'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { doc, setDoc, getDoc } from 'firebase/firestore'
import { db } from '../components/firebase'
import { v4 as uuidv4 } from 'uuid'
import { useSession } from 'next-auth/react'

interface SignUpModalProps {
  onClose: () => void
}

export default function SignUpModal({ onClose }: SignUpModalProps) {
  const [firstName, setFirstName] = useState('')
  const [surname, setSurname] = useState('')
  const [email, setEmail] = useState('')
  const [city, setCity] = useState('')
  const [country, setCountry] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const { data: session } = useSession()

  const countries = [
    'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 'Japan', 'India', 
    'Brazil', 'Mexico', 'South Africa', 'China', 'Russia', 'Italy', 'Spain', 'Netherlands', 
    'Sweden', 'Norway', 'Denmark', 'Finland', 'New Zealand', 'Ireland', 'Switzerland', 'Austria'
  ]

  useEffect(() => {
    const checkExistingAccount = async () => {
      if (session?.user?.email) {
        const accountDoc = await getDoc(doc(db, 'Accounts', session.user.email))
        if (accountDoc.exists()) {
          onClose()
          router.push('/dashboard/Upload')
        } else {
          setIsLoading(false)
        }
      } else {
        setIsLoading(false)
      }
    }

    checkExistingAccount()
  }, [session, onClose, router])

  const validateForm = () => {
    if (!firstName || !surname || !email || !city || !country || !password || !confirmPassword) {
      setError('All fields are required')
      return false
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setError('Invalid email format')
      return false
    }
    if (password !== confirmPassword) {
      setError('Passwords do not match')
      return false
    }
    if (password.length < 8) {
      setError('Password must be at least 8 characters long')
      return false
    }
    return true
  }

  const resetForm = () => {
    setFirstName('')
    setSurname('')
    setEmail('')
    setCity('')
    setCountry('')
    setPassword('')
    setConfirmPassword('')
    setError('')
    setSuccess('')
  }

  const sendVerificationEmail = async (email: string, verificationToken: string) => {
    const response = await fetch('/api/send-verification-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, verificationToken }),
    })

    if (!response.ok) {
      throw new Error('Failed to send verification email')
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateForm()) return

    try {
      // Check if the account already exists
      const accountDoc = await getDoc(doc(db, 'Accounts', email))
      
      if (accountDoc.exists()) {
        setError('An account with this email already exists')
        return
      }

      const verificationToken = uuidv4()

      // Add new user to the Accounts collection
      await setDoc(doc(db, 'Accounts', email), {
        firstName,
        surname,
        email,
        city,
        country,
        createdAt: new Date().toISOString(),
        isVerified: false,
        verificationToken,
      })

      // Send verification email
      await sendVerificationEmail(email, verificationToken)

      setSuccess('Account created successfully. Please check your email to verify your account.')
      setIsSubmitted(true)
      resetForm()

      // Close the modal and redirect after a short delay
      setTimeout(() => {
        onClose()
        router.push('/')
      }, 5000)
    } catch (error) {
      if (error instanceof Error) {
        setError(`Failed to create account: ${error.message}`)
      } else {
        setError('Failed to create account. Please try again.')
      }
      console.error('Error creating account:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-ike-purple p-8 rounded-lg shadow-lg">
          <p className="text-white">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-ike-purple p-8 rounded-lg shadow-lg w-96">
        <button
          className="absolute top-2 right-2 text-rose-600 hover:text-white"
          onClick={onClose}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <div className="flex flex-col items-center mb-4">
          <Image src="/logo3.png" alt="Company Logo" width={80} height={80} />
          <h2 className="text-lg font-semibold mt-8">Create an account to get started</h2>
        </div>
        {isSubmitted ? (
          <div className="text-center">
            <p className="text-green-500 font-semibold mb-4">Account created successfully!</p>
            <p className="text-white">Please check your email to verify your account.</p>
            <p className="text-white mt-2">You will be redirected to the home page shortly.</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4 text-slate-700">
            <input
              type="text"
              placeholder="First Name"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              className="w-full px-3 py-2 border rounded"
            />
            <input
              type="text"
              placeholder="Surname"
              value={surname}
              onChange={(e) => setSurname(e.target.value)}
              className="w-full px-3 py-2 border rounded"
            />
            <input
              type="email"
              placeholder="Gmail account email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border rounded"
            />
            <input
              type="text"
              placeholder="City/Town"
              value={city}
              onChange={(e) => setCity(e.target.value)}
              className="w-full px-3 py-2 border rounded"
            />
            <select
              value={country}
              onChange={(e) => setCountry(e.target.value)}
              className="w-full px-3 py-2 border rounded"
            >
              <option value="">Select Country</option>
              {countries.map((c) => (
                <option key={c} value={c}>
                  {c}
                </option>
              ))}
            </select>
            <input
              type="password"
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border rounded"
            />
            <input
              type="password"
              placeholder="Confirm Password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="w-full px-3 py-2 border rounded"
            />
            {error && <p className="text-red-500 text-sm">{error}</p>}
            {success && <p className="text-green-500 text-sm">{success}</p>}
            <button
              type="submit"
              className="w-full bg-blue-500 text-white py-2 rounded hover:bg-blue-600"
            >
              Sign Up
            </button>
          </form>
        )}
      </div>
    </div>
  )
}