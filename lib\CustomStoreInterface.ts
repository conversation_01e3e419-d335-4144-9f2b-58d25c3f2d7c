// lib/CustomStoreInterface.ts

/**
 * Custom interface for a key-value store.
 */
export interface CustomStoreInterface<K, V> {
  /**
   * Retrieves multiple values for a set of keys.
   * @param keys - An array of keys.
   * @returns A Promise that resolves with an array of values or undefined if a key is not found.
   */
  mget(keys: K[]): Promise<(V | undefined)[]>;
  
  /**
   * Sets values for multiple keys.
   * @param keyValuePairs - An array of key-value pairs.
   * @returns A Promise that resolves when the operation is complete.
   */
  mset(keyValuePairs: [K, V][]): Promise<void>;
  
  /**
   * Deletes multiple keys.
   * @param keys - An array of keys to delete.
   * @returns A Promise that resolves when the operation is complete.
   */
  mdelete(keys: K[]): Promise<void>;
  
  /**
   * Yields keys, optionally filtered by a prefix.
   * @param prefix - Optional prefix to filter keys.
   * @returns An asynchronous generator that yields keys.
   */
  yieldKeys(prefix?: string): AsyncGenerator<K | string>;
}
