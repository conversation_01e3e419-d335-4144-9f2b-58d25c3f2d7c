// InputBox.tsx

import React, { useRef, useEffect, useState } from 'react';
import { CircleStackIcon, PaperAirplaneIcon } from '@heroicons/react/24/solid';

interface MessageInputProps {
  message: string;
  setMessage: (message: string) => void;
  sendMessage: (messageText: string) => Promise<void>; // Ensure this is an async function
  isPending: boolean;
}

const MessageInput: React.FC<MessageInputProps> = ({
  message,
  setMessage,
  sendMessage,
  isPending,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isSubmitting, setIsSubmitting] = useState(false); // Add a state to track submission status

  // Function to adjust the height of the textarea based on its content
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto'; // Reset the height
      textarea.style.height = `${textarea.scrollHeight}px`; // Set to the scroll height
    }
  };

  // Adjust the height whenever the message changes
  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  const handleChatSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true); // Set submitting state to true

    try {
      const formData = new FormData(e.currentTarget);
      const prompt = formData.get('prompt') as string;

      if (prompt.trim() === '') return; // Prevent sending empty messages

      setMessage(''); // Clear input field
      await sendMessage(prompt); // Send the message
    } catch (error) {
      console.error('Error submitting message:', error);
    } finally {
      setIsSubmitting(false); // Reset submitting state after completion
    }
  };

  return (
    <div className="flex-row w-full mb-4 p4 relative">
      <form onSubmit={handleChatSubmit} className="flex items-center p-1  w-full">
        <textarea
          ref={textareaRef}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              const formEvent = {
                preventDefault: () => {},
                currentTarget: e.currentTarget.form,
              } as React.FormEvent<HTMLFormElement>;
              handleChatSubmit(formEvent);
            }
          }} 
          placeholder="Type a message..."
          name="prompt"
          className="flex-1 p-2 pr-25 border rounded-lg text-sm text-white border-indigo-500 bg-slate-700 resize-none overflow-hidden 
          focus:outline-none focus:ring-2 focus:ring-amber-400 transition-all duration-200"
          rows={2} // Adjusted to 2 rows to fit the submit button
          disabled={isSubmitting} // Disable the input when submitting
        />
        <button
          type="submit"
          disabled={!message.trim() || isPending}
          className={`absolute right-2 bottom-2 p-2 mb-1 rounded-full ${
            isPending
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-amber-300 hover:bg-amber-400'
          } transition-colors duration-200`}
        >
          {isPending ? (
            <CircleStackIcon className="h-5 w-5 text-gray-800 animate-spin" />
          ) : (
            <PaperAirplaneIcon className="h-5 w-5 text-slate-800" />
          )}
        </button>
      </form>
    </div>
  );
};

export default MessageInput;
