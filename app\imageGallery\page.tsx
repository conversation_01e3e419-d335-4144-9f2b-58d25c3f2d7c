'use client';

import React, { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from 'components/firebase';
import ImageModal from 'components/image-generation/ImageModal';
import Image from 'next/image';
import { ArrowLeft, ImageIcon } from 'lucide-react';

interface ImageData {
  url: string;
  description: string;
  id: string;
}

export default function ImageGalleryPage() {
  const { data: session } = useSession();
  const userId = session?.user?.email ?? null;
  const [images, setImages] = useState<ImageData[]>([]);
  const [selectedImage, setSelectedImage] = useState<ImageData | null>(null);
  const [showSignOutModal, setShowSignOutModal] = useState(false);

  useEffect(() => {
    if (!userId) return;

    const fetchImages = async () => {
      try {
        const qRef = query(
          collection(db, 'users', userId, 'files'),
          where('category', '==', 'My Images'), 
          orderBy('createdAt', 'desc')
        );
        const snapshot = await getDocs(qRef);

        const temp: ImageData[] = [];
        snapshot.forEach((docSnap) => {
          const data = docSnap.data();
          temp.push({
            url: data.downloadUrl,
            description: data.description || 'No description available',
            id: docSnap.id,
          });
        });

        setImages(temp);
      } catch (error) {
        console.error('Error fetching images for gallery:', error);
      }
    };

    fetchImages();
  }, [userId]);

  if (!userId) {
    return <div className="text-white">Please log in to view your images.</div>;
  }

  return (
    <div className="min-h-screen bg-ike-message-bg">
      <div className="flex">
        <div className="flex-1 container mx-auto px-4 py-8">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-4 -mt-10">
            <ImageIcon className="w-18 h-18  text-blue-200 " />
              <h1 className="text-3xl font-bold text-blue-200">My Gallery</h1>

            </div>
            {session?.user && (
              <div className="flex items-center">
                <span className="text-white mr-2">{session.user.name}</span>
                <button
                  onClick={() => setShowSignOutModal(true)}
                  className="flex items-center"
                >
                  <Image
                    src={session.user.image || '/placeholder-user.jpg'}
                    alt="User profile"
                    width={40}
                    height={40}
                    className="rounded-full"
                  />
                </button>
              </div>
            )}
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {images.map((img) => (
              <div key={img.id} className="flex flex-col items-center" onClick={() => setSelectedImage(img)}>
                <img
                  src={img.url}
                  alt={img.description}
                  className="w-full h-48 object-cover border border-gray-300 rounded cursor-pointer" 
                />
                <span className="text-sm mt-2 truncate text-blue-200">
                  {img.description.length > 26 
                    ? `${img.description.substring(0, 26)}...` 
                    : img.description}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
      {selectedImage && (
        <ImageModal
          image={selectedImage}
          onClose={() => setSelectedImage(null)}
        />
      )}
      {/* Add SignOut modal here */}
    </div>
  );
}

