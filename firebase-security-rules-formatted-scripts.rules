// Firebase Security Rules for formatted_scripts collection
// Add these rules to your existing firestore.rules file

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Rules for formatted_scripts collection
    match /users/{userId}/formatted_scripts/{documentId} {
      // Allow read/write only if the user is authenticated and matches the userId
      allow read, write: if request.auth != null 
                        && request.auth.token.email == userId;
      
      // Additional validation for write operations
      allow create: if request.auth != null 
                   && request.auth.token.email == userId
                   && validateFormattedScriptData(request.resource.data);
      
      allow update: if request.auth != null 
                   && request.auth.token.email == userId
                   && validateFormattedScriptUpdate(request.resource.data, resource.data);
      
      allow delete: if request.auth != null 
                   && request.auth.token.email == userId;
    }
    
    // Validation functions for formatted script data
    function validateFormattedScriptData(data) {
      return data.keys().hasAll(['originalFileId', 'originalNamespace', 'formattedMarkdown', 'metadata', 'userId', 'formattingStatus', 'formattingAttempts', 'createdAt', 'updatedAt'])
             && data.originalFileId is string
             && data.originalNamespace is string
             && data.formattedMarkdown is string
             && data.metadata is map
             && data.metadata.keys().hasAll(['title', 'author', 'characters', 'summary'])
             && data.metadata.title is string
             && data.metadata.author is string
             && data.metadata.characters is list
             && data.metadata.summary is string
             && data.userId is string
             && data.formattingStatus in ['pending', 'completed', 'failed', 'retrying']
             && data.formattingAttempts is number
             && data.formattingAttempts >= 0
             && data.createdAt is timestamp
             && data.updatedAt is timestamp;
    }
    
    function validateFormattedScriptUpdate(newData, existingData) {
      return newData.originalFileId == existingData.originalFileId
             && newData.originalNamespace == existingData.originalNamespace
             && newData.userId == existingData.userId
             && newData.createdAt == existingData.createdAt
             && newData.updatedAt is timestamp
             && (newData.formattingAttempts >= existingData.formattingAttempts);
    }
    
    // Example of how to integrate with existing rules structure
    // Uncomment and modify based on your existing rules
    
    /*
    // Existing files collection rules (for reference)
    match /users/{userId}/files/{documentId} {
      allow read, write: if request.auth != null 
                        && request.auth.token.email == userId;
    }
    
    // Existing byteStoreCollection rules (for reference)
    match /users/{userId}/byteStoreCollection/{documentId} {
      allow read, write: if request.auth != null 
                        && request.auth.token.email == userId;
    }
    
    // Existing chats collection rules (for reference)
    match /users/{userId}/chats/{chatId} {
      allow read, write: if request.auth != null 
                        && request.auth.token.email == userId;
      
      match /messages/{messageId} {
        allow read, write: if request.auth != null 
                          && request.auth.token.email == userId;
      }
    }
    */
  }
}

// Instructions for implementation:
// 1. Copy the formatted_scripts rules section to your existing firestore.rules file
// 2. Ensure the validation functions are included
// 3. Test the rules in the Firebase Console Rules Playground
// 4. Deploy the rules using: firebase deploy --only firestore:rules

// Security considerations:
// - Users can only access their own formatted scripts
// - Data validation ensures proper structure
// - Prevents unauthorized modification of critical fields
// - Allows for proper error handling and retry mechanisms
