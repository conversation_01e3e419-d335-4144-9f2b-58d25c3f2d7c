// src/app/api/generateImage/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { cert, getApps, initializeApp } from 'firebase-admin/app';
import { getStorage } from 'firebase-admin/storage';
import { getFirestore } from 'firebase-admin/firestore';

import { GenerateImageTool } from 'components/tools/generateImageTool';
import { GenerateImageAgent } from 'components/Agents/GenerateImageAgent';

// 1) Initialize Firebase Admin if not already
if (!getApps().length) {
  const serviceAccount = JSON.parse(
    process.env.FIREBASE_SERVICE_ACCOUNT_KEY || '{}'
  );

  initializeApp({
    credential: cert(serviceAccount),
    storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
  });
}

// 2) Retrieve Admin SDK instances
const storage = getStorage();
const adminDB = getFirestore();

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    const { jobId } = await req.json();

    // Provide your OpenAI API key
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    // Instantiate the tool & agent
    const imageTool = new GenerateImageTool(apiKey);
    const imageAgent = new GenerateImageAgent({ generateImageTool: imageTool });

    // 3) Generate the base64 image string
    const generateImageResponse = await imageAgent.generateImage(jobId);
    const base64Image = generateImageResponse.base64Image || '';

    // 4) Safely split off any base64 prefix
    const parts = base64Image.includes(',')
      ? base64Image.split(',')
      : [null, base64Image];
    const base64Data = parts[1];
    if (!base64Data) {
      throw new Error('Invalid base64 image data received');
    }

    // Convert to buffer
    const imageBuffer = Buffer.from(base64Data, 'base64');

    // 5) Upload to Firebase Storage (Admin SDK)
    const bucket = storage.bucket();
    const filePath = `generated/${jobId}.png`;
    const file = bucket.file(filePath);

    await file.save(imageBuffer, {
      metadata: { contentType: 'image/png' },
    });

    // Generate a Signed URL for the uploaded image
    const [downloadUrl] = await file.getSignedUrl({
      action: 'read',
      expires: '03-01-2500', // Far-future expiration
    });

    // 6) Update Firestore (Admin DB)
    const jobRef = adminDB.collection('imageJobs').doc(jobId);
    await jobRef.update({
      status: 'completed',
      imageUrl: downloadUrl,
      updatedAt: new Date(),
    });

    return NextResponse.json({ success: true, imageUrl: downloadUrl });
  } catch (error) {
    console.error('Image generation error:', error);

    // Attempt to update Firestore job status to 'failed'
    try {
      const { jobId } = await req.json();
      const jobRef = getFirestore().collection('imageJobs').doc(jobId);
      await jobRef.update({
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        updatedAt: new Date(),
      });
    } catch (updateError) {
      console.error('Failed to update job status:', updateError);
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to process image',
      },
      { status: 500 }
    );
  }
}
