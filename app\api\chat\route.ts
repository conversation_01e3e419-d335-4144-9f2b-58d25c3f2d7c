// app/api/chat/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";

// Helper function to convert user prompt to vector embedding
async function convertPromptToVector(prompt: string) {
    const { OpenAIEmbeddings } = await import("@langchain/openai");
    const embeddings = new OpenAIEmbeddings();
    const queryVector = await embeddings.embedQuery(prompt);
    return queryVector;
}

// Fetch the category associated with the fileDocumentId
async function fetchCategoryForFile(userId: string, fileDocumentId: string): Promise<string | null> {
    const { adminDb } = await import("../../../components/firebase-admin");
    const fileDocRef = adminDb.collection('users').doc(userId).collection('files').doc(fileDocumentId);
    const fileDoc = await fileDocRef.get();

    if (fileDoc.exists) {
        const data = fileDoc.data();
        return data?.category || null;
    }

    return null;
}

// Fetch namespaces for a given category
async function fetchNamespacesForCategory(userId: string, category: string): Promise<string[]> {
    const { adminDb } = await import("../../../components/firebase-admin");
    const namespaces: string[] = [];

    const snapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('files')
        .where('category', '==', category)
        .get();

    snapshot.forEach(doc => {
        const data = doc.data();
        if (data.namespace) {
            namespaces.push(data.namespace);
        }
    });

    return namespaces;
}

// Fetch chat history (using fileDocumentId in place of chatId)
// async function fetchChatHistory(userId: string, fileDocumentId: string): Promise<string> {
//     const messagesSnapshot = await adminDb
//         .collection('users')
//         .doc(userId)
//         .collection('chats')
//         .doc(fileDocumentId)
//         .collection('messages')
//         .orderBy('createdAt', 'asc')
//         .get();

//     let chatHistory = '';
//     messagesSnapshot.forEach(doc => {
//         const message = doc.data();
//         chatHistory += `${message.role === 'user' ? 'User: ' : 'AI: '}${message.text}\n`;
//     });

//     return chatHistory;
// }

export async function POST(req: NextRequest) {
    try {
        // Check for required environment variables
        if (!process.env.OPENAI_API_KEY) {
            console.error('Missing OPENAI_API_KEY environment variable');
            return new NextResponse('Service configuration error', { status: 500 });
        }

        const session = await getServerSession(authOptions);
        if (!session) {
            return new NextResponse('Unauthorized', { status: 401 });
        }

        const userId = session.user?.email;
        if (!userId) {
            return new NextResponse('Bad Request: Missing userId', { status: 400 });
        }

        const { prompt, combinedHistory, fileDocumentId } = await req.json();

        console.log(`Received prompt: ${prompt}`);
        console.log(`Using fileDocumentId: ${fileDocumentId}`);

        if (!fileDocumentId) {
            return new NextResponse('Bad Request: Missing fileDocumentId', { status: 400 });
        }

        const category = await fetchCategoryForFile(userId, fileDocumentId);
        const namespaces = category
            ? await fetchNamespacesForCategory(userId, category)
            : [fileDocumentId];

        if (!namespaces.length) {
            return new NextResponse('No namespaces found for the provided category or fileDocumentId', { status: 404 });
        }

        const chatHistory = combinedHistory
        const { getRelevantHistory } = await import('../../../lib/getRelevantHistory');
        const relevantHist = await getRelevantHistory(prompt, chatHistory);
        const queryVector = await convertPromptToVector(prompt);

        // Stream setup
        const stream = new ReadableStream({
            async start(controller) {
                const { queryOpenAIAcrossNamespacesAndProcessAI } = await import('../../../lib/queryOpenAIAcrossNamespaces');
                await queryOpenAIAcrossNamespacesAndProcessAI(
                    controller,
                    queryVector,
                    namespaces,
                    prompt,
                    relevantHist,
                    category,
                    userId
                );
            }
        });

        return new NextResponse(stream, {
            headers: { 'Content-Type': 'text/plain' }
        });

    } catch (error) {
        console.error('Error:', error);
        return new NextResponse('Internal Server Error', { status: 500 });
    }
}
