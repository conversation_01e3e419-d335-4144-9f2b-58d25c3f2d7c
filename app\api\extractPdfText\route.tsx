import { NextRequest, NextResponse } from 'next/server';
import { adminDb, adminStorage } from '../../../components/firebase-admin';
import pdfParse from 'pdf-parse';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { downloadUrl, userId, docId } = body;

    if (!downloadUrl || !userId || !docId) {
      return NextResponse.json({ success: false, error: 'Missing required parameters.' }, { status: 400 });
    }

    // Download the PDF using the downloadUrl
    const response = await fetch(downloadUrl);

    if (!response.ok) {
      throw new Error(`Failed to download PDF: ${response.statusText}`);
    }

    const pdfBuffer = await response.arrayBuffer();

    // Use pdf-parse to extract text from the PDF
    const data = await pdfParse(Buffer.from(pdfBuffer));
    const extractedText = data.text;

    // Check Firestore document size limit
    const textSizeInBytes = Buffer.byteLength(extractedText, 'utf8');
    const maxFirestoreDocumentSize = 1 * 1024 * 1024; // 1 MiB

    let updateData: any = {
      textExtractedAt: new Date(),
    };

    if (textSizeInBytes >= maxFirestoreDocumentSize) {
      // Store the extracted text in Firebase Storage
      const bucket = adminStorage.bucket();
      const textFilePath = `extractedTexts/${userId}/${docId}.txt`;
      const file = bucket.file(textFilePath);

      await file.save(extractedText, {
        metadata: {
          contentType: 'text/plain',
        },
      });

      updateData.textFilePath = textFilePath;
      updateData.textStoredIn = 'storage';
    } else {
      // Store the extracted text in Firestore
      updateData.contents = extractedText;
      updateData.textStoredIn = 'firestore';
    }

    // Update Firestore document
    await adminDb
      .collection('users')
      .doc(userId)
      .collection('files')
      .doc(docId)
      .update(updateData);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    return NextResponse.json({ success: false, error: 'Internal server error.' }, { status: 500 });
  }
}