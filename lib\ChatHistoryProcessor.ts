// ChatHistoryProcessor.ts

export interface ChatMessage {
    role: "user" | "assistant";
    text: string;
  }
  
  export interface ChatHistoryAnalytics {
    messagesOverTokenLimit: number;
    truncatedMessages: number;
    convertedCount: number;
    originalCount: number;
    averageMessageLength: number;
    willBeTruncated: boolean;
    tokensPerMessage: number[];
    totalMessages: number;
    originalMessageCount: number;
    truncatedMessageCount: number;
    totalTokens: number;
    averageTokensPerMessage: number;
  }
  
  export interface ChatHistoryConversionMetrics {
    originalCount: number;
    convertedCount: number;
    truncatedMessages: number;
    messagesOverTokenLimit: number;
  }
  
  export interface ChatHistoryConfig {
    maxHistoryMessages?: number;
    maxTokensPerMessage?: number;
    maxMessageLength?: number;
  }
  
  export class ChatHistoryProcessor {
    private readonly maxHistoryMessages: number;
    private readonly maxTokensPerMessage: number;
    private readonly maxMessageLength: number;
  
    constructor(config: ChatHistoryConfig = {}) {
      this.maxHistoryMessages = config.maxHistoryMessages || 10;
      this.maxTokensPerMessage = config.maxTokensPerMessage || 500;
      this.maxMessageLength = config.maxMessageLength || 1000;
    }
  
    private countTokens(text: string): number {
      // More accurate token estimation:
      // 1. Split into words (including punctuation)
      const words = text.trim().split(/\s+/);
      
      // 2. Count actual words
      const wordCount = words.length;
      
      // 3. Add overhead for role prefix ("User: " or "Assistant: ")
      const roleOverhead = 2; // approximate tokens for role prefix
      
      // 4. More conservative token estimation:
      // - Average English word is ~1.3 tokens
      // - Add role overhead
      const estimatedTokens = Math.ceil(wordCount * 1.3) + roleOverhead;
      
      // 5. Apply reasonable limits
      const finalTokenCount = Math.min(
        Math.max(estimatedTokens, 3), // minimum 3 tokens per message
        this.maxTokensPerMessage
      );
  
      console.log(`Token calculation for message: "${text.slice(0, 50)}..."`, {
        wordCount,
        estimatedTokens,
        finalTokenCount
      });
  
      return finalTokenCount;
    }
  
    public calculateHistoryTokens(chatHistory: string): { tokenCount: number; analytics: ChatHistoryAnalytics } {
      if (!chatHistory) {
        console.log("No chat history to process");
        return {
          tokenCount: 0,
          analytics: {
            originalMessageCount: 0,
            truncatedMessageCount: 0,
            totalTokens: 0,
            averageTokensPerMessage: 0,
            tokensPerMessage: [],
            totalMessages: 0,
            messagesOverTokenLimit: 0,
            truncatedMessages: 0,
            convertedCount: 0,
            originalCount: 0,
            averageMessageLength: 0,
            willBeTruncated: false
          }
        };
      }
      
      const messages = chatHistory
        .split('\n')
        .filter(msg => msg.trim());
    
      const recentMessages = messages.slice(-this.maxHistoryMessages);
      
      const tokenCounts = recentMessages.map(msg => this.countTokens(msg));
      const totalTokens = tokenCounts.reduce((sum, count) => sum + count, 0);
    
      // Calculate additional required properties
      const messagesOverTokenLimit = tokenCounts.filter(count => count > this.maxTokensPerMessage).length;
      const truncatedMessages = recentMessages.filter(msg => msg.length > this.maxMessageLength).length;
      const averageMessageLength = messages.reduce((total, msg) => total + msg.length, 0) / messages.length || 0;
    
      const analytics: ChatHistoryAnalytics = {
        originalMessageCount: messages.length,
        truncatedMessageCount: recentMessages.length - messages.length,
        totalTokens,
        averageTokensPerMessage: Math.round(totalTokens / (recentMessages.length || 1)),
        tokensPerMessage: tokenCounts,
        totalMessages: recentMessages.length,
        messagesOverTokenLimit,
        truncatedMessages,
        convertedCount: recentMessages.length, // Assuming all recent messages are converted
        originalCount: messages.length,
        averageMessageLength,
        willBeTruncated: messages.length > this.maxHistoryMessages
      };
    
      console.log("Detailed token analysis:", {
        messageCount: recentMessages.length,
        tokenCountPerMessage: tokenCounts,
        totalTokens,
        averageTokensPerMessage: analytics.averageTokensPerMessage
      });
    
      return { tokenCount: totalTokens, analytics };
    }
  
    public convertToMessageArray(rawHistory: string): { 
      messages: ChatMessage[];
      metrics: ChatHistoryConversionMetrics;
    } {
      if (!rawHistory) {
        console.log("No chat history provided");
        return {
          messages: [],
          metrics: {
            originalCount: 0,
            convertedCount: 0,
            truncatedMessages: 0,
            messagesOverTokenLimit: 0
          }
        };
      }
      
      const messages = rawHistory
        .split('\n')
        .filter(line => line.trim());
  
      console.log("Initial chat history analysis:", {
        totalMessages: messages.length,
        willBeTruncated: messages.length > this.maxHistoryMessages,
        averageMessageLength: Math.round(
          messages.reduce((sum, msg) => sum + msg.length, 0) / messages.length
        )
      });
  
      const recentMessages = messages.slice(-this.maxHistoryMessages);
      
      const converted = recentMessages.map(line => {
        const isUser = line.startsWith('User:');
        const text = line.slice(isUser ? 6 : 11).trim(); // Updated to handle "Assistant:" prefix
        const truncatedText = text.length > this.maxMessageLength 
          ? `${text.slice(0, this.maxMessageLength)}...` 
          : text;
        
        return {
          role: isUser ? 'user' as const : 'assistant' as const,
          text: truncatedText
        };
      });
  
      const metrics = {
        originalCount: messages.length,
        convertedCount: converted.length,
        truncatedMessages: messages.length - converted.length,
        messagesOverTokenLimit: converted.filter(msg => msg.text.endsWith('...')).length
      };
  
      console.log("Chat history conversion complete:", metrics);
  
      return { messages: converted, metrics };
    }
  
    public formatHistoryForPrompt(messages: ChatMessage[]): string {
      return messages
        .map(msg => `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.text}`)
        .join('\n');
    }
  }