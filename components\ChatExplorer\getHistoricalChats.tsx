// getHistoricalChats.ts
import { collection, query, getDocs, orderBy, Timestamp } from 'firebase/firestore';
import { db } from 'components/firebase';
import { ChatData } from 'types';
import { ChatVectorContext } from './ChatVectorTypes';
import _ from 'lodash';


const DEFAULT_CATEGORY = 'Unknown';

interface FirestoreMessage {
  id: string;
  text: string;
  role: 'user' | 'ai';
  userId: string;
  createdAt: Timestamp;
  fileDocumentId?: string;
  fileName?: string;
  category?: string;
}

interface HistoricalChat {
  userMessage: ChatData;
  aiMessage: ChatData;
  context: ChatVectorContext;
}

interface ProcessingProgress {
  totalChats: number;
  processedChats: number;
  currentChatId: string;
  messagesFound: number;
  chatPairsFound: number;
}

type ProgressCallback = (progress: ProcessingProgress) => void;

// Helper function to convert Firestore message to ChatData
const convertToChatData = (message: FirestoreMessage, userEmail: string): ChatData => {
  return {
    id: message.id,
    text: message.text || '',
    role: message.role,
    userId: userEmail,
    createdAt: message.createdAt,
    fileDocumentId: message.fileDocumentId || '',
    fileName: message.fileName || '',
    category: message.category || DEFAULT_CATEGORY,
    visualization: '',
  };
};

// Process messages and create chat pairs
const createChatPairs = (
  messages: FirestoreMessage[],
  userEmail: string,
  chatId: string,
  fileDocumentId?: string
): HistoricalChat[] => {
  const pairs: HistoricalChat[] = [];
  const sortedMessages = _.orderBy(messages, ['createdAt'], ['asc']);

  for (let i = 0; i < sortedMessages.length - 1; i++) {
    const currentMsg = sortedMessages[i];
    const nextMsg = sortedMessages[i + 1];

    // Only create pairs for valid user -> AI message sequences
    if (currentMsg.role === 'user' && nextMsg.role === 'ai') {
      const historicalChat: HistoricalChat = {
        userMessage: convertToChatData(currentMsg, userEmail),
        aiMessage: convertToChatData(nextMsg, userEmail),
        context: {
          selectedDocId: chatId,
          documentNamespace: fileDocumentId || 'default',
          category: currentMsg.category || DEFAULT_CATEGORY
        }
      };

      if (validateChatPair(historicalChat)) {
        pairs.push(historicalChat);
      }
    }
  }

  return pairs;
};

// Validate chat pair data
const validateChatPair = (chat: HistoricalChat): boolean => {
  const { userMessage, aiMessage, context } = chat;
  
  // Validate messages exist and have required fields
  if (!userMessage?.text || !aiMessage?.text) return false;
  if (userMessage.role !== 'user' || aiMessage.role !== 'ai') return false;
  if (!userMessage.id || !aiMessage.id) return false;
  
  // Validate context
  if (!context?.selectedDocId) return false;
  
  return true;
};

// Process a single chat document
const processChatDocument = async (
  chatDoc: any,
  userEmail: string,
  progress: ProcessingProgress,
  onProgress: ProgressCallback
): Promise<HistoricalChat[]> => {
  const chatId = chatDoc.id;
  progress.currentChatId = chatId;
  
  try {
    // Get messages for this chat
    const messagesRef = collection(db, `users/${userEmail}/chats/${chatId}/messages`);
    const messagesQuery = query(messagesRef, orderBy('createdAt', 'asc'));
    const messagesSnapshot = await getDocs(messagesQuery);
    
    // Update progress
    progress.messagesFound += messagesSnapshot.size;
    onProgress({ ...progress });

    if (messagesSnapshot.empty) {
      return [];
    }

    // Convert messages to array
    const messages = messagesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as FirestoreMessage));

    // Get file document ID if it exists
    const fileDocumentId = chatDoc.data()?.fileDocumentId;

    // Create chat pairs
    const chatPairs = createChatPairs(messages, userEmail, chatId, fileDocumentId);
    
    // Update progress
    progress.chatPairsFound += chatPairs.length;
    onProgress({ ...progress });

    console.log(`Processed chat ${chatId}, found ${chatPairs.length} chat pairs`);
    return chatPairs;
    
  } catch (error) {
    console.error(`Error processing chat ${chatId}:`, error);
    return [];
  }
};

export const getHistoricalChats = async (
  userEmail: string,
  onProgress?: ProgressCallback
): Promise<HistoricalChat[]> => {
  if (!userEmail) {
    throw new Error('User email is required');
  }

  const progress: ProcessingProgress = {
    totalChats: 0,
    processedChats: 0,
    currentChatId: '',
    messagesFound: 0,
    chatPairsFound: 0
  };

  try {
    console.log('Starting historical chats processing for user:', userEmail);

    // Get all chat documents
    const chatsRef = collection(db, `users/${userEmail}/chats`);
    const chatsQuery = query(chatsRef, orderBy('createdAt', 'desc'));
    const chatsSnapshot = await getDocs(chatsQuery);

    progress.totalChats = chatsSnapshot.size;
    onProgress?.({ ...progress });

    if (chatsSnapshot.empty) {
      console.log('No historical chats found');
      return [];
    }

    // Process chats sequentially to avoid overwhelming Firestore
    const allHistoricalChats: HistoricalChat[] = [];
    
    for (const chatDoc of chatsSnapshot.docs) {
      const chatPairs = await processChatDocument(
        chatDoc,
        userEmail,
        progress,
        onProgress || (() => {})
      );
      
      allHistoricalChats.push(...chatPairs);
      
      // Update progress
      progress.processedChats++;
      onProgress?.({ ...progress });
      
      // Add small delay between chats to prevent rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('Completed processing historical chats:', {
      totalChats: progress.totalChats,
      processedChats: progress.processedChats,
      totalChatPairs: allHistoricalChats.length
    });

    // Return unique chat pairs based on message IDs
    return _.uniqBy(allHistoricalChats, (chat: { userMessage: { id: any; }; aiMessage: { id: any; }; }) => 
      `${chat.userMessage.id}-${chat.aiMessage.id}`
    );

  } catch (error) {
    console.error('Error processing historical chats:', error);
    throw error;
  }
};

export type {
  HistoricalChat,
  FirestoreMessage,
  ProcessingProgress,
  ProgressCallback
};