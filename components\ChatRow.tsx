/**
 * Purpose/Summary:
 * The ChatRow component fetches and displays the first message of a chat.
 * It provides a link to navigate to the chat and uses the DeleteChatRow component for deleting the chat.
 * If no messages exist in the chat, it displays 'Begin messaging'.
 * 
 * Key Features:
 * - Fetches the first message of the chat.
 * - Displays the first message or 'Begin messaging' if no messages exist.
 * - Provides a link to navigate to the chat.
 * - Uses the DeleteChatRow component for chat deletion.
 * 
 * Dependencies:
 * - Requires Firebase Firestore for database operations.
 * - Requires NextAuth for session management.
 * - Requires Next.js for navigation.
 * - Requires Heroicons for UI icons.
 */

'use client';

import { useEffect, useState } from 'react';
import { ChatBubbleLeftIcon, TrashIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { collection, query, orderBy, limit, getDocs } from 'firebase/firestore';
import { db } from '../components/firebase'; // Adjust this path
import { useSession } from 'next-auth/react';
import DeleteChatRow from './DeleteChatRow'; // Import the DeleteChatRow component


type Props = {
  id: string;
};

function ChatRow({ id }: Props) {
  const { data: session } = useSession();
  const [firstMessage, setFirstMessage] = useState('Begin messaging..');
  const [dateCreated, setDateCreated] = useState('');

  useEffect(() => {
    // Function to fetch the first message in a chat
    const fetchFirstMessage = async () => {
      // Ensure the user is logged in
      if (!session) return;

      // Create a query to get the first message in the chat
      const messagesQuery = query(
        collection(db, 'users', session.user?.email!, 'chats', id, 'messages'),
        orderBy('createdAt', 'asc'),
        limit(1)
      );

      // Execute the query and update the state with the first message
      const messageSnapshot = await getDocs(messagesQuery);
      if (!messageSnapshot.empty) {
        const firstMessageDoc = messageSnapshot.docs[0];
        setFirstMessage(firstMessageDoc.data().text);
        const createdAt = firstMessageDoc.data().createdAt;
        setDateCreated(new Date(createdAt.seconds * 1000).toLocaleString());
      }
    };

    // Fetch the first message when the component mounts
    fetchFirstMessage();
  }, [id, session]);

  // Callback function to handle deletion
  const handleDelete = async () => {
    // Fetch the latest chat after deletion
    if (session) {
      const chatsQuery = query(
        collection(db, 'users', session.user?.email!, 'chats'),
        orderBy('createdAt', 'desc'),
        limit(1)
      );

      const chatSnapshot = await getDocs(chatsQuery);
      if (!chatSnapshot.empty) {
        const latestChatDoc = chatSnapshot.docs[0];
        window.location.href = `/chat/${latestChatDoc.id}`;
      } else {
        window.location.href = '/';
      }
    }
  };

  return (
    <div className="flex items-center p-2 space-x-2 cursor-pointer hover:bg-gray-700 transition-all duration-200 ease-out">
      <Link href={`/chat/${id}`} className="flex flex-1 items-center space-x-2">
        <ChatBubbleLeftIcon className="h-5 w-5 flex-shrink-0" />
        <p className="truncate max-w-sm">{firstMessage.substring(0, 20)}</p>
      </Link>
        <DeleteChatRow  id={id} message={firstMessage} dateCreated={dateCreated} onDelete={handleDelete}/> {/* Use the DeleteChatRow component */}

      {/* </button> */}
    </div>
  );
}

export default ChatRow;
