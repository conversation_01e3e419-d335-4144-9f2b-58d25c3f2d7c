import { Groq } from "groq-sdk";

// Types for client initialization
interface GroqClientConfig {
  userEmail: string;
}

class GroqClientManager {
  private static getApiKey(userEmail: string): string {
    const isSystemAdmin = userEmail === process.env.SYS_ADMIN || '<EMAIL>';
    return isSystemAdmin ? process.env.GROQ_API_KEY! : process.env.GROQ_ike_FREE_KEY!;
  }

  public static initializeClient({ userEmail }: GroqClientConfig): Groq {
    const apiKey = this.getApiKey(userEmail);
    return new Groq({ apiKey });
  }
}

// Convenience function for easier imports and usage
export const createGroqClient = (config: GroqClientConfig): Groq => {
  return GroqClientManager.initializeClient(config);
};

// Export the class as well in case more complex management is needed
export { GroqClientManager };