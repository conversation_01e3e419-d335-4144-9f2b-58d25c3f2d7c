'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { signOut, useSession, signIn } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { doc, getDoc } from 'firebase/firestore'
import { db } from '../components/firebase'
import UserDetailsModal from './UserDetailsModal'
import LoadingOverlay from './LoadingOverlay'

export default function Header() {
  const { data: session, status, update } = useSession()
  const router = useRouter()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [showUserDetailsModal, setShowUserDetailsModal] = useState(false)
  const [isProfileIncomplete, setIsProfileIncomplete] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isSigningOut, setIsSigningOut] = useState(false)
  const [hasCheckedProfile, setHasCheckedProfile] = useState(false)

  useEffect(() => {
    async function checkUserAccount() {
      // Only check once when the user first authenticates
      if (status === 'authenticated' && session?.user?.email && !hasCheckedProfile) {
        const accountDoc = await getDoc(doc(db, 'Accounts', session.user.email))
        if (!accountDoc.exists()) {
          setShowUserDetailsModal(true)
          setIsProfileIncomplete(true)
        } else {
          setIsProfileIncomplete(false)
        }
        setHasCheckedProfile(true)
      }
    }

    checkUserAccount()
  }, [status, session, hasCheckedProfile])

  // Reset states when authentication status changes to unauthenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      setIsModalOpen(false)
      setShowUserDetailsModal(false)
      setIsProfileIncomplete(false)
      setIsSigningOut(false)
      setIsLoading(false)
      setHasCheckedProfile(false)
    }
  }, [status])

  const handleSignIn = async () => {
    try {
      setIsLoading(true)
      await signIn('google', { callbackUrl: '/' })
    } catch (error) {
      console.error('Sign in error:', error)
      setIsLoading(false)
    }
  }

  const handleSignOut = async () => {
    setIsModalOpen(false)
    setIsLoading(true)
    setIsSigningOut(true)
    
    try {
      await signOut({ 
        redirect: false,
      })
      
      await update()
      
      setIsModalOpen(false)
      setShowUserDetailsModal(false)
      setIsProfileIncomplete(false)
      setHasCheckedProfile(false)
      
      router.push('/')
      router.refresh()
    } catch (error) {
      console.error('Sign out error:', error)
    } finally {
      setIsLoading(false)
      setIsSigningOut(false)
    }
  }

  const handleUserDetailsComplete = () => {
    setShowUserDetailsModal(false)
    setIsProfileIncomplete(false)
    setIsLoading(true)
    router.push('/dashboard/Upload')
  }

  const handleUserDetailsClose = () => {
    setShowUserDetailsModal(false)
  }


  // Determine if we should show the sign-in button
  const showSignInButton = status === 'unauthenticated' || (isSigningOut && !session)

  return (
    <>
      {isLoading && <LoadingOverlay message="Please wait..." />}
      <header className="bg-gray-200 shadow-md">
        <nav className="container mx-auto px-4 py-2">
          <div className="flex justify-between items-center">
            <Link href="/" className="flex items-center space-x-1 text-indigo-950 hover:text-gray-600 transition-colors duration-200">
              <Image
                src="/logo.png"
                alt="Company Logo"
                width={50}
                height={50}
                className="hidden sm:block"
                style={{ width: '100%', height: 'auto' }}
              />
              <Image
                src="/faviconC.png"
                alt="Company Logo"
                width={60}
                height={60}
                className="hidden sm:block"
                style={{ width: '100%', height: 'auto' }}
              />
            </Link>

            <div className="relative flex items-center">
              <nav className="flex flex-wrap justify-center md:justify-end text-ike-purple space-x-3 pr-8">
                <Link href="/features" className="hover:text-gray-200 text-sm hover:bg-ike-purple transition duration-300 px-2">
                  Features
                </Link>
                <Link href="/pricing" className="hover:text-gray-200 text-sm hover:bg-ike-purple transition duration-300 px-2">
                  Pricing
                </Link>
                <Link href="/contact" className="hover:text-gray-200 text-sm hover:bg-ike-purple transition duration-300 px-2">
                  Contact
                </Link>
              </nav>

              {status === 'authenticated' && session?.user && !isSigningOut ? (
                <>
                  <div className="flex items-center mr-2 cursor-pointer" onClick={() => setIsModalOpen(!isModalOpen)}>
                    <Image
                      src={session.user.image || "/default-avatar.png"}
                      alt={`${session.user.name}'s profile`}
                      width={32}
                      height={32}
                      className="rounded-full mr-1"
                    />
                  </div>

                  {isModalOpen && (
                    <div className="absolute top-12 right-0 bg-white p-4 rounded-lg shadow-lg w-64 z-50">
                      <button
                        className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
                        onClick={() => setIsModalOpen(false)}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                      <div className="flex flex-col items-center justify-center mt-4">
                        <Image
                          src={session.user.image || "/default-avatar.png"}
                          alt="User Profile"
                          width={40}
                          height={40}
                          className="rounded-full mb-2"
                        />
                        <h2 className="text-md font-semibold text-center">{session.user.name}</h2>
                        <p className="text-sm text-gray-600 text-center mb-4">{session.user.email}</p>
                        {isProfileIncomplete && (
                          <button
                            onClick={() => setShowUserDetailsModal(true)}
                            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded w-full mb-2 transition duration-300 ease-in-out transform hover:scale-105"
                          >
                            Complete Profile
                          </button>
                        )}
                        <button
                          onClick={handleSignOut}
                          className="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded w-full transition duration-300 ease-in-out transform hover:scale-105"
                        >
                          Sign Out
                        </button>
                      </div>
                    </div>
                  )}
                </>
              ) : showSignInButton && (
                <div className="flex space-x-2">
                  <button
                    onClick={handleSignIn}
                    className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-300 ease-in-out"
                  >
                    Sign In
                  </button>
                </div>
              )}
            </div>
          </div>
        </nav>

        {showUserDetailsModal && session?.user && (
          <UserDetailsModal
            email={session.user.email}
            name={session.user.name}
            onClose={handleUserDetailsClose}
            onComplete={handleUserDetailsComplete}
          />
        )}
      </header>
    </>
  )
}