import { ProcessChatHistoryTool } from "components/tools/ProcessChatHistoryTool";
import { TokenManagement } from "@/src/tokenTracker/tokenManagement";
import { ChatMessage } from 'lib/ChatHistoryProcessor';

interface ChatHistoryAgentInput {
  rawChatHistory: string;
  tokenManager: TokenManagement;
}

interface ChatHistoryAgentOutput {
  success: boolean;
  chatHistoryArray: ChatMessage[];
  chatHistoryTokens: number;
  historyAnalytics: {
    tokenCount: number;
    messageCount: number;
    avgTokensPerMessage: number;
    [key: string]: any;
  };
  error?: string;
}

/**
 * ChatHistoryAgent
 * Manages chat history processing and optimization
 */
export class ChatHistoryAgent {
  static description = `ChatHistoryAgent processes and manages conversation history to:
  - Optimize message history for context preservation
  - Track and manage token usage
  - Calculate conversation analytics
  - Format chat messages for LLM consumption
  
  The agent ensures:
  - Efficient token utilization
  - Proper message formatting
  - Accurate analytics tracking
  - Error handling and recovery
  - History truncation when needed`;

  constructor(
    private readonly chatHistoryTool: ProcessChatHistoryTool
  ) {}

  async process(input: ChatHistoryAgentInput): Promise<ChatHistoryAgentOutput> {
    try {
      // Process chat history using the tool
      const result = await this.chatHistoryTool._call({
        rawChatHistory: input.rawChatHistory,
        tokenManager: input.tokenManager
      });

      return {
        success: true,
        ...result
      };

    } catch (error) {
      console.error("ChatHistoryAgent: Processing failed", error);
      return {
        success: false,
        chatHistoryArray: [],
        chatHistoryTokens: 0,
        historyAnalytics: {
          tokenCount: 0,
          messageCount: 0,
          avgTokensPerMessage: 0
        },
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  // Method to get agent's description
  getDescription(): string {
    return ChatHistoryAgent.description;
  }
}