"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { Lo<PERSON>, FileText, StopCircle, Eye, EyeOff, Volume2, VolumeX, Trash, Play, Pause, ArrowUp, <PERSON><PERSON><PERSON>, Mic, MicOff } from "lucide-react";
import { useSession } from "next-auth/react";
// Import ScriptLine type - it's not directly used here anymore for formatting but might be for other things.
// import { ScriptLine } from "../../lib/tools/scriptFormatter"; // Keep if used elsewhere, otherwise can remove
import ScriptMarkdownContent from "./ScriptMarkdownContent";
import { ScriptFormattingProgress } from "../CircularProgress";

interface ScriptValidationStatus {
  isValidating: boolean
  isSynchronizing: boolean
  firebaseExists: boolean
  elevenLabsExists: boolean
  isReady: boolean
  statusMessage: string
  error?: string
}

interface ScriptTabProps {
  scriptContent: string;
  isScriptLoading: boolean;
  isScriptReady: boolean;
  scriptName: string | null;
  scriptId?: string | null; // Add scriptId prop for pre-formatted script lookup
  isListening?: boolean;
  isMuted?: boolean;
  handleEndConversation?: () => Promise<void>;
  handleStartConversation?: () => Promise<void>;
  toggleMute?: () => void;
  onScriptDeleted?: () => void; // Callback for when script is deleted
  // Rehearsal-related props
  apiConfigStatus?: 'unchecked' | 'valid' | 'invalid' | 'connecting';
  hasPermission?: boolean;
  voiceStatus?: string;
  selectedVoiceId?: string | null;
  // Script validation props
  scriptValidationStatus?: ScriptValidationStatus;
}

function ScriptTab({
  scriptContent,
  isScriptLoading,
  isScriptReady,
  scriptName,
  scriptId,
  isListening = false,
  isMuted = false,
  handleEndConversation = async () => {},
  handleStartConversation = async () => {},
  toggleMute = () => {},
  onScriptDeleted = () => {},
  apiConfigStatus = 'unchecked',
  hasPermission = false,
  voiceStatus = 'disconnected',
  selectedVoiceId = null,
  scriptValidationStatus,
}: ScriptTabProps) {
  const [formattedMarkdown, setFormattedMarkdown] = useState<string>("");
  const [isFormatting, setIsFormatting] = useState<boolean>(false);
  const [formatError, setFormatError] = useState<string | null>(null);
  const [showRawScript, setShowRawScript] = useState<boolean>(false);
  const [formattingStage, setFormattingStage] = useState<'uploading' | 'processing' | 'formatting' | 'storing' | 'completed' | 'error'>('formatting');
  const [formattingProgress, setFormattingProgress] = useState<number>(0);
  const [isUsingFallback, setIsUsingFallback] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  // Auto-scroll state
  const [isAutoScrolling, setIsAutoScrolling] = useState<boolean>(false);
  const [scrollSpeed, setScrollSpeed] = useState<number>(2); // lines per second
  const [showScrollControls, setShowScrollControls] = useState<boolean>(false);
  const [loopAtEnd, setLoopAtEnd] = useState<boolean>(false); // Whether to loop back to top when reaching end
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const autoScrollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const userScrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  // FIX: Ref to track programmatic scrolling to prevent onScroll from stopping it
  const isScrollingProgrammatically = useRef<boolean>(false);

  const { data: session } = useSession();

  // Auto-scroll functions
  const startAutoScroll = useCallback(() => {
    if (!scrollContainerRef.current || isAutoScrolling) return;

    setIsAutoScrolling(true);
    const container = scrollContainerRef.current;
    const scrollStep = 4; // pixels per step
    const intervalTime = 1000 / (scrollSpeed * 2); // Convert lines per second to interval

    autoScrollIntervalRef.current = setInterval(() => {
      if (!container) return;

      const { scrollTop, scrollHeight, clientHeight } = container;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;

      if (isAtBottom) {
        if (loopAtEnd) {
          isScrollingProgrammatically.current = true; // FIX: Flag scroll as programmatic
          container.scrollTop = 0; // Loop back to top
        } else {
          setIsAutoScrolling(false);
          if (autoScrollIntervalRef.current) {
            clearInterval(autoScrollIntervalRef.current);
            autoScrollIntervalRef.current = null;
          }
        }
        return;
      }

      isScrollingProgrammatically.current = true; // FIX: Flag scroll as programmatic
      container.scrollTop += scrollStep;
    }, intervalTime);
  }, [scrollSpeed, isAutoScrolling, loopAtEnd]);

  const stopAutoScroll = useCallback(() => {
    setIsAutoScrolling(false);
    if (autoScrollIntervalRef.current) {
      clearInterval(autoScrollIntervalRef.current);
      autoScrollIntervalRef.current = null;
    }
  }, []);

  const toggleAutoScroll = useCallback(() => {
    if (isAutoScrolling) {
      stopAutoScroll();
    } else {
      startAutoScroll();
    }
  }, [isAutoScrolling, startAutoScroll, stopAutoScroll]);

  const scrollToTop = useCallback(() => {
    if (scrollContainerRef.current) {
      isScrollingProgrammatically.current = true; // FIX: Flag scroll as programmatic
      scrollContainerRef.current.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  }, []);

  // FIX: Rewritten handleUserScroll to use the ref flag instead of event.isTrusted
  const handleUserScroll = useCallback(() => {
    // If the scroll event was triggered by our code, reset the flag and do nothing.
    if (isScrollingProgrammatically.current) {
      isScrollingProgrammatically.current = false;
      return;
    }

    // If we're here, it was a manual user scroll. Pause auto-scrolling if it's active.
    if (isAutoScrolling) {
      stopAutoScroll();

      if (userScrollTimeoutRef.current) {
        clearTimeout(userScrollTimeoutRef.current);
      }
      userScrollTimeoutRef.current = setTimeout(() => {
        // Don't auto-resume, let the user restart it.
      }, 2000);
    }
  }, [isAutoScrolling, stopAutoScroll]);


  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle shortcuts when script tab is active and not in input fields
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (event.key) {
        case ' ': // Spacebar to toggle auto-scroll
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            toggleAutoScroll();
          }
          break;
        case 'Home': // Home key to scroll to top
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            scrollToTop();
          }
          break;
        case 'Escape': // Escape to stop auto-scroll
          if (isAutoScrolling) {
            event.preventDefault();
            stopAutoScroll();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [toggleAutoScroll, scrollToTop, stopAutoScroll, isAutoScrolling]);

  // Cleanup intervals on unmount
  useEffect(() => {
    return () => {
      if (autoScrollIntervalRef.current) {
        clearInterval(autoScrollIntervalRef.current);
      }
      if (userScrollTimeoutRef.current) {
        clearTimeout(userScrollTimeoutRef.current);
      }
    };
  }, []);

  // Removed direct instantiation of scriptFormatter

  useEffect(() => {
    // Renamed to avoid conflict if an old isFormatting was in scope
    let formattingUnderway = false;

    const loadFormattedScript = async () => {
      if (!isScriptReady || formattingUnderway) {
        return;
      }

      formattingUnderway = true;
      setIsFormatting(true);
      setFormatError(null);
      setFormattedMarkdown(""); // Clear previous markdown
      setFormattingStage('uploading');
      setFormattingProgress(10);

      try {
        // First, try to get the pre-formatted script if we have a scriptId
        if (scriptId) {
          console.log("ScriptTab: Loading pre-formatted script for ID:", scriptId);
          setFormattingStage('processing');
          setFormattingProgress(30);

          const response = await fetch(`/api/get-formatted-script/${scriptId}`);

          if (response.ok) {
            const data = await response.json();
            if (data.success) {
              console.log("ScriptTab: Loaded pre-formatted script:", data.data);
              setFormattingStage('completed');
              setFormattingProgress(100);
              setFormattedMarkdown(data.data.formattedMarkdown);
              return;
            }
          } else if (response.status === 404) {
            console.log("ScriptTab: Pre-formatted script not found, attempting fallback formatting...");
            setFormattingStage('formatting');
            setFormattingProgress(50);
            setIsUsingFallback(true);

            // Try fallback formatting
            const fallbackResponse = await fetch("/api/format-script-fallback", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                scriptId: scriptId,
              }),
            });

            if (fallbackResponse.ok) {
              const fallbackData = await fallbackResponse.json();
              if (fallbackData.success) {
                console.log("ScriptTab: Fallback formatting successful:", fallbackData.data);
                setFormattingStage('storing');
                setFormattingProgress(90);
                setFormattedMarkdown(fallbackData.data.formattedMarkdown);
                setFormattingStage('completed');
                setFormattingProgress(100);
                setIsUsingFallback(false);
                return;
              }
            }
            setIsUsingFallback(false);
          }
        }

        // If no scriptId or pre-formatting failed, fall back to on-demand formatting
        if (!scriptContent) {
          throw new Error("No script content available for formatting");
        }

        console.log("ScriptTab: Using on-demand formatting as final fallback...");
        setFormattingStage('formatting');
        setFormattingProgress(60);

        const response = await fetch('/api/format-script', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ scriptContent }),
        });

        const responseData = await response.json();

        if (!response.ok) {
          console.error("ScriptTab: API Error Response Data:", responseData);
          throw new Error(responseData.details || responseData.error || `API request failed with status ${response.status}`);
        }

        if (!responseData.formattedMarkdown) {
            throw new Error("API response did not include formattedMarkdown.");
        }

        setFormattingStage('storing');
        setFormattingProgress(90);
        setFormattedMarkdown(responseData.formattedMarkdown);
        setFormattingStage('completed');
        setFormattingProgress(100);
        console.log("ScriptTab: On-demand formatting successful.");

      } catch (error) {
        console.error("ScriptTab: Error loading formatted script:", error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        setFormatError(errorMessage);
        setFormattingStage('error');

        // Create fallback markdown with raw content
        const fallbackMarkdown = `## Original Content (Formatting Error)\n\n**Error:** ${errorMessage}\n\n---\n\n${scriptContent
          .split('\n')
          .slice(0, 50)
          .join('\n')}${scriptContent.split('\n').length > 50 ? "\n\n*[...content truncated]*" : ""}`;
        setFormattedMarkdown(fallbackMarkdown);
      } finally {
        setIsFormatting(false);
        setIsUsingFallback(false);
        formattingUnderway = false;
      }
    };

    // Call loadFormattedScript when dependencies change
    loadFormattedScript();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scriptContent, isScriptReady, scriptId]); // Added scriptId to dependencies

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!scriptId || !session?.user?.email) {
      console.error("Cannot delete: missing scriptId or user session");
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch('/api/deleteDocumentAndChats', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: session.user.email,
          namespace: scriptId,
          scriptName: scriptName, // Include script name for ElevenLabs deletion
        }),
      });

      if (response.ok) {
        console.log("Script deleted successfully");
        onScriptDeleted(); // Notify parent component
        setShowDeleteModal(false);
      } else {
        console.error('Failed to delete script:', await response.json());
      }
    } catch (error) {
      console.error('Error deleting script:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  const rawFormattedContent = scriptContent
    .split("\n")
    .map((line, index) => (
      <p key={index} className={`mb-2 ${line.trim() === "" ? "h-4" : ""}`}>
        {line}
      </p>
    ));

  return (
    <div className="h-full flex flex-col">
      {/* Script Validation Status Banner */}
      {scriptValidationStatus && (scriptValidationStatus.isValidating || scriptValidationStatus.isSynchronizing || scriptValidationStatus.error) && (
        <div className={`flex-shrink-0 mb-4 p-3 rounded-lg border transition-colors duration-300 ${
          scriptValidationStatus.error
            ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
            : scriptValidationStatus.isSynchronizing
              ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
              : 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
        }`}>
          <div className="flex items-center space-x-3">
            {(scriptValidationStatus.isValidating || scriptValidationStatus.isSynchronizing) && (
              <Loader className="w-4 h-4 animate-spin text-blue-600 dark:text-blue-400" />
            )}
            <div className="flex-1">
              <p className={`text-sm font-medium ${
                scriptValidationStatus.error
                  ? 'text-red-800 dark:text-red-200'
                  : scriptValidationStatus.isSynchronizing
                    ? 'text-blue-800 dark:text-blue-200'
                    : 'text-yellow-800 dark:text-yellow-200'
              }`}>
                {scriptValidationStatus.statusMessage}
              </p>
              {scriptValidationStatus.error && (
                <p className="text-xs text-red-600 dark:text-red-300 mt-1">
                  {scriptValidationStatus.error}
                </p>
              )}
              {!scriptValidationStatus.error && (
                <div className="flex items-center space-x-4 mt-2 text-xs">
                  <div className="flex items-center space-x-1">
                    <div className={`w-2 h-2 rounded-full ${
                      scriptValidationStatus.firebaseExists
                        ? 'bg-green-500'
                        : 'bg-gray-300 dark:bg-gray-600'
                    }`} />
                    <span className="text-gray-600 dark:text-gray-400">Firebase</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className={`w-2 h-2 rounded-full ${
                      scriptValidationStatus.elevenLabsExists
                        ? 'bg-green-500'
                        : scriptValidationStatus.isSynchronizing
                          ? 'bg-blue-500 animate-pulse'
                          : 'bg-gray-300 dark:bg-gray-600'
                    }`} />
                    <span className="text-gray-600 dark:text-gray-400">ElevenLabs</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Fixed Header */}
      <div className="flex-shrink-0 mb-4 pb-4 border-b border-gray-200 dark:border-white/10 flex justify-between items-center transition-colors duration-300 relative">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center transition-colors duration-300">
          <FileText className="w-5 h-5 mr-2 text-gray-500 dark:text-gray-400" />
          Script Content
        </h2>

        {/* Rehearsal Button - Centered */}
        <div className="absolute left-1/2 -translate-x-1/2">
          {isScriptReady && !isFormatting && (
            <>
              {voiceStatus === "connected" ? (
                <button
                  onClick={handleEndConversation}
                  className="flex items-center gap-2 px-3 py-2 bg-red-50 dark:bg-red-600/80 hover:bg-red-100 dark:hover:bg-red-500 text-red-600 dark:text-white rounded-lg shadow-sm transition-colors"
                  title="Stop rehearsal"
                >
                  <MicOff className="w-4 h-4" />
                  <span className="text-sm font-medium">Stop</span>
                </button>
              ) : (
                <button
                  onClick={handleStartConversation}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg shadow-sm transition-colors ${
                    !hasPermission || apiConfigStatus === 'invalid' || !session?.user?.email || apiConfigStatus === 'connecting' || !selectedVoiceId
                      ? "bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed"
                      : "bg-green-50 dark:bg-green-600/80 hover:bg-green-100 dark:hover:bg-green-500 text-green-600 dark:text-white"
                  }`}
                  disabled={!hasPermission || apiConfigStatus === 'invalid' || !session?.user?.email || apiConfigStatus === 'connecting' || !selectedVoiceId}
                  title={
                    !selectedVoiceId
                      ? "Select a voice first"
                      : !hasPermission
                        ? "Microphone permission required"
                        : apiConfigStatus === 'invalid'
                          ? "API configuration invalid"
                          : !session?.user?.email
                            ? "Please sign in"
                            : "Start rehearsal"
                  }
                >
                  {apiConfigStatus === 'connecting' ? (
                    <Loader className="w-4 h-4 animate-spin" />
                  ) : (
                    <Mic className="w-4 h-4" />
                  )}
                  <span className="text-sm font-medium">Rehearse</span>
                </button>
              )}
            </>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* Auto-scroll Controls */}
          {isScriptReady && !isFormatting && (
            <>
              {/* Scroll to Top Button */}
              <button
                onClick={scrollToTop}
                className="p-2 bg-blue-50 dark:bg-blue-600/80 hover:bg-blue-100 dark:hover:bg-blue-500 text-blue-600 dark:text-white rounded-full shadow-sm transition-colors"
                title="Scroll to top"
              >
                <ArrowUp className="w-5 h-5" />
              </button>

              {/* Auto-scroll Toggle Button */}
              <button
                onClick={toggleAutoScroll}
                className={`p-2 rounded-full shadow-sm transition-colors ${
                  isAutoScrolling
                    ? 'bg-green-50 dark:bg-green-600/80 hover:bg-green-100 dark:hover:bg-green-500 text-green-600 dark:text-white'
                    : 'bg-gray-50 dark:bg-gray-600/80 hover:bg-gray-100 dark:hover:bg-gray-500 text-gray-600 dark:text-white'
                }`}
                title={isAutoScrolling ? "Stop auto-scroll" : "Start auto-scroll"}
              >
                {isAutoScrolling ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
              </button>

              {/* Scroll Speed Control */}
              <button
                onClick={() => setShowScrollControls(!showScrollControls)}
                className="p-2 bg-gray-50 dark:bg-gray-600/80 hover:bg-gray-100 dark:hover:bg-gray-500 text-gray-600 dark:text-white rounded-full shadow-sm transition-colors"
                title="Scroll settings"
              >
                <Settings className="w-5 h-5" />
              </button>
            </>
          )}

          {/* Delete Script Button */}
          {isScriptReady && !isFormatting && scriptId && scriptName && (
            <button
              onClick={handleDeleteClick}
              className="p-2 bg-red-50 dark:bg-red-600/80 hover:bg-red-100 dark:hover:bg-red-500 text-red-600 dark:text-white rounded-full shadow-sm transition-colors"
              title="Delete script"
            >
              <Trash className="w-5 h-5" />
            </button>
          )}

          {/* Toggle Raw/Formatted Script Button */}
          {isScriptReady && !isFormatting && (
            <button
              onClick={() => setShowRawScript(!showRawScript)}
              className="p-2 bg-gray-100 dark:bg-gray-800/80 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-full shadow-sm transition-colors"
              title={showRawScript ? "Show formatted script" : "Show raw script"}
            >
              {showRawScript ? <Eye className="w-5 h-5" /> : <EyeOff className="w-5 h-5" />}
            </button>
          )}

          {/* Rehearsal Controls */}
          {isListening && (
            <>
              <button
                onClick={toggleMute}
                className={`p-2 rounded-full ${
                  isMuted ? "bg-red-500/20 text-red-400" : "bg-green-500/20 text-green-400"
                } hover:bg-opacity-30 transition-colors`}
                title={isMuted ? "Unmute Microphone" : "Mute Microphone"}
              >
                {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
              </button>
              <button
                onClick={() => {
                  const hideSettingsEvent = new CustomEvent("hide-settings-panel");
                  document.dispatchEvent(hideSettingsEvent);
                  if (handleEndConversation) handleEndConversation();
                }}
                className="p-2 bg-red-500/20 text-red-400 hover:bg-red-500/30 rounded-full transition-colors"
                title="End Rehearsal"
              >
                <StopCircle className="w-4 h-4" />
              </button>
            </>
          )}
        </div>
      </div>

      {/* Scroll Speed Control Panel */}
      {showScrollControls && (
        <div className="mb-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 transition-colors duration-300">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Auto-scroll Speed</span>
            <div className="flex items-center space-x-3">
              <span className="text-xs text-gray-500 dark:text-gray-400">Slow</span>
              <input
                type="range"
                min="1"
                max="5"
                step="0.5"
                value={scrollSpeed}
                onChange={(e) => setScrollSpeed(parseFloat(e.target.value))}
                className="w-24 h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer"
              />
              <span className="text-xs text-gray-500 dark:text-gray-400">Fast</span>
              <span className="text-xs font-medium text-gray-700 dark:text-gray-300 min-w-[3rem]">
                {scrollSpeed}x
              </span>
            </div>
          </div>
          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Loop at End</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={loopAtEnd}
                  onChange={(e) => setLoopAtEnd(e.target.checked)}
                  className="sr-only"
                />
                <div className={`w-11 h-6 rounded-full transition-colors ${
                  loopAtEnd ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'
                }`}>
                  <div className={`w-5 h-5 bg-white rounded-full shadow-md transform transition-transform ${
                    loopAtEnd ? 'translate-x-5' : 'translate-x-0.5'
                  } mt-0.5`}></div>
                </div>
                <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                  {loopAtEnd ? 'Restart from top' : 'Stop at end'}
                </span>
              </label>
            </div>
          </div>
          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              <div className="font-medium mb-1">Keyboard Shortcuts:</div>
              <div className="space-y-1">
                <div><kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-700 rounded text-xs">Ctrl/Cmd + Space</kbd> Toggle auto-scroll</div>
                <div><kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-700 rounded text-xs">Ctrl/Cmd + Home</kbd> Scroll to top</div>
                <div><kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-700 rounded text-xs">Esc</kbd> Stop auto-scroll</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Scrollable Script Content */}
      <div
        ref={scrollContainerRef}
        onScroll={handleUserScroll}
        className="flex-grow overflow-y-auto custom-script-scrollbar pr-4 -mr-4 relative"
      >
        {/* Auto-scroll Status Indicator */}
        {isAutoScrolling && (
          <div className="absolute top-4 left-4 z-20 bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg flex items-center space-x-2">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <span>Auto-scrolling at {scrollSpeed}x speed</span>
          </div>
        )}
        {isScriptLoading ? (
          <div className="flex items-center justify-center h-40">
            <Loader className="w-6 h-6 text-gray-500 dark:text-gray-400 animate-spin mr-3" />
            <span className="text-gray-700 dark:text-gray-300">Loading script content...</span>
          </div>
        ) : !isScriptReady || !scriptId ? (
          <div className="text-center py-20">
            <div className="flex flex-col items-center space-y-4">
              <FileText className="w-16 h-16 text-gray-400 dark:text-gray-500" />
              <div className="space-y-2">
                <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300">
                  {!scriptId
                    ? "No Script Selected"
                    : scriptName
                      ? "Script content is being prepared..."
                      : "Loading Script..."}
                </h3>
                <p className="text-gray-500 dark:text-gray-400 max-w-md">
                  {!scriptId
                    ? "Select a script from the sidebar to view its content, or upload a new script to get started."
                    : "Please wait while we prepare your script content."}
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="relative">
            {isFormatting ? (
              <div className="flex flex-col items-center justify-center min-h-[400px] py-8 -mt-10">
                <ScriptFormattingProgress
                  stage={formattingStage}
                  progress={formattingProgress}
                />
                <span className="text-gray-600 dark:text-gray-300 -mt-5">Formatting script with CastMate AI</span>
                <span className="text-gray-600 dark:text-gray-300 mt-1">This may take a moment...</span>
                {isUsingFallback && (
                  <span className="text-amber-300 mt-2 font-semibold animate-pulse">
                    Almost Done..
                  </span>
                )}
              </div>
            ) : showRawScript ? (
              <div className="bg-gray-50 dark:bg-gray-900 p-6 rounded-lg shadow-sm mb-4 mx-auto max-w-4xl border border-gray-200 dark:border-gray-700 transition-colors duration-300">
                <div className="text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono leading-relaxed">
                  {rawFormattedContent}
                </div>
              </div>
            ) : (
              <div className="markdown-script-output">
                {/* If formattedMarkdown is empty and no error, show a message or keep it blank */}
                {formattedMarkdown || formatError ? (
                    <ScriptMarkdownContent content={formattedMarkdown} />
                ) : (
                    <div className="text-center py-10 text-gray-500 dark:text-gray-400">
                        Ready to display formatted script.
                    </div>
                )}
              </div>
            )}

            {/* Floating Action Buttons for Mobile */}
            {isScriptReady && !isFormatting && (
              <div className="fixed bottom-6 right-6 flex flex-col space-y-2 z-10 sm:hidden">
                {/* Scroll to Top FAB */}
                <button
                  onClick={scrollToTop}
                  className="w-12 h-12 bg-blue-500 hover:bg-blue-600 active:bg-blue-700 text-white rounded-full shadow-lg flex items-center justify-center transition-colors touch-manipulation"
                  title="Scroll to top"
                  aria-label="Scroll to top"
                >
                  <ArrowUp className="w-6 h-6" />
                </button>

                {/* Auto-scroll Toggle FAB */}
                <button
                  onClick={toggleAutoScroll}
                  className={`w-12 h-12 rounded-full shadow-lg flex items-center justify-center transition-colors touch-manipulation ${
                    isAutoScrolling
                      ? 'bg-green-500 hover:bg-green-600 active:bg-green-700 text-white'
                      : 'bg-gray-500 hover:bg-gray-600 active:bg-gray-700 text-white'
                  }`}
                  title={isAutoScrolling ? "Stop auto-scroll" : "Start auto-scroll"}
                  aria-label={isAutoScrolling ? "Stop auto-scroll" : "Start auto-scroll"}
                >
                  {isAutoScrolling ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                </button>
              </div>
            )}

          </div>
        )}
      </div>

      <style jsx global>{`
        .custom-script-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-script-scrollbar::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 10px;
        }
        .custom-script-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(107, 114, 128, 0.5);
          border-radius: 10px;
        }
        .custom-script-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(107, 114, 128, 0.7);
        }
        .custom-script-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: rgba(107, 114, 128, 0.5) rgba(0, 0, 0, 0.1);
        }

        /* Range slider styling */
        input[type="range"]::-webkit-slider-thumb {
          appearance: none;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        input[type="range"]::-webkit-slider-thumb:hover {
          background: #2563eb;
        }

        input[type="range"]::-moz-range-thumb {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        input[type="range"]::-moz-range-thumb:hover {
          background: #2563eb;
        }

        .markdown-script-output p strong {
          display: block;
          text-align: center;
          font-weight: bold;
          font-size: 1.25rem;
          color: #1f2937; /* Dark gray for character names - works in both themes */
          margin-bottom: 0.5rem;
          margin-top: 2rem;
        }

        .markdown-script-output p:has(strong) {
          margin-top: 2rem;
          margin-bottom: 0.7rem; /* Reduced slightly from 2.7 to tighten with dialogue */
        }

        .markdown-script-output p {
          margin-bottom: 1.3rem;
          line-height: 1.6; /* Slightly increased for readability */
          color: #374151; /* Professional gray for dialogue text */
        }

        .markdown-script-output p em {
          color: #6b7280; /* Professional muted gray for notes */
          display: block;
          margin-top: 0.5rem;
          margin-bottom: 1rem;
          font-style: italic;
        }

        /* Potentially keep for other uses if any */
        .script-dialogue {
          margin-bottom: 1.3rem;
          padding-left: 0.5rem;
          border-left: 2px solid rgba(107, 114, 128, 0.3);
        }
        .script-dialogue strong {
          color: #f59e0b;
          display: inline-block;
          min-width: 120px;
        }
        .line-number {
          color: #6b7280;
          font-weight: normal;
          margin-right: 0.5rem;
          user-select: none;
          display: inline-block;
          min-width: 2rem;
          text-align: right;
        }
        .highlighted-text {
          background-color: #fef3c7;
          color: #000000;
          padding: 0 4px;
        }

        /* Styles for headings and summaries from the markdown */
        .markdown-script-output h1,
        .markdown-script-output h2 {
          color: #111827; /* Professional dark gray for headings */
          margin-top: 1.5em;
          margin-bottom: 0.8em;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 0.3em;
        }
        .markdown-script-output h1 {
          font-size: 1.8rem;
        }
        .markdown-script-output h2 {
          font-size: 1.5rem;
        }
        .markdown-script-output ul {
          list-style-type: disc;
          margin-left: 20px;
          margin-bottom: 1em;
          color: #374151;
        }
        .markdown-script-output li {
          margin-bottom: 0.5em;
        }

      `}</style>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
          <div className="bg-white dark:bg-slate-800 rounded-xl p-6 max-w-md w-full mx-4 border border-red-200 dark:border-red-500/20 shadow-xl transition-colors duration-300">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Delete Script</h3>
            <p className="text-gray-700 dark:text-gray-300 mb-6">
              Are you sure you want to delete "{scriptName}"? This action cannot be undone and will also delete all associated chats.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleDeleteCancel}
                disabled={isDeleting}
                className="px-4 py-2 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-white rounded-md transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirm}
                disabled={isDeleting}
                className="px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded-md transition-colors disabled:opacity-50 flex items-center"
              >
                {isDeleting ? (
                  <>
                    <Loader className="w-4 h-4 animate-spin mr-2" />
                    Deleting...
                  </>
                ) : (
                  'Delete'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default ScriptTab;