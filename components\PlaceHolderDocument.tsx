"use client"
import React from 'react';
import { ArrowUpTrayIcon } from '@heroicons/react/24/solid'; // Import the icon
import { useRouter } from 'next/navigation';

interface PlaceHolderDocumentProps {
  pageContent: string;
}

function PlaceHolderDocument({ pageContent }: PlaceHolderDocumentProps) {
  const router = useRouter();

  const handleClick = () => {
    router.push("/dashboard/Upload");
  };

  return (
    <button 
      onClick={handleClick}
      className="flex flex-col justify-center items-center w-64 h-80 rounded-xl bg-gray-200 hover:bg-gray-600 drop-shadow-md text-gray-400 hover:text-white transition-colors duration-200"
      style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}
    >
      <ArrowUpTrayIcon className="h-12 w-12 mb-2" />
      <p className="text-center">{pageContent || "Add a document"}</p>
    </button>
  );
}

export default PlaceHolderDocument;
