"use client";

import React, { useState } from "react";
import {
  FileIcon,
  ImageIcon,
  TableIcon,
  FileTextIcon,
  XIcon,
  ThumbsUpIcon,
  ArrowDownCircle
} from "lucide-react";
import Image from 'next/image';
import {
  useFileUpload,
  useFileIdRedirect,
  isUploadInProgress,
  getFileIcon,
} from './FileUploadUtils';
import useUpload from "../hooks/useUpload";
import useFetchCategories from "./DocViewer/useFetchCategories";
import DetailedUploadProgress from "./DetailedUploadProgress";

interface FileUploaderProps {
  fileId?: string;
}

export default function FileUploader({ fileId: propFileId }: FileUploaderProps) {
  const { progress, status, handleUpload } = useUpload();
  const categories = useFetchCategories();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [newCategory, setNewCategory] = useState<string>("");

  useFileIdRedirect(propFileId);

  const {
    fileName,
    selectedFile,
    preview,
    error,
    clearSelection,
    getRootProps,
    getInputProps,
    isDragActive,
  } = useFileUpload({
    handleUpload,
    selectedCategory,
    newCategory,
  });

  return (
    <div className="min-h-screen bg-ike-purple">
      <div className="flex flex-col items-center pt-5 pb-5 justify-center gap-2 mb-5">
        <Image
          src="/favicon.png"
          alt="Company Logo"
          width={60}
          height={60}
          className="hidden sm:block"
        />
        <div className="flex items-center">
          <h1 className="text-2xl font-bold text-white">Content Uploader</h1>
          <FileIcon className="h-8 w-8 text-white ml-5" />
        </div>
        <p className="text-white text-center mt-4 max-w-2xl mx-auto">
          To get started upload your content and explore insights. Create folders for files with common themes.
        </p>
      </div>

      <div className="flex flex-col gap-4 items-center mt-5 pb-5 rounded-xl shadow-lg bg-gray-100 max-w-4xl mx-auto px-4">
        {error && (
          <div className="mt-4 w-full max-w-md">
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
              <p className="text-sm">{error}</p>
            </div>
          </div>
        )}

        {!isUploadInProgress(progress) && (
          <div className="mt-4 w-full max-w-md">
            <label htmlFor="category" className="block text-sm font-medium text-gray-700">
              Select existing folder
            </label>
            <select
              id="category"
              name="category"
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-indigo-600 focus:border-indigo-600 sm:text-sm rounded-md bg-white"
              value={selectedCategory || ""}
              onChange={(e) => setSelectedCategory(e.target.value)}
              disabled={newCategory.trim() !== "" || isUploadInProgress(progress)}
            >
              <option value="">My folder list</option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            <div className="mt-4">
              <label htmlFor="new-category" className="block text-sm font-medium text-gray-700">
                Or Add a New Folder
              </label>
              <input
                type="text"
                id="new-category"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-indigo-600 focus:border-indigo-600 sm:text-sm rounded-md bg-white"
                value={newCategory}
                onChange={(e) => setNewCategory(e.target.value)}
                placeholder="Enter a folder name here or click the box below (folderless files)"
                disabled={selectedCategory !== null || isUploadInProgress(progress)}
              />
            </div>
          </div>
        )}

      {isUploadInProgress(progress) && (
        <div className="mt-32 flex flex-col justify-center items-center gap-5">
          <h2 className="text-lg font-bold text-ike-purple_b mb-4">
            Uploading File: {fileName}
          </h2>
          <DetailedUploadProgress 
            progress={progress} 
            status={status} 
            error={error} 
          />
        </div>
      )}

        {!isUploadInProgress(progress) && (
          <div
            {...getRootProps()}
            className={`p-10 border-2 border-dashed rounded-lg mt-10 w-full max-w-3xl mx-4 transition-colors duration-200 ${
              isDragActive 
                ? "border-indigo-500 bg-indigo-50" 
                : "border-gray-300"
            } hover:border-indigo-500 hover:bg-indigo-50`}
          >
            <input {...getInputProps()} />
            <div className="flex flex-col items-center justify-center text-gray-600">
              {selectedFile ? (
                <div className="space-y-4">
                  <div className="relative inline-block">
                    {preview ? (
                      <img
                        src={preview}
                        alt="Preview"
                        className="max-h-48 rounded-lg mx-auto"
                      />
                    ) : (
                      getFileIcon(selectedFile.type)
                    )}
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        clearSelection();
                      }}
                      className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                      aria-label="Remove file"
                    >
                      <XIcon className="w-4 h-4" />
                    </button>
                  </div>
                  <p className="text-sm text-gray-500">{fileName}</p>
                </div>
              ) : isDragActive ? (
                <>
                  <ThumbsUpIcon className="h-16 w-16 mb-4 animate-bounce text-indigo-600" />
                  <p className="text-indigo-600">Drop the files here ...</p>
                </>
              ) : (
                <>
                  <ArrowDownCircle className="h-16 w-16 mb-4 animate-bounce text-indigo-600" />
                  <div className="space-y-4 text-center">
                    <p className="text-indigo-600">Drag 'n' drop some files here, or click to select files</p>
                    <div className="flex justify-center gap-8 mt-4">
                      <FileIcon className="w-12 h-12 text-indigo-600" />
                      <ImageIcon className="w-12 h-12 text-indigo-600" />
                      <TableIcon className="w-12 h-12 text-indigo-600" />
                      <FileTextIcon className="w-12 h-12 text-indigo-600" />
                    </div>
                    <div className="mt-6 text-gray-600">
                      <p className="font-medium mb-2">Supported formats:</p>
                      <div className="grid grid-cols-2 gap-8">
                        <div>
                          <p className="font-medium mb-1">Documents:</p>
                          <ul className="list-disc list-inside text-sm space-y-1">
                            <li>PDF (.pdf)</li>
                            <li>Word (.doc, .docx)</li>
                            <li>Text (.txt)</li>
                            <li>RTF (.rtf)</li>
                          </ul>
                        </div>
                        <div>
                          <p className="font-medium mb-1">Data & Images:</p>
                          <ul className="list-disc list-inside text-sm space-y-1">
                            <li>Excel (.xls, .xlsx)</li>
                            <li>CSV (.csv)</li>
                            <li>Images (.jpg, .png, .gif, .webp)</li>
                          </ul>
                        </div>
                      </div>
                      <p className="text-sm mt-4">
                        Max size: Images - 4MB, Documents - 50MB
                      </p>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}