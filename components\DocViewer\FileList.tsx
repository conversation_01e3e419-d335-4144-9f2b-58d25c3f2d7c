import React from 'react';
import { File, Download } from 'lucide-react';
import { Timestamp } from 'firebase/firestore';
import { FileData } from './FileManagerstructure';
import { SortConfig } from './FileManagerDirectory';

interface FileListProps {
  files: FileData[];
  handleFileClick: (file: FileData) => void;
  truncateFileName: (fileName: string, maxLength?: number) => string;
  formatTimestamp: (timestamp: Timestamp | undefined) => string;
  sortConfig: SortConfig | null;
  variant: 'unknown' | 'categorized';
}

const FileList: React.FC<FileListProps> = ({
  files,
  handleFileClick,
  truncateFileName,
  formatTimestamp,
  sortConfig,
  variant,
}) => {
  const abbreviateFileType = (type: string | undefined): string => {
    if (!type) return '';
    if (type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      return 'application/docx';
    }
    if (type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      return 'application/xlsx';
    }    
    return type;
  };

  // Sort files based on the current sort configuration
  const sortedFiles = React.useMemo(() => {
    if (!sortConfig) return files;

    return [...files].sort((a, b) => {
      switch (sortConfig.key) {
        case 'files':
          return sortConfig.direction === 'asc'
            ? a.name.localeCompare(b.name)
            : b.name.localeCompare(a.name);
        case 'date':
          if (!a.createdAt || !b.createdAt) return 0;
          return sortConfig.direction === 'asc'
            ? a.createdAt.seconds - b.createdAt.seconds
            : b.createdAt.seconds - a.createdAt.seconds;
        case 'type':
          return sortConfig.direction === 'asc'
            ? (a.type || '').localeCompare(b.type || '')
            : (b.type || '').localeCompare(a.type || '');
        default:
          return 0;
      }
    });
  }, [files, sortConfig]);

  const baseClasses = "grid grid-cols-6 gap-2 ml-2 text-xs justify-center rounded-md bg-opacity-65 mb-1 border-b-2 border-gray-900 p-1 mr-2 transition-colors duration-200 ease-in-out";
  const variantClasses = {
    unknown: "bg-ike-purple_b pl-2 text-white hover:bg-gray-700",
    categorized: "bg-gray-800 text-gray-200 hover:bg-ike-purple_b"
  };

  return (
    <>
      {sortedFiles.map((file) => (
        <div
          key={file.id}
          className={`${baseClasses} ${variantClasses[variant]}`}
        >
          <div className="col-span-2 ml-1 font-normal flex items-center pl-2">
            <File 
              className={`h-4 w-4 inline mr-2 ${
                variant === 'unknown' ? 'text-white' : 'text-blue-400'
              } text-left`}
            />
            <span
              className="hover:text-blue-400 mr-5 transition-colors text-left duration-200 ease-in-out cursor-pointer"
              onClick={() => handleFileClick(file)}
            >
              {truncateFileName(file.name)}
            </span>
          </div>
          <div className="flex items-center justify-center w-full">
            <a
              href={file.downloadUrl}
              download
              className="hover:text-blue-400 transition-colors duration-200 ease-in-out"
            >
              <Download className="h-4 w-4" />
            </a>
          </div>
          <div className="flex items-center justify-center w-full">
            {file.chatCount}
          </div>
          <div className="flex items-center justify-center w-full">
            {formatTimestamp(file.createdAt)}
          </div>
          <div className="flex items-center mr-1 justify-right w-full">
            {abbreviateFileType(file.type)}
          </div>
        </div>
      ))}
    </>
  );
};

export default FileList;