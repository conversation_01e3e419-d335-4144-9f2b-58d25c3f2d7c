export interface VoiceData {
  id: string;
  name: string;
  gender: 'female' | 'male';
  description: string;
  previewText: string;
}

export const AVAILABLE_VOICES: VoiceData[] = [
  // Female voices
  {
    id: 'rCuVrCHOUMY3OwyJBJym',
    name: '<PERSON>',
    gender: 'female',
    description: 'Warm and professional female voice',
    previewText: 'Hi, this is <PERSON> from CastMate, your scene rehearsal partner'
  },
  {
    id: 'QQutlXbwqnU9C4Zprxnn',
    name: '<PERSON>',
    gender: 'female',
    description: 'Clear and articulate female voice',
    previewText: 'Hi, this is <PERSON> from CastMate, your scene rehearsal partner'
  },
  {
    id: 'P7x743VjyZEOihNNygQ9',
    name: '<PERSON>',
    gender: 'female',
    description: 'Friendly and engaging female voice',
    previewText: 'Hi, this is <PERSON> from CastMate, your scene rehearsal partner'
  },
  // Male voices
  {
    id: 'kmSVBPu7loj4ayNinwWM',
    name: '<PERSON>',
    gender: 'male',
    description: 'Confident and clear male voice',
    previewText: 'Hi, this is <PERSON> from CastMate, your scene rehearsal partner'
  },
  {
    id: 'AeRdCCKzvd23BpJoofzx',
    name: '<PERSON>',
    gender: 'male',
    description: 'Smooth and professional male voice',
    previewText: 'Hi, this is Nathaniel from CastMate, your scene rehearsal partner'
  },
  {
    id: 'vVnXvLYPFjIyE2YrjUBE',
    name: 'Brad',
    gender: 'male',
    description: 'Energetic and dynamic male voice',
    previewText: 'Hi, this is Brad from CastMate, your scene rehearsal partner'
  }
];

/**
 * Generates a preview audio URL for a given voice using ElevenLabs TTS API
 */
export async function generateVoicePreview(voiceId: string, text: string): Promise<string> {
  try {
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************';

    const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`, {
      method: 'POST',
      headers: {
        'Accept': 'audio/mpeg',
        'Content-Type': 'application/json',
        'xi-api-key': apiKey,
      },
      body: JSON.stringify({
        text: text,
        model_id: 'eleven_multilingual_v2',
        voice_settings: {
          stability: 0.5,
          similarity_boost: 0.5,
          style: 0.0,
          use_speaker_boost: true
        }
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to generate voice preview: ${response.statusText}`);
    }

    const audioBlob = await response.blob();
    return URL.createObjectURL(audioBlob);
  } catch (error) {
    console.error('Error generating voice preview:', error);
    throw error;
  }
}

/**
 * Gets voice data by ID
 */
export function getVoiceById(voiceId: string): VoiceData | undefined {
  return AVAILABLE_VOICES.find(voice => voice.id === voiceId);
}

/**
 * Gets voices by gender
 */
export function getVoicesByGender(gender: 'female' | 'male'): VoiceData[] {
  return AVAILABLE_VOICES.filter(voice => voice.gender === gender);
}
