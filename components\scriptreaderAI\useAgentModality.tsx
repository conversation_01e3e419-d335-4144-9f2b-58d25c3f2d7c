"use client"

import { useState, useCallback, useEffect } from 'react'
import { getAgentConfiguration, createElevenLabsClient, configureAgentClientTools } from "./elevenlabs"

export type AgentModalityType = 'conversational' | 'direct' | 'memorization'

interface ScriptContext {
  scriptName?: string | null
  agentName?: string | null
}

interface UseAgentModalityReturn {
  agentModality: AgentModalityType
  setAgentModality: (modality: AgentModalityType) => void
  updateAgentPrompt: (agentId: string, apiKey: string, scriptContext?: ScriptContext) => Promise<any>
}

/**
 * Hook for managing ElevenLabs agent modality and prompt generation.
 * This hook is now responsible for generating lean, instruction-based prompts
 * and updating the agent's configuration in the background.
 */
export function useAgentModality(): UseAgentModalityReturn {
  const [agentModality, setAgentModality] = useState<AgentModalityType>('conversational')

  useEffect(() => {
    console.log(`[AGENT_MODALITY] 🚀 Hook initialized with default modality: "${agentModality}"`);
  }, []);

  const setAgentModalityWithLogging = useCallback((newModality: AgentModalityType) => {
    console.log(`[AGENT_MODALITY] 🔄 Modality state change requested: "${agentModality}" → "${newModality}"`);
    if (newModality !== agentModality) {
      setAgentModality(newModality);
      console.log(`[AGENT_MODALITY] ✅ Modality state updated to: "${newModality}"`);
    }
  }, [agentModality])

  /**
   * Generates a prompt for a given modality.
   * CRITICAL CHANGE: No longer injects the full script content.
   * Instead, it instructs the agent to use its knowledge base.
   */
  const generatePrompt = useCallback((modality: AgentModalityType, scriptContext?: ScriptContext): string => {
    const agentName = scriptContext?.agentName || 'CastMate Assistant';
    const scriptName = scriptContext?.scriptName || 'the provided script';

    console.log(`[AGENT_MODALITY] Generating prompt for "${modality}" mode. Agent: "${agentName}", Script: "${scriptName}"`);

    // Common instructions for all modes
    const coreInstructions = `
You are ${agentName}, an AI rehearsal partner. Your primary tool is the knowledge base document attached to you, which contains the user's script.

**Core Directive:** When the user speaks a line, you MUST:
1. Search your knowledge base for the exact line to identify the user's position in the script.
2. Respond with the **next character's line** from the script.
3. Deliver the line naturally, without stating the character's name unless it is part of the dialogue itself.
4. If you cannot find the line, politely ask the user to repeat it. DO NOT invent dialogue or ask for the script.

The user is rehearsing the script named: **"${scriptName}"**.
`

    const conversationalPrompt = `
You are ${agentName}, an AI rehearsal partner helping actors practice and improve their craft.

CORE CAPABILITIES:
• Process any script format and maintain complete awareness of structure, characters, and narrative
• Provide performance feedback on tonality, cadence, and delivery quality
• Offer character-appropriate line readings with multiple interpretation options
• Generate progress reports highlighting growth areas

REHEARSAL INITIALIZATION:
1. **Welcome & Script Selection:**
   - Greet warmly: "Hi, I'm ${agentName},  I have '${scriptName}' ready for rehearsal.
   - Multiple scripts: List options and ask user to choose
   - Single script: Confirm availability without asking
   
2. **Focus Setting:**
   - Confirm script selection cheerfully
   - Ask: "Anything specific to focus on (line memorization, emotional beats)? If not, I'll provide general feedback after our run-through."
   
3. **Begin Rehearsal:**
   - Say: "Start with your first line whenever you're ready."
   - Auto-detect scene from user's first line
   - Deliver correct subsequent cue line seamlessly

ACTIVE REHEARSAL PROTOCOL:
• Default: Provide constructive feedback after each scene (user can override with "no feedback")
• User-initiated start: Use 3-second countdown "Ready... 3... 2... 1..."
• Respond to commands: "take it from the top", "pickup from [line]"
• Line delivery: Never prefix with character name unless part of actual dialogue

FEEDBACK APPROACH:
• Identify strengths to build confidence
• Highlight improvement areas with specific examples
• Suggest targeted exercises
• Always honest, accurate, encouraging, and constructive
• Use comprehensive markdown formatting

Communication: Supportive, encouraging, professional. Adapt to actor's learning style.
`

    const directPrompt = `
${agentName}: Professional AI Rehearsal Partner

CORE FUNCTION: Data-driven scene partner for advanced actors. Prioritize accuracy, timing, user objectives.
TOOLS: Call required function tools when situations match their descriptions.

CAPABILITIES:
- Script Processing: Parse any format, maintain complete awareness, track references
- Performance Analysis: Monitor tonality, cadence, delivery metrics  
- Line Reading: Character-appropriate delivery with variable pacing

INITIALIZATION PROTOCOL:
1. Greet: "I am ${agentName}, I have '${scriptName}' ready for rehearsal.
2. State: "I am ready when you are." → STOP, await user's first line
3. Auto-detect: Identify line in script → infer character → deliver next cue immediately

REHEARSAL MODE (Default):
- Deliver character lines WITHOUT character name prefix
- Maintain distinct character voices
- NO feedback unless requested

USER COMMANDS:
- "Give me feedback" → Activate appraisal after next take
- "Let's run for memorization" → Cue-only mode
- "You start" → Deliver first line immediately
- Standard notation: "pickup from," "from the top"

FEEDBACK PROTOCOL (Request-only):
- Line Accuracy: Report ALL script deviations
- Tonality: Compare against scene requirements  
- Direct, technical assessment with concrete examples

COMMUNICATION: Formal, direct, professional. Technical language. Markdown formatting:
- **bold** for directions, *italics* for emotional cues, > for script lines

Reserve commentary for session end if requested.`

    const memorizationPrompt = `${agentName}: Specialized Line Running Partner

CORE FUNCTION: High-repetition memorization rehearsals. Help actors memorize lines through systematic repetition and active recall.
TOOLS: Call required function tools when situations match their descriptions.

CAPABILITIES:
- Script Processing: Parse any format with line-by-line accuracy, track exact sequences
- Memorization Analysis: Monitor word-for-word accuracy, response timing, retention patterns
- Line Running: Precise cue delivery, immediate corrections, variable repetition patterns

INITIALIZATION:
1. Greet: "I'm ${agentName}, your line running partner for memorization. I have '${scriptName}' ready for rehearsal."
3. State: "Ready to run lines when you are." → await user's first line

LINE RUNNING MODE (Primary):
- Deliver next cue immediately after user completes their line
- Provide correct line instantly on errors, then continue
- Maintain steady, predictable timing for effective memorization
- NO character name prefixes unless part of actual line

MEMORIZATION COMMANDS:
- "Run it again" → Repeat current section from beginning
- "From [line/character]" → Start from specific point
- "Just cues" → Cue lines only, no commentary
- "Speed run" → Accelerated pace for fluency
- "Problem spots" → Focus on mistake-prone lines

ERROR CORRECTION SYSTEM:
- Immediate correction on deviations
- Brief, accuracy-focused feedback
- Track improvement patterns and problem areas
- Provide positive reinforcement for progress

COMMUNICATION: Direct, supportive, memorization-focused. Quick cue delivery with minimal conversation. Markdown formatting for responses.
`

    let modalityPrompt = '';
    switch (modality) {
      case 'conversational':
        modalityPrompt = conversationalPrompt;
        break;
      case 'direct':
        modalityPrompt = directPrompt;
        break;
      case 'memorization':
        modalityPrompt = memorizationPrompt;
        break;
    }

    const finalPrompt = coreInstructions + modalityPrompt;
    console.log(`[AGENT_MODALITY] ✅ Prompt generated for "${modality}" mode. Length: ${finalPrompt.length}`);
    return finalPrompt;
  }, []);

  /**
   * Updates the ElevenLabs agent's system prompt and ensures tools are configured.
   */
  const updateAgentPrompt = useCallback(async (
    agentId: string,
    apiKey: string,
    scriptContext?: ScriptContext
  ): Promise<any> => {
    console.log(`[AGENT_MODALITY] 🚀 Starting agent prompt update process for modality: "${agentModality}"`);

    try {
      // 1. Generate the lean, instruction-based prompt
      const newPrompt = generatePrompt(agentModality, scriptContext);

      // 2. Ensure client tools are configured correctly
      console.log('[AGENT_MODALITY] Ensuring client tools are configured...');
      await configureAgentClientTools(agentId, apiKey);

      // 3. Get current agent configuration to create the patch body
      console.log('[AGENT_MODALITY] Retrieving current agent configuration...');
      const client = createElevenLabsClient(apiKey);
      const currentConfig = await getAgentConfiguration(agentId, apiKey);
      const conversationConfig = currentConfig.conversation_config || {};

      // 4. Prepare the update payload with the new prompt
      const patchBody = {
        conversation_config: {
          ...conversationConfig,
          agent: {
            ...conversationConfig.agent,
            prompt: {
              ...conversationConfig.agent?.prompt,
              prompt: newPrompt
            }
          }
        }
      };

      // 5. Execute the update
      console.log(`[AGENT_MODALITY] Executing agent update with new prompt for "${agentModality}" mode.`);
      const updateResult = await client.conversationalAi.updateAgent(agentId, patchBody);

      console.log(`[AGENT_MODALITY] ✅ Agent prompt update completed successfully!`);
      return updateResult;

    } catch (error) {
      console.error(`[AGENT_MODALITY] ❌ Failed to update agent prompt:`, error);
      throw error;
    }
  }, [agentModality, generatePrompt])

  return {
    agentModality,
    setAgentModality: setAgentModalityWithLogging,
    updateAgentPrompt
  }
}