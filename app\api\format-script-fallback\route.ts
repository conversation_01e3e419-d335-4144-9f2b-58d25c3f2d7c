import { NextRequest, NextResponse } from "next/server";
import { doc, getDoc, setDoc, collection, query, where, getDocs, Timestamp } from "firebase/firestore";
import { db } from "../../../components/firebase";
import { getServerSession } from "next-auth/next";
import { authOptions } from "../auth/[...nextauth]/authOptions";
import { ScriptFormatterTool } from "../../../lib/tools/scriptFormatter";
import { FormattedScriptDocument } from "../../../lib/types/formattedScript";
import { convertFormattedScriptToMarkdownCompact } from "../../../lib/utils/scriptToMarkdown";

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Validate NextAuth session
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: "Unauthorized - No valid session" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { scriptId } = body;
    const userId = session.user.email;

    if (!scriptId) {
      return NextResponse.json(
        { success: false, error: "Script ID is required" },
        { status: 400 }
      );
    }

    // Get the original file document
    const fileDocRef = doc(db, "users", userId, "files", scriptId);
    const fileDocSnap = await getDoc(fileDocRef);

    if (!fileDocSnap.exists()) {
      return NextResponse.json(
        { success: false, error: "Original script file not found" },
        { status: 404 }
      );
    }

    const fileData = fileDocSnap.data();
    const namespace = fileData.namespace || scriptId;

    // Get raw script content from byteStoreCollection
    const chunksRef = collection(db, "users", userId, "byteStoreCollection");
    const q = query(chunksRef, where("metadata.doc_id", "==", namespace));
    const querySnapshot = await getDocs(q);
    const chunks = querySnapshot.docs.map((d) => d.data());

    if (chunks.length === 0) {
      return NextResponse.json(
        { success: false, error: "No content found for this script" },
        { status: 404 }
      );
    }

    // Sort chunks by position
    if ("position" in chunks[0]) {
      chunks.sort((a, b) => (a.position || 0) - (b.position || 0));
    } else if ("metadata" in chunks[0] && "page_number" in chunks[0].metadata) {
      chunks.sort((a, b) => (a.metadata.page_number || 0) - (b.metadata.page_number || 0));
    }

    // Assemble raw content
    const contentField = "pageContent" in chunks[0] ? "pageContent" : "content";
    const rawContent = chunks.map((chunk) => chunk[contentField] || "").join("\n");

    if (!rawContent || rawContent.trim().length === 0) {
      return NextResponse.json(
        { success: false, error: "No content available for formatting" },
        { status: 400 }
      );
    }

    try {
      // Format the script
      const scriptFormatter = new ScriptFormatterTool();
      const formattedScript = await scriptFormatter.formatScript(rawContent, {
        model: "gemini-2.5-pro-preview-05-06",
        provider: "google"
      });

      // Convert to markdown
      const markdownContent = convertFormattedScriptToMarkdownCompact(formattedScript);

      // Store or update formatted script
      const formattedScriptDoc: FormattedScriptDocument = {
        id: scriptId,
        originalFileId: scriptId,
        originalNamespace: namespace,
        formattedMarkdown: markdownContent,
        metadata: formattedScript.metadata,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        userId: userId,
        formattingStatus: 'completed'
      };

      await setDoc(
        doc(db, "users", userId, "formatted_scripts", scriptId),
        formattedScriptDoc
      );

      return NextResponse.json({
        success: true,
        data: {
          id: scriptId,
          formattedMarkdown: markdownContent,
          metadata: formattedScript.metadata,
          formattingStatus: 'completed'
        }
      });

    } catch (formattingError) {
      console.error("Error during fallback script formatting:", formattingError);

      // Store error in formatted_scripts collection
      const errorDoc: FormattedScriptDocument = {
        id: scriptId,
        originalFileId: scriptId,
        originalNamespace: namespace,
        formattedMarkdown: '',
        metadata: {
          title: fileData.name || 'Untitled Script',
          author: '',
          characters: [],
          summary: ''
        },
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        userId: userId,
        formattingStatus: 'failed',
        formattingError: formattingError instanceof Error ? formattingError.message : String(formattingError)
      };

      await setDoc(
        doc(db, "users", userId, "formatted_scripts", scriptId),
        errorDoc
      );

      return NextResponse.json({
        success: false,
        error: "Script formatting failed",
        formattingError: formattingError instanceof Error ? formattingError.message : String(formattingError)
      }, { status: 500 });
    }

  } catch (error) {
    console.error("Error in fallback formatting:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : "Unknown error occurred" 
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS(req: NextRequest): Promise<NextResponse> {
  return NextResponse.json({}, { status: 200 });
}
