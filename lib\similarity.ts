import { OpenAIEmbeddings } from "@langchain/openai";
import { decode, encode } from "gpt-3-encoder";

// Simple cosine similarity function
function cosineSimilarity(vecA: number[], vecB: number[]): number {
  const dotProduct = vecA.reduce((sum, a, i) => sum + a * vecB[i], 0);
  const magnitudeA = Math.sqrt(vecA.reduce((sum, a) => sum + a * a, 0));
  const magnitudeB = Math.sqrt(vecB.reduce((sum, b) => sum + b * b, 0));
  return dotProduct / (magnitudeA * magnitudeB);
}

export async function rankChunksByRelevance(
  chunks: string[],
  userQuery: string,
  maxTokens: number = 5000,
  maxChunks: number = 1
): Promise<string> {
  const embeddings = new OpenAIEmbeddings();
  const chunkEmbeddings = await embeddings.embedDocuments(chunks);
  const queryEmbedding = await embeddings.embedQuery(userQuery);

  const rankedChunks = chunks
    .map((chunk, i) => ({
      chunk,
      similarity: cosineSimilarity(chunkEmbeddings[i], queryEmbedding),
    }))
    .sort((a, b) => b.similarity - a.similarity);

  let totalTokens = 0;
  const selectedChunks = [];

  for (const { chunk } of rankedChunks) {
    const chunkTokens = encode(chunk).length;
    if (totalTokens + chunkTokens > maxTokens || selectedChunks.length >= maxChunks) {
      break;
    }
    selectedChunks.push(chunk);
    totalTokens += chunkTokens;
  }

  return selectedChunks.join("\n\n");
}

export function truncateToTokenLimit(text: string, maxTokens: number): string {
  const tokens = encode(text);
  if (tokens.length <= maxTokens) {
    return text;
  }
  return decode(tokens.slice(0, maxTokens));
}