import React, { useEffect, useState } from 'react';
import { ChatBubbleLeftIcon } from '@heroicons/react/24/solid';
import ChatHistoryRow from '../ChatHistoryRow';

interface DocumentData {
  id: string;
  name: string;
}

interface ChatData {
  id: string;
  namespace: string;
  category: string;
  firstMessage: string;
  createdAt: {
    seconds: number;
    nanoseconds: number;
  } | string;
}

interface MenuSectionProps {
  menuName: string;
  toggleMenu: () => void;
  expanded: boolean;
  documents: DocumentData[];
  selectedDocId: string | null;
  handleDocumentChange: (newSelectedId: string, menuName: string) => void;
  createNewChat: (message: string) => Promise<void>;
  loading: boolean;
  error: boolean;
  chats: ChatData[];
  selectedChatId: string | null;
  handleSelectChat: (chatId: string | null, menuName: string) => void;
  handleDeleteChat: (id: string) => Promise<void>;
  isFirst: boolean;
  isLast: boolean;
  additionalClasses?: string;
}

const MenuSection: React.FC<MenuSectionProps> = ({
  menuName,
  toggleMenu,
  expanded,
  documents,
  selectedDocId,
  handleDocumentChange,
  createNewChat,
  loading,
  error,
  chats,
  selectedChatId,
  handleSelectChat,
  handleDeleteChat,
  isFirst,
  isLast,
  additionalClasses,
}) => {
  const [sortedChats, setSortedChats] = useState<ChatData[]>([]);
  const roundedClass = isFirst ? 'rounded-t-lg' : isLast ? 'rounded-b-lg' : '';

  // Time periods for chat filtering - now in chronological order
  const timePeriods = [
    { label: 'Today', value: 'today' },
    { label: 'Yesterday', value: 'yesterday' },
    { label: 'Last 7 days', value: 'week' },
    { label: 'Last 30 days', value: 'month' },
    { label: 'Older', value: 'older' }
  ];

  const sortChats = (chats: ChatData[]) => {
    return [...chats].sort((a, b) => {
      const getTime = (chat: ChatData) => {
        const timestamp = chat.createdAt;
        if (typeof timestamp === 'string') {
          return new Date(timestamp).getTime();
        }
        if (timestamp && 'seconds' in timestamp) {
          return timestamp.seconds * 1000;
        }
        return 0;
      };

      return getTime(b) - getTime(a);
    });
  };

  useEffect(() => {
    if (chats && chats.length > 0) {
      setSortedChats(sortChats(chats));
    } else {
      setSortedChats([]);
    }
  }, [chats]);

  const onDocumentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSelectedId = e.target.value;
    handleDocumentChange(newSelectedId, menuName);
  };

  const handleNewChat = (e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    createNewChat('Begin Messaging');
  };

  const handleChatSelect = async (id: string | null) => {
    await handleSelectChat(id, menuName);
  };

  return (
    <div
      className={`bg-gray-300 bg-opacity-65 pt-2 pl-2 pb-3 pr-2 border-b mb-1 border-gray-800 ${roundedClass} ${
        additionalClasses || ''
      }`}
    >
      <div
        onClick={toggleMenu}
        className="flex items-center justify-between cursor-pointer text-blue-700 hover:text-blue-200"
      >
        <p>{menuName}</p>
        <span>{expanded ? '▼' : '▶'}</span>
      </div>

      {expanded && (
        <div className="mt-2">
          {documents.length > 0 ? (
            <>
              <select
                value={selectedDocId || ''}
                onChange={onDocumentChange}
                className="p-1 bg-ike-dark-purple text-gray-200 rounded border border-gray-700 w-48 ml-2"
              >
                <option value="" disabled>
                  {menuName === 'Groups' ? 'Select a group' : 'Select a file'}
                </option>
                {documents.map((doc) => (
                  <option key={doc.id} value={doc.id}>
                    {doc.name}
                  </option>
                ))}
              </select>
            </>
          ) : (
            <div className="text-gray-300 ml-2">
              <p>No {menuName.toLowerCase()} available.</p>
            </div>
          )}

          <div
            onClick={handleNewChat}
            className="flex items-center text-left space-x-2 mr-2 cursor-pointer hover:text-green-500 transition-all duration-200 ease-out p-1 mt-3"
          >
            <div className="relative">
              <ChatBubbleLeftIcon className="h-5 w-5 text-ike-purple" />
            </div>
            <span className="text-ike-purple">New Chat</span>
          </div>

          <hr className="mt-2 mb-2 border-gray-600" />

          <div className="-mt-1">
            {loading && <div className="text-gray-300">Loading chats...</div>}
            {!loading && sortedChats.length === 0 && selectedDocId && (
              <div className="text-gray-300 ml-2">No chats yet</div>
            )}
            {!loading && selectedDocId && (
              <ChatHistoryRow
                chats={sortedChats.map(chat => ({
                  id: chat.id,
                  firstMessage: chat.firstMessage,
                  createdAt: typeof chat.createdAt === 'string' 
                    ? new Date(chat.createdAt).getTime()
                    : chat.createdAt.seconds * 1000
                }))}
                selectedChatId={selectedChatId}
                onSelectChat={handleChatSelect}
                onDeleteChat={handleDeleteChat}
                menuName={menuName}
                isLoading={loading}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MenuSection;