// /lib/schemas/schemaHandler.ts

import { z } from "zod";
import { metadataSchemaV1 } from "./schema_v1";
// Import additional schema versions as needed

export function validateSchema(data: any, version: string = "v1"): z.SafeParseReturnType<any, any> {
  switch (version) {
    case "v1":
      return metadataSchemaV1.safeParse(data);
    // Add cases for other versions
    default:
      throw new Error(`Unsupported schema version: ${version}`);
  }
}
