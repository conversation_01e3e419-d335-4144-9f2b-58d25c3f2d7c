import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { collection, query, getDocs, updateDoc, doc } from 'firebase/firestore';
import { db } from 'components/firebase';
import { FolderPlus, XCircle } from 'lucide-react';
import router from 'next/router';

interface FileData {
  id: string;
  name: string;
  category?: string;
  [key: string]: any;
}

interface SelectedFiles {
  [key: string]: boolean;
}

interface Props {
  onUpdate: () => void;
}

const CreateFoldersModal: React.FC<Props> = ({ onUpdate }) => {
  const { data: session } = useSession();
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [files, setFiles] = useState<FileData[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<SelectedFiles>({});
  const [newFolderName, setNewFolderName] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [error, setError] = useState<string>('');

  useEffect(() => {
    if (session?.user?.email && isOpen) {
      fetchFiles();
    }
  }, [session?.user?.email, isOpen]);

  const clearState = () => {
    setError('');
    setSuccessMessage('');
    setShowSuccess(false);
    setIsSubmitting(false);
    setSelectedFiles({});
    setNewFolderName('');
  };

  const handleClose = () => {
    clearState();
    setIsOpen(false);
  };

  const fetchFiles = async (): Promise<void> => {
    if (!session?.user?.email) return;

    try {
      const filesCollection = collection(db, 'users', session.user.email, 'files');
      const filesSnapshot = await getDocs(filesCollection);
      const filesData = filesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as FileData[];
      
      const sortedFiles = filesData.sort((a, b) => 
        a.name.toLowerCase().localeCompare(b.name.toLowerCase())
      );
      
      setFiles(sortedFiles);
    } catch (error) {
      console.error('Error fetching files:', error);
      setError('Failed to load files. Please try again.');
    }
  };

  const handleCheckboxChange = (fileId: string): void => {
    setSelectedFiles(prev => ({
      ...prev,
      [fileId]: !prev[fileId]
    }));
  };

  const handleSubmit = async (): Promise<void> => {
    // Clear any existing messages
    setError('');
    setSuccessMessage('');
    setShowSuccess(false);

    if (!session?.user?.email) {
      setError('No user session found. Please log in again.');
      return;
    }

    if (!newFolderName.trim()) {
      setError('Please enter a folder name');
      return;
    }

    const selectedFileIds = Object.entries(selectedFiles)
      .filter(([_, isSelected]) => isSelected)
      .map(([fileId]) => fileId);

    if (selectedFileIds.length === 0) {
      setError('Please select at least one file');
      return;
    }

    setIsSubmitting(true);

    try {
      const updatePromises = selectedFileIds.map(fileId => 
        updateDoc(doc(db, 'users', session.user?.email!, 'files', fileId), {
          category: newFolderName
        })
      );

      await Promise.all(updatePromises);
      
      const fileCount = selectedFileIds.length;
      setSuccessMessage(
        `Successfully moved ${fileCount} ${fileCount === 1 ? 'file' : 'files'} to folder "${newFolderName}"`
      );
      setShowSuccess(true);
      
      // Trigger sidebar update
      onUpdate();
      router.push('/fileManager') // Redirect to a 404 page or handle as needed
      // Close modal after delay
      setTimeout(() => {
        handleClose();
      }, 2000);

    } catch (error) {
      console.error('Error updating files:', error);
      //setError('Failed to update files. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <div 
        onClick={() => setIsOpen(true)}
        className="flex items-center px-4 py-2 text-white hover:text-blue-200 cursor-pointer"
      >
        <FolderPlus className="h-4 w-4 -ml-3 mr-2 text-ike-purple" />
        <p>Create folders for existing files</p>
      </div>

      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-ike-dark-purple rounded-lg w-full max-w-md max-h-[80vh] overflow-hidden mx-4">
            <div className="flex justify-between items-center p-4 border-b border-gray-700">
              <h2 className="text-white text-lg font-semibold">Create New Folder</h2>
              <button
                onClick={handleClose}
                className="text-gray-400 hover:text-white"
                type="button"
              >
                <XCircle className="h-6 w-6" />
              </button>
            </div>

            <div className="p-4 overflow-y-auto">
              <div className="mb-4">
                <label 
                  htmlFor="folderName" 
                  className="block text-white mb-2"
                >
                  New Folder Name:
                </label>
                <input
                  id="folderName"
                  type="text"
                  value={newFolderName}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                    setNewFolderName(e.target.value)
                  }
                  className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-ike-purple_b"
                  placeholder="Enter folder name"
                />
              </div>

              {error && (
                <div className="mb-4 p-3 bg-red-500 bg-opacity-20 border border-red-500 rounded-lg text-red-500">
                  {error}
                </div>
              )}

              {showSuccess && (
                <div className="mb-4 p-3 bg-green-500 bg-opacity-20 border border-green-500 rounded-lg text-green-500">
                  {successMessage}
                </div>
              )}

              <div className="mb-4">
                <label className="block text-white mb-2">Select Files:</label>
                <div className="border border-gray-700 rounded-lg p-3 max-h-60 overflow-y-auto">
                  {files.map((file) => (
                    <div key={file.id} className="flex items-center mb-2 last:mb-0">
                      <input
                        type="checkbox"
                        id={file.id}
                        checked={selectedFiles[file.id] || false}
                        onChange={() => handleCheckboxChange(file.id)}
                        className="mr-2 text-ike-purple_b focus:ring-ike-purple_b"
                      />
                      <label 
                        htmlFor={file.id}
                        className="text-white cursor-pointer hover:text-amber-400"
                      >
                        {file.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                type="button"
                className={`w-full py-2 px-4 rounded-lg text-white 
                  ${isSubmitting 
                    ? 'bg-gray-600 cursor-not-allowed' 
                    : 'bg-ike-purple hover:bg-ike-purple_b'
                  }`}
              >
                {isSubmitting ? 'Processing...' : 'Create Folder and Move Files'}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CreateFoldersModal;