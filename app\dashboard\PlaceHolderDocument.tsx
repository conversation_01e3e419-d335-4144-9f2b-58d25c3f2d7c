// components/PlaceHolderDocument.tsx

import React from 'react';
import { ArrowUpTrayIcon } from '@heroicons/react/24/solid'; // Import the icon

function PlaceHolderDocument() {
  return (
    <button className="flex flex-col justify-center items-center w-64 h-80 rounded-xl bg-gray-200 drop-shadow-md text-gray-400">
      <ArrowUpTrayIcon className="h-12 w-12 mb-2" />
      <p>Add a document</p>
    </button>
  );
}

export default PlaceHolderDocument;
