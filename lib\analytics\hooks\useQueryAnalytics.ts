'use client';

import { useAnalytics } from '../analyticsProvider';

export function useQueryAnalytics() {
  const { analyticsMiddleware, isInitialized } = useAnalytics();

  const trackQuery = async (
    userId: string,
    namespaces: string[] | null,
    query: string,
    chatHistory: string,
    category: string | null
  ) => {
    if (!isInitialized || !analyticsMiddleware) {
      console.warn('Analytics not initialized');
      return null;
    }

    return await analyticsMiddleware.interceptQueryStart(
      userId,
      namespaces,
      query,
      chatHistory,
      category
    );
  };

  const trackQueryEnd = async (totalMatches: number, uniqueNamespaces: number) => {
    if (!isInitialized || !analyticsMiddleware) {
      console.warn('Analytics not initialized');
      return;
    }

    await analyticsMiddleware.interceptQueryEnd(totalMatches, uniqueNamespaces);
  };

  return {
    trackQuery,
    trackQueryEnd,
    isInitialized,
    analyticsMiddleware
  };
}