import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";
import { ref, uploadBytes, getDownloadURL, deleteObject, listAll, getMetadata } from "firebase/storage";
import { storage } from "../../../components/firebase";
import { v4 as uuidv4 } from "uuid";

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({
        success: false,
        error: "Authentication required",
        details: "Please sign in to record video"
      }, { status: 401 });
    }

    const userId = session.user.email;

    // Process the video file
    const formData = await req.formData();
    const videoFile = formData.get("video") as File;
    const rehearsalId = formData.get("rehearsalId") as string;
    const scriptName = formData.get("scriptName") as string;
    const duration = formData.get("duration") as string;

    if (!videoFile) {
      return NextResponse.json({
        success: false,
        error: "No video file provided"
      }, { status: 400 });
    }

    // Check file size (limit to 100MB)
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (videoFile.size > maxSize) {
      return NextResponse.json({
        success: false,
        error: "Video file too large",
        details: "Maximum file size is 100MB"
      }, { status: 400 });
    }

    console.log(`[SelfTake Video API] Processing video file: ${videoFile.name} (${videoFile.size} bytes) for user: ${userId}`);

    // Generate unique filename
    const timestamp = Date.now();
    const recordingId = uuidv4();
    const fileExtension = videoFile.type.includes('webm') ? 'webm' : 'mp4';
    const filename = `selftake-${rehearsalId || timestamp}.${fileExtension}`;

    // Upload to Firebase Storage
    const videoBuffer = Buffer.from(await videoFile.arrayBuffer());
    const storageRef = ref(storage, `${userId}/selfTakeVideo/${filename}`);

    await uploadBytes(storageRef, videoBuffer, {
      contentType: videoFile.type,
      customMetadata: {
        rehearsalId: rehearsalId || `rehearsal-${timestamp}`,
        scriptName: scriptName || "Unknown Script",
        recordingId: recordingId,
        uploadedAt: new Date().toISOString(),
        duration: duration || "0",
        fileSize: videoFile.size.toString(),
        type: "video"
      }
    });

    const videoUrl = await getDownloadURL(storageRef);

    console.log("[SelfTake Video API] Video uploaded successfully:", filename);

    return NextResponse.json({
      success: true,
      id: recordingId,
      filename: filename,
      url: videoUrl,
      timestamp: new Date().toISOString(),
      rehearsalId: rehearsalId || `rehearsal-${timestamp}`,
      type: "video",
      duration: duration ? parseInt(duration) : 0,
      fileSize: videoFile.size
    });

  } catch (error) {
    console.error("[SelfTake Video API] Error:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";

    return NextResponse.json({
      success: false,
      error: errorMessage,
      details: "Failed to process self-take video recording"
    }, { status: 500 });
  }
}

export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({
        success: false,
        error: "Authentication required"
      }, { status: 401 });
    }

    const userId = session.user.email;
    const { searchParams } = new URL(req.url);
    const requestedUserId = searchParams.get('userId');

    // Verify user can only access their own recordings
    if (requestedUserId !== userId) {
      return NextResponse.json({
        success: false,
        error: "Access denied"
      }, { status: 403 });
    }

    // List all video recordings for the user
    const storageRef = ref(storage, `${userId}/selfTakeVideo/`);

    try {
      const listResult = await listAll(storageRef);
      const recordings = await Promise.all(
        listResult.items.map(async (itemRef) => {
          try {
            const url = await getDownloadURL(itemRef);
            const metadata = await getMetadata(itemRef);

            return {
              id: metadata.customMetadata?.recordingId || itemRef.name,
              filename: itemRef.name,
              url: url,
              timestamp: metadata.customMetadata?.uploadedAt || metadata.timeCreated,
              rehearsalId: metadata.customMetadata?.rehearsalId || 'unknown',
              scriptName: metadata.customMetadata?.scriptName || 'Unknown Script',
              type: "video",
              duration: metadata.customMetadata?.duration ? parseInt(metadata.customMetadata.duration) : 0,
              fileSize: metadata.customMetadata?.fileSize ? parseInt(metadata.customMetadata.fileSize) : metadata.size
            };
          } catch (error) {
            console.error(`Error processing video recording ${itemRef.name}:`, error);
            return null;
          }
        })
      );

      // Filter out failed recordings and sort by timestamp (newest first)
      const validRecordings = recordings
        .filter(recording => recording !== null)
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      return NextResponse.json({
        success: true,
        recordings: validRecordings
      });

    } catch (error) {
      // If the folder doesn't exist, return empty array
      if (error instanceof Error && error.message.includes('does not exist')) {
        return NextResponse.json({
          success: true,
          recordings: []
        });
      }
      throw error;
    }

  } catch (error) {
    console.error("[SelfTake Video API] GET Error:", error);
    return NextResponse.json({
      success: false,
      error: "Failed to load video recordings"
    }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({
        success: false,
        error: "Authentication required"
      }, { status: 401 });
    }

    const userId = session.user.email;
    const { searchParams } = new URL(req.url);
    const recordingId = searchParams.get('id');
    const requestedUserId = searchParams.get('userId');

    // Verify user can only delete their own recordings
    if (requestedUserId !== userId) {
      return NextResponse.json({
        success: false,
        error: "Access denied"
      }, { status: 403 });
    }

    if (!recordingId) {
      return NextResponse.json({
        success: false,
        error: "Recording ID required"
      }, { status: 400 });
    }

    // List all video recordings to find the one with matching ID
    const storageRef = ref(storage, `${userId}/selfTakeVideo/`);
    const listResult = await listAll(storageRef);

    let targetFile = null;
    for (const itemRef of listResult.items) {
      try {
        const metadata = await getMetadata(itemRef);
        if (metadata.customMetadata?.recordingId === recordingId || itemRef.name.includes(recordingId)) {
          targetFile = itemRef;
          break;
        }
      } catch (error) {
        console.error(`Error checking metadata for ${itemRef.name}:`, error);
      }
    }

    if (!targetFile) {
      return NextResponse.json({
        success: false,
        error: "Video recording not found"
      }, { status: 404 });
    }

    // Delete the file
    await deleteObject(targetFile);

    console.log(`[SelfTake Video API] Deleted video recording: ${targetFile.name} for user: ${userId}`);

    return NextResponse.json({
      success: true,
      message: "Video recording deleted successfully"
    });

  } catch (error) {
    console.error("[SelfTake Video API] DELETE Error:", error);
    return NextResponse.json({
      success: false,
      error: "Failed to delete video recording"
    }, { status: 500 });
  }
}
