interface ChunkMetadata {
  chunkId: string;
  document_title: string;
  page_number: number;
  text: string;
  embedding?: number[];
  namespace?: string;
  doc_id?: string;  // Added to preserve document ID
}

interface ProcessedChunk {
  content: string;
  metadata: ChunkMetadata;
  relevanceScore: number;
  tokenCount: number;
  diversityScore: number;
  namespace?: string;
}

export class DocumentProcessor {
  private countTokens(text: string): number {
    const words = text.trim().split(/[\s,.!?;:()\[\]'"]+/);
    const charCount = text.length;
    return Math.max(words.length, Math.ceil(charCount / 4));
  }

  private calculateSimilarity(vecA: number[], vecB: number[]): number {
    if (!vecA || !vecB || vecA.length !== vecB.length) {
      console.warn("Invalid vectors for similarity calculation:", {
        vecALength: vecA?.length,
        vecBLength: vecB?.length,
        vectorA: !!vecA,
        vectorB: !!vecB
      });
      return 0;
    }

    try {
      const dotProduct = vecA.reduce((acc, cur, i) => acc + cur * vecB[i], 0);
      const magnitudeA = Math.sqrt(vecA.reduce((acc, cur) => acc + cur * cur, 0));
      const magnitudeB = Math.sqrt(vecB.reduce((acc, cur) => acc + cur * cur, 0));
      
      if (magnitudeA === 0 || magnitudeB === 0) {
        console.warn("Zero magnitude vector detected");
        return 0;
      }
      
      return Math.max(0, Math.min(1, dotProduct / (magnitudeA * magnitudeB)));
    } catch (error) {
      console.error("Error calculating similarity:", error);
      return 0;
    }
  }

  private async processChunk(
    doc: any,
    queryEmbedding: number[]
  ): Promise<ProcessedChunk> {
    // First log the incoming document structure
    console.log("Processing incoming chunk:", {
      hasMetadata: !!doc.metadata,
      hasValues: !!doc.values,
      metadataKeys: doc.metadata ? Object.keys(doc.metadata) : [],
      docKeys: Object.keys(doc)
    });

    // Extract metadata from the enriched document structure
    const chunkId = doc.metadata?.chunk_id || doc.id || 'unknown';
    // Use doc_id as namespace if available, fallback to existing namespace
    const namespace = doc.metadata?.doc_id || doc.metadata?.namespace || '';
    
    // Get embedding from the enriched document (should be at root level after enrichment)
    const embeddingVector = doc.values;

    // Log embedding extraction attempt with correct chunk ID
    console.log("Embedding extraction attempt:", {
      chunkId,
      namespace,
      hasValues: !!doc.values,
      hasMetadataValues: !!doc.metadata?.values,
      hasMetadataEmbedding: !!doc.metadata?.embedding,
      vectorLength: embeddingVector?.length,
      doc_id: doc.metadata?.doc_id
    });

    // Get content from pageContent (from Firestore) or text (from metadata)
    const text = doc.pageContent || doc.metadata?.text || '';
    if (!text) {
      console.warn("Empty content for chunk:", chunkId);
    }

    // Construct metadata with all available fields
    const metadata: ChunkMetadata = {
      chunkId,
      document_title: doc.metadata?.document_title || 'Unknown Document',
      page_number: doc.metadata?.page_number || 0,
      text,
      embedding: embeddingVector,
      namespace,
      doc_id: doc.metadata?.doc_id  // Preserve doc_id in metadata
    };

    // Calculate relevance score
    const relevanceScore = embeddingVector && embeddingVector.length === queryEmbedding.length ? 
      this.calculateSimilarity(queryEmbedding, embeddingVector) : 0;

    const result: ProcessedChunk = {
      content: text,
      metadata,
      relevanceScore,
      tokenCount: this.countTokens(text),
      diversityScore: 0,
      namespace
    };

    // Log processed result with the correct chunk ID
    console.log(`Processed chunk ${chunkId}:`, {
      namespace: result.namespace,
      doc_id: metadata.doc_id,
      hasContent: !!result.content,
      contentLength: result.content.length,
      hasEmbedding: !!embeddingVector,
      embeddingLength: embeddingVector?.length,
      relevanceScore: result.relevanceScore
    });

    return result;
  }

  public async selectRelevantChunks(
    documentChunks: any[],
    queryEmbedding: number[],
    options: {
      maxInputTokens?: number;
      systemPromptTokens?: number;
      chatHistoryTokens?: number;
      minChunks?: number;
      maxChunks?: number;
    } = {}
  ) {
    console.log("Starting chunk processing with:", {
      totalChunks: documentChunks.length,
      queryEmbeddingLength: queryEmbedding.length,
      uniqueNamespaces: new Set(documentChunks.map(d => d.metadata?.doc_id)).size,
      options
    });

    const {
      maxInputTokens = 4500,
      systemPromptTokens = 1000,
      chatHistoryTokens = 800,
      minChunks = 2,
      maxChunks = 10
    } = options;

    // Process all chunks first
    const processedChunks = await Promise.all(
      documentChunks.map(doc => this.processChunk(doc, queryEmbedding))
    );

    // Filter out invalid chunks and sort by relevance
    const validChunks = processedChunks
      .filter(chunk => chunk.content && chunk.content.length > 0)
      .sort((a, b) => b.relevanceScore - a.relevanceScore);

    console.log("Valid chunks by namespace:", 
      Object.fromEntries(
        Array.from(new Set(validChunks.map(c => c.metadata.doc_id)))
          .map(docId => [docId || 'unknown', validChunks.filter(c => c.metadata.doc_id === docId).length])
      )
    );

    // Select chunks while respecting namespace distribution
    const selectedChunks: ProcessedChunk[] = [];
    let totalTokens = 0;
    const availableTokens = maxInputTokens - systemPromptTokens - chatHistoryTokens;
    
    // Ensure we get at least one chunk from each namespace if possible
    const docIds = new Set(validChunks.map(c => c.metadata.doc_id).filter(Boolean));
    for (const docId of docIds) {
      const bestChunkForNamespace = validChunks.find(c => 
        c.metadata.doc_id === docId && 
        !selectedChunks.includes(c) &&
        totalTokens + c.tokenCount <= availableTokens
      );
      
      if (bestChunkForNamespace) {
        selectedChunks.push(bestChunkForNamespace);
        totalTokens += bestChunkForNamespace.tokenCount;
      }
    }

    // Fill remaining slots with best available chunks
    while (selectedChunks.length < maxChunks && 
           totalTokens < availableTokens && 
           validChunks.length > selectedChunks.length) {
      const nextChunk = validChunks.find(c => !selectedChunks.includes(c));
      if (nextChunk && totalTokens + nextChunk.tokenCount <= availableTokens) {
        selectedChunks.push(nextChunk);
        totalTokens += nextChunk.tokenCount;
      } else {
        break;
      }
    }

    // Sort final selection by relevance
    selectedChunks.sort((a, b) => b.relevanceScore - a.relevanceScore);

    console.log(`Selected ${selectedChunks.length} chunks:`, {
      totalTokens,
      namespaceDistribution: Object.fromEntries(
        Array.from(new Set(selectedChunks.map(c => c.metadata.doc_id)))
          .map(docId => [docId || 'unknown', selectedChunks.filter(c => c.metadata.doc_id === docId).length])
      ),
      averageRelevance: selectedChunks.reduce((acc, c) => acc + c.relevanceScore, 0) / selectedChunks.length
    });

    return {
      content: selectedChunks
        .map(chunk => {
          const metadata = chunk.metadata;
          return `[Source: ${metadata.document_title}, Page: ${metadata.page_number}, DocID: ${metadata.doc_id || 'Unknown'}]\n${chunk.content.trim()}`;
        })
        .join('\n\n'),
      metadata: {
        sources: selectedChunks.map(chunk => ({
          title: chunk.metadata.document_title,
          page: chunk.metadata.page_number,
          doc_id: chunk.metadata.doc_id,
          relevance: chunk.relevanceScore
        })),
        totalTokens,
        chunkCount: selectedChunks.length,
        averageRelevance: selectedChunks.reduce((acc, c) => acc + c.relevanceScore, 0) / selectedChunks.length,
        namespaceDistribution: Object.fromEntries(
          Array.from(new Set(selectedChunks.map(c => c.metadata.doc_id)))
            .map(docId => [docId || 'unknown', selectedChunks.filter(c => c.metadata.doc_id === docId).length])
        )
      }
    };
  }
}