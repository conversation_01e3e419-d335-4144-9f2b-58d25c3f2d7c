// lib/validation/duplicationCheck.ts

import { db } from "components/firebase";
import { collection, query, where, getDocs } from "firebase/firestore";

/**
 * Checks for duplicates in a specified Firestore collection based on a field value.
 * @param collectionName - The name of the Firestore collection.
 * @param fieldName - The field to check for duplicates.
 * @param fieldValue - The value of the field to match.
 * @returns A boolean indicating if a duplicate exists.
 */
export async function checkForDuplicates(
  collectionName: string,
  fieldName: string,
  fieldValue: string
): Promise<boolean> {
  try {
    const q = query(collection(db, collectionName), where(fieldName, "==", fieldValue));
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  } catch (error) {
    console.error(`Duplication check failed for ${collectionName}:`, error);
    return false; // Assuming no duplicate if check fails
  }
}
