import React, { useState } from 'react';
import Image from 'next/image';
import { doc, setDoc } from 'firebase/firestore';
import { db } from '../components/firebase';
import { useRouter } from 'next/navigation';

interface UserDetailsModalProps {
  email: string | null | undefined;
  name: string | null | undefined;
  onComplete: () => void;
  onClose: () => void;
}

interface UserAccount {
  firstName: string;
  surname: string;
  email: string;
  city: string;
  country: string;
  createdAt: string;
}

export default function UserDetailsModal({ 
  email = '', 
  name = '', 
  onComplete,
  onClose
}: UserDetailsModalProps) {
  const router = useRouter();
  const [firstName, setFirstName] = useState<string>(name?.split(' ')[0] || '');
  const [surname, setSurname] = useState<string>(name?.split(' ')[1] || '');
  const [city, setCity] = useState<string>('');
  const [country, setCountry] = useState<string>('');
  const [error, setError] = useState<string>('');

  const countries: string[] = [
    'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 
    'Japan', 'India', 'Brazil', 'Mexico', 'South Africa', 'China', 'Russia', 
    'Italy', 'Spain', 'Netherlands', 'Sweden', 'Norway', 'Denmark', 'Finland', 
    'New Zealand', 'Ireland', 'Switzerland', 'Austria'
  ].sort();

  const validateForm = (): boolean => {
    if (!firstName || !surname || !city || !country) {
      setError('All fields are required');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!validateForm() || !email) return;

    try {
      const userData: UserAccount = {
        firstName,
        surname,
        email,
        city,
        country,
        createdAt: new Date().toISOString(),
      };

      await setDoc(doc(db, 'Accounts', email), userData);
      onComplete();
    } catch (error) {
      setError('Failed to save user details. Please try again.');
      console.error('Error saving user details:', error);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  const RequiredAsterisk = () => (
    <span className="text-red-500 ml-1" aria-label="required">*</span>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-8 rounded-lg shadow-lg w-96 relative text-black">
        <div className="flex flex-col items-center mb-6">
          <Image src="/logo.png" alt="Company Logo" width={80} height={80} />
          <h2 className="text-xl font-semibold mt-4 text-gray-800">Complete Your Profile</h2>
          <p className="text-sm text-gray-500 mt-2">
            Fields marked with <span className="text-red-500">*</span> are mandatory
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
              First Name
              <RequiredAsterisk />
            </label>
            <input
              id="firstName"
              type="text"
              value={firstName}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFirstName(e.target.value)}
              placeholder="Enter your first name"
              className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label htmlFor="surname" className="block text-sm font-medium text-gray-700 mb-1">
              Surname
              <RequiredAsterisk />
            </label>
            <input
              id="surname"
              type="text"
              value={surname}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSurname(e.target.value)}
              placeholder="Enter your surname"
              className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">
              City/Town
              <RequiredAsterisk />
            </label>
            <input
              id="city"
              type="text"
              value={city}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setCity(e.target.value)}
              placeholder="Enter your city or town"
              className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-1">
              Country
              <RequiredAsterisk />
            </label>
            <select
              id="country"
              value={country}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setCountry(e.target.value)}
              className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="" disabled>Please select your country</option>
              {countries.map((c) => (
                <option key={c} value={c}>{c}</option>
              ))}
            </select>
          </div>

          {error && (
            <p className="text-red-500 text-sm">{error}</p>
          )}

          <div className="flex gap-4">
            <button
              type="submit"
              className="flex-1 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition duration-200"
            >
              Complete Profile
            </button>
            <button
              type="button"
              onClick={handleCancel}
              className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition duration-200"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}