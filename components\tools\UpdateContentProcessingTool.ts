import { Tool } from "@langchain/core/tools";
import { TokenManagement } from "@/src/tokenTracker/tokenManagement";

interface ContentProcessingMetadata {
  totalTokens: number;
  chunkCount: number;
  averageRelevance: number;
  namespaceDistribution: Record<string, number>;
  [key: string]: any; // Allow for additional metadata fields
}

interface UpdateContentProcessingOutput {
  success: boolean;
  updatedTokenCount: number;
  error?: string;
  metadata?: ContentProcessingMetadata;
}

/**
 * UpdateContentProcessingTool
 * Updates token tracking with processed content metadata
 */
export class UpdateContentProcessingTool extends Tool {
  name = "updateContentProcessing";
  description = "Update token tracking with processed content metadata";

  constructor(private tokenManager: TokenManagement) {
    super();
  }

  async _call(metadata: ContentProcessingMetadata): Promise<UpdateContentProcessingOutput> {
    try {
      // Validate input metadata
      this.validateMetadata(metadata);

      // Update token tracking with the provided metadata
      await this.tokenManager.updateContentProcessing({
        totalTokens: metadata.totalTokens,
        chunkCount: metadata.chunkCount,
        averageRelevance: metadata.averageRelevance,
        namespaceDistribution: metadata.namespaceDistribution
      });

      // Get current token usage after update
      const currentUsage = this.tokenManager.getCurrentUsage();

      return {
        success: true,
        updatedTokenCount: currentUsage.totalTokens,
        metadata: {
          ...metadata,
          currentUsage
        }
      };

    } catch (error) {
      console.error("UpdateContentProcessingTool: Error updating content processing:", error);
      return {
        success: false,
        updatedTokenCount: 0,
        error: `Failed to update content processing: ${(error as Error).message}`
      };
    }
  }

  private validateMetadata(metadata: ContentProcessingMetadata): void {
    // Check if required fields are present
    if (typeof metadata.totalTokens !== 'number' || metadata.totalTokens < 0) {
      throw new Error("Invalid totalTokens value");
    }

    if (typeof metadata.chunkCount !== 'number' || metadata.chunkCount < 0) {
      throw new Error("Invalid chunkCount value");
    }

    if (typeof metadata.averageRelevance !== 'number' || 
        metadata.averageRelevance < 0 || 
        metadata.averageRelevance > 1) {
      throw new Error("Invalid averageRelevance value (should be between 0 and 1)");
    }

    if (!metadata.namespaceDistribution || 
        typeof metadata.namespaceDistribution !== 'object') {
      throw new Error("Invalid namespaceDistribution");
    }

    // Validate namespace distribution values
    for (const [namespace, count] of Object.entries(metadata.namespaceDistribution)) {
      if (typeof count !== 'number' || count < 0) {
        throw new Error(`Invalid count for namespace ${namespace}`);
      }
    }
  }
}