import React from 'react';
import { ArrowDown, ArrowUp } from 'lucide-react';
import { SortConfig } from './FileManagerDirectory';

interface SortButtonProps {
  column: SortConfig['key'];
  sortConfig: SortConfig | null;
  onSort: (key: SortConfig['key']) => void;
}

export const SortButton: React.FC<SortButtonProps> = ({ column, sortConfig, onSort }) => (
  <button
    onClick={() => onSort(column)}
    className="ml-2 w-6 h-6 flex items-center justify-center focus:outline-none"
    aria-label={`Sort by ${column}`}
  >
    {sortConfig?.key === column ? (
      sortConfig.direction === 'asc' ? (
        <ArrowUp className="h-4 w-4 text-blue-500" />
      ) : (
        <ArrowDown className="h-4 w-4 text-blue-500" />
      )
    ) : (
      <div className="flex flex-col items-center justify-center h-4">
        <ArrowUp className="h-2 w-2 text-blue-500" />
        <ArrowDown className="h-2 w-2 text-blue-500" />
      </div>
    )}
  </button>
);