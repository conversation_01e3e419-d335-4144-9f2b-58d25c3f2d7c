import { NextRequest, NextResponse } from 'next/server';
import { updateAgentVoice, getAgentConfiguration } from '../../../components/scriptreaderAI/elevenlabs';

/**
 * Test endpoint for voice update functionality
 * POST /api/test-voice-update
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { agentId, voiceId, apiKey } = body;

    // Validate required parameters
    if (!agentId) {
      return NextResponse.json(
        { error: 'agentId is required' },
        { status: 400 }
      );
    }

    if (!voiceId) {
      return NextResponse.json(
        { error: 'voiceId is required' },
        { status: 400 }
      );
    }

    console.log(`[TEST_VOICE_UPDATE] Testing voice update for agent ${agentId} with voice ${voiceId}`);

    // Step 1: Get current agent configuration
    console.log(`[TEST_VOICE_UPDATE] Step 1: Fetching current agent configuration...`);
    let currentConfig;
    try {
      currentConfig = await getAgentConfiguration(agentId, apiKey);
      console.log(`[TEST_VOICE_UPDATE] Current agent config retrieved successfully`);
    } catch (error) {
      console.error(`[TEST_VOICE_UPDATE] Failed to fetch agent config:`, error);
      return NextResponse.json(
        {
          error: 'Failed to fetch agent configuration',
          details: error instanceof Error ? error.message : String(error)
        },
        { status: 500 }
      );
    }

    // Step 2: Attempt voice update
    console.log(`[TEST_VOICE_UPDATE] Step 2: Attempting voice update...`);
    let updateResult;
    try {
      updateResult = await updateAgentVoice(agentId, voiceId, apiKey);
      console.log(`[TEST_VOICE_UPDATE] Voice update successful:`, updateResult);
    } catch (error) {
      console.error(`[TEST_VOICE_UPDATE] Voice update failed:`, error);
      return NextResponse.json(
        {
          error: 'Voice update failed',
          details: error instanceof Error ? error.message : String(error),
          currentConfig: currentConfig
        },
        { status: 500 }
      );
    }

    // Step 3: Verify the update
    console.log(`[TEST_VOICE_UPDATE] Step 3: Verifying voice update...`);
    let finalConfig;
    try {
      // Wait a moment for propagation
      await new Promise(resolve => setTimeout(resolve, 2000));

      finalConfig = await getAgentConfiguration(agentId, apiKey);
      const finalConversationConfig = finalConfig.conversation_config || finalConfig;
      const finalTtsVoiceId = finalConversationConfig.tts?.voice_id;
      const finalAgentVoiceId = finalConversationConfig.agent?.voice_id;

      // Primary success criteria: TTS voice_id should match (this is what ElevenLabs uses)
      const isUpdateSuccessful = finalTtsVoiceId === voiceId;

      console.log(`[TEST_VOICE_UPDATE] Verification complete:`, {
        expectedVoiceId: voiceId,
        finalTtsVoiceId: finalTtsVoiceId,
        finalAgentVoiceId: finalAgentVoiceId,
        ttsMatch: finalTtsVoiceId === voiceId,
        agentMatch: finalAgentVoiceId === voiceId,
        isSuccessful: isUpdateSuccessful
      });

      return NextResponse.json({
        success: true,
        message: 'Voice update test completed',
        results: {
          agentId,
          requestedVoiceId: voiceId,
          finalTtsVoiceId: finalTtsVoiceId,
          finalAgentVoiceId: finalAgentVoiceId,
          ttsVoiceMatches: finalTtsVoiceId === voiceId,
          agentVoiceMatches: finalAgentVoiceId === voiceId,
          updateSuccessful: isUpdateSuccessful,
          updateResult,
          currentConfig: {
            tts: currentConfig.conversation_config?.tts || currentConfig.tts,
            agent: currentConfig.conversation_config?.agent || currentConfig.agent
          },
          finalConfig: {
            tts: finalConfig.conversation_config?.tts || finalConfig.tts,
            agent: finalConfig.conversation_config?.agent || finalConfig.agent
          }
        }
      });

    } catch (error) {
      console.error(`[TEST_VOICE_UPDATE] Verification failed:`, error);
      return NextResponse.json(
        {
          error: 'Voice update verification failed',
          details: error instanceof Error ? error.message : String(error),
          updateResult,
          currentConfig: currentConfig
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('[TEST_VOICE_UPDATE] Unexpected error:', error);
    return NextResponse.json(
      {
        error: 'Unexpected error occurred',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to provide usage instructions
 */
export async function GET() {
  return NextResponse.json({
    message: "Voice Update Test Endpoint",
    usage: {
      method: "POST",
      endpoint: "/api/test-voice-update",
      body: {
        agentId: "your_agent_id_here",
        voiceId: "your_voice_id_here",
        apiKey: "optional_api_key_override"
      },
      description: "Tests the ElevenLabs agent voice update functionality and verifies the result"
    },
    availableVoiceIds: [
      "rCuVrCHOUMY3OwyJBJym", // Mia
      "QQutlXbwqnU9C4Zprxnn", // Morgan
      "P7x743VjyZEOihNNygQ9", // Dakota
      "kmSVBPu7loj4ayNinwWM", // Archie
      "AeRdCCKzvd23BpJoofzx", // Nathaniel
      "vVnXvLYPFjIyE2YrjUBE"  // Brad
    ]
  });
}
