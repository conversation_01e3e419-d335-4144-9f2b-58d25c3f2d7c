// lib/determineTypeUsingLLM.ts

import { ChatOpenAI } from "@langchain/openai";

/**
 * Determines the type of the document using an LLM.
 * @param model - An instance of the ChatOpenAI model.
 * @param validatedData - The structured data generated by the AI model.
 * @returns The determined type as a string (e.g., "recipe", "anthropometry").
 */
export async function determineTypeUsingLLM(model: ChatOpenAI, validatedData: any): Promise<string> {
  const prompt = `Based on the following data, determine the type of the document. Is it a "recipe" or "anthropometry"? Provide only the type as a lowercase word.

Data:
${JSON.stringify(validatedData, null, 2)}`;

try {
  // Invoke the LLM with the prompt
  const response = await model.invoke(prompt);

  // Extract and clean the response
  const classification = response.content

  if (classification === "recipe" || classification === "anthropometry") {
    return classification;
  } else {
    throw new Error(`Unexpected LLM response: ${response}`);
  }
} catch (error) {
  console.error("Error in determineTypeUsingLLM:", error);
  throw error;
}
}
