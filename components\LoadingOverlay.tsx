import React from 'react'
import { Loader2 } from 'lucide-react'

interface LoadingOverlayProps {
  message?: string
}

export default function LoadingOverlay({ message = 'Loading...' }: LoadingOverlayProps) {
  return (
    <div className="fixed inset-0 flex items-center justify-center ">
      <div className="bg-ike-message-bg rounded-lg p-6 flex flex-col items-center">
        <Loader2 className="h-12 w-12 text-white animate-spin" />
        <p className="mt-4 text-lg font-semibold text-amber-500 ">{message}</p>
      </div>
    </div>
  )
}