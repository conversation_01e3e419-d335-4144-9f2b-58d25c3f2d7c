import { ChatGroq } from "@langchain/groq";
import { z } from "zod";
import { createGroqClient } from "./llms/groq";
import { getServerSession } from "next-auth";
import { authOptions } from "../app/api/auth/[...nextauth]/authOptions";


const session = await getServerSession(authOptions);

if (!session?.user?.email) {
  throw new Error("Unauthorized - No valid session");
}

// Initialize Groq client with user's email for API key selection
const groqClient = createGroqClient({ userEmail: session.user.email });

// Define the model instance
const model = new ChatGroq({
  temperature: 0,
  model: process.env.GROQ_MODEL,  // Your LLM model environment variable
  apiKey: groqClient.apiKey!, // Use the dynamically selected API key  // API key environment variable
});

// Function to generate follow-up questions
export const generateFollowUpQuestions = async (aiResponse: string) => {
  // Define the schema for the follow-up questions

  const followUpQuestionsSchema = z.object({
    questions: z.array(z.string()).describe("List of follow-up questions"),
  });

  // Generate structured output using the schema
  const structuredLlm = model.withStructuredOutput(followUpQuestionsSchema, { name: "follow_up_questions" });

  // Invoke the model with the AI response to generate follow-up questions
  const response = await structuredLlm.invoke(`Generate 3 follow-up questions based on the following response: ${aiResponse}`);

  // Return the parsed follow-up questions
  return response;
};
