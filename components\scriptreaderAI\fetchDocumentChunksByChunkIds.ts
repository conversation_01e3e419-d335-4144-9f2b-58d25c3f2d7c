import { getFirestore, doc, getDoc } from "firebase/firestore";

export async function fetchDocumentChunksByChunkIds(chunkIds: string[], userId: string) {
  try {
    const db = getFirestore();
    const chunksPromises = chunkIds.map(async (chunkId) => {
      // Use the correct Firestore path: users/${userId}/byteStoreCollection/${chunkId}
      const chunkRef = doc(db, `users/${userId}/byteStoreCollection`, chunkId);
      const chunkSnap = await getDoc(chunkRef);
      if (chunkSnap.exists()) {
        return {
          id: chunkSnap.id,
          pageContent: chunkSnap.data()?.content || "", // Map Firestore 'content' field to pageContent
        };
      } else {
        console.warn(`Chunk ${chunkId} not found`);
        return null;
      }
    });

    const chunks = (await Promise.all(chunksPromises)).filter((chunk) => chunk !== null);
    if (chunks.length === 0) {
      throw new Error("No document chunks retrieved");
    }
    return chunks;
  } catch (error) {
    console.error("Error in fetchDocumentChunksByChunkIds:", error);
    throw new Error(`Failed to fetch document chunks: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}