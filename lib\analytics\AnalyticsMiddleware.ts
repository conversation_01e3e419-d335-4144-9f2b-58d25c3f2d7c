import { addDoc, collection, Timestamp } from 'firebase/firestore';
import { db } from 'components/firebase';

export class AnalyticsMiddleware {
  track(arg0: string, arg1: { userId: string; category: string | null; }) {
    throw new Error("Method not implemented.");
  }
  async interceptQueryStart(
    userId: string,
    namespaces: string[] | null,
    query: string,
    chatHistory: string,
    category: string | null
  ): Promise<string> {
    try {
      const queryMetricsRef = collection(db, 'query_metrics');
      const docRef = await addDoc(queryMetricsRef, {
        userId,
        namespaces,
        query,
        category,
        timestamp: Timestamp.now(),
        status: 'started'
      });
      return docRef.id;
    } catch (error) {
      console.error('Error logging query start:', error);
      throw error;
    }
  }

  async interceptTokenAnalysis(tokenData: {
    messageCount: number;
    tokenCountPerMessage: Record<number, number>;
    totalTokens: number;
    averageTokensPerMessage: number;
  }) {
    try {
      const tokenMetricsRef = collection(db, 'token_metrics');
      await addDoc(tokenMetricsRef, {
        ...tokenData,
        timestamp: Timestamp.now()
      });
    } catch (error) {
      console.error('Error logging token analysis:', error);
    }
  }

  async interceptContentProcessing(
    totalTokens: number,
    chunkCount: number,
    averageRelevance: number,
    namespaceDistribution: Record<string, number>,
    systemPromptTokens: number,
    chatHistoryTokens: number
  ) {
    try {
      const processMetricsRef = collection(db, 'process_metrics');
      await addDoc(processMetricsRef, {
        totalTokens,
        chunkCount,
        averageRelevance,
        namespaceDistribution,
        systemPromptTokens,
        chatHistoryTokens,
        timestamp: Timestamp.now()
      });
    } catch (error) {
      console.error('Error logging content processing:', error);
    }
  }

  async interceptQueryEnd(matchCount: number, namespaceCount: number) {
    try {
      const queryMetricsRef = collection(db, 'query_metrics');
      await addDoc(queryMetricsRef, {
        matchCount,
        namespaceCount,
        timestamp: Timestamp.now(),
        status: 'completed'
      });
    } catch (error) {
      console.error('Error logging query end:', error);
    }
  }

  // New method to capture chat history metrics
  async interceptChatHistoryMetrics(historyMetrics: {
    totalMessages: number;
    willBeTruncated: boolean;
    averageMessageLength: number;
    originalCount: number;
    convertedCount: number;
    truncatedMessages: number;
    messagesOverTokenLimit: number;
  }) {
    try {
      const chatMetricsRef = collection(db, 'chat_metrics');
      await addDoc(chatMetricsRef, {
        ...historyMetrics,
        timestamp: Timestamp.now()
      });
    } catch (error) {
      console.error('Error logging chat history metrics:', error);
    }
  }
}