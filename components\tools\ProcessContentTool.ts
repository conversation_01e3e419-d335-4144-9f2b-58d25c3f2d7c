import { Tool } from "@langchain/core/tools";
import { TokenManagement } from "@/src/tokenTracker/tokenManagement";
import { ContentSelector } from "@/src/processContent/selectContent";

interface ProcessContentInput {
  queryVector: number[];
  namespaces: string[];
  userId: string;
}

interface ContentMetadata {
  sources: Array<{
    doc_id: any;
    relevance: any;
    title: string;
    page: number;
  }>;
  totalTokens: number;
  chunkCount: number;
  averageRelevance: number;
  namespaceDistribution: Record<string, number>;
}

interface ProcessContentOutput {
  content: string;
  metadata: ContentMetadata;
  success: boolean;
  error?: string;
}

/**
 * ProcessContentTool
 * Handles content selection and processing across namespaces using vector similarity
 */
export class ProcessContentTool extends Tool {
  name = "processContent";
  description = "Select and process content across multiple namespaces based on vector similarity";
  
  constructor(
    private userId: string,
    private tokenManager: TokenManagement
  ) {
    super();
  }

  async _call(input: ProcessContentInput): Promise<ProcessContentOutput> {
    try {
      // Validate input
      this.validateInput(input);

      // Initialize content selector
      const contentSelector = new ContentSelector(input.userId || this.userId);

      // Select content based on vector similarity
      const processedContent = await contentSelector.selectContent(
        input.queryVector,
        input.namespaces,
        this.tokenManager
      );

      // Handle case where no content is found
      if (!processedContent) {
        return {
          content: "",
          metadata: {
            sources: [],
            totalTokens: 0,
            chunkCount: 0,
            averageRelevance: 0,
            namespaceDistribution: {}
          },
          success: false,
          error: "No relevant content found"
        };
      }

      // Update token tracking with processed content metadata
      await this.tokenManager.updateContentProcessing({
        totalTokens: processedContent.metadata.totalTokens,
        chunkCount: processedContent.metadata.chunkCount,
        averageRelevance: processedContent.metadata.averageRelevance,
        namespaceDistribution: processedContent.metadata.namespaceDistribution
      });

      return {
        content: processedContent.content,
        metadata: {
          sources: processedContent.metadata.sources,
          totalTokens: processedContent.metadata.totalTokens,
          chunkCount: processedContent.metadata.chunkCount,
          averageRelevance: processedContent.metadata.averageRelevance,
          namespaceDistribution: processedContent.metadata.namespaceDistribution
        },
        success: true
      };

    } catch (error: any) {
      console.error("ProcessContentTool: Error processing content:", error);
      throw new Error(`Failed to process content: ${error?.message || "Unknown error"}`);
    }
  }

  private validateInput(input: ProcessContentInput): void {
    if (!input.queryVector || !Array.isArray(input.queryVector) || input.queryVector.length === 0) {
      throw new Error("Invalid query vector provided");
    }

    if (!input.namespaces || !Array.isArray(input.namespaces)) {
      throw new Error("Invalid namespaces provided");
    }

    if (!input.userId && !this.userId) {
      throw new Error("User ID is required either in input or constructor");
    }
  }
}