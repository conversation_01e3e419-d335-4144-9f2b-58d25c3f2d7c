/**
 * errorHandling.ts
 * 
 * This module handles error cases and fallback responses in the AI query system.
 * It provides centralized error handling and graceful degradation through
 * user-friendly fallback messages when errors occur or when the system
 * cannot process a query successfully.
 * 
 * Key features:
 * - Centralized error logging
 * - Graceful error handling with user-friendly messages
 * - Stream-aware error recovery
 * - Consistent fallback behavior
 */

import { streamToClient } from "../streamingController/streaming";

/**
 * Default fallback message sent to users when an error occurs or no relevant content is found.
 * The END_METADATA marker is used to indicate the end of any metadata section in the stream.
 */
const FALLBACK_MESSAGE: string = "I apologize, but I couldn't find relevant information to answer your question. Please try rephrasing or asking a different question.\nEND_METADATA\n";

/**
 * Handles errors that occur during query processing by logging the error
 * and streaming a fallback message to the client.
 * 
 * @param controller - Stream controller used to send data to the client
 * @param error - The error that occurred during processing
 * @returns Promise that resolves when the fallback message has been sent
 */
export async function handleError(
    controller: ReadableStreamDefaultController<any>, 
    error: Error
): Promise<void> {
    // Log the error for debugging and monitoring
    console.error("Error:", error);
    
    // Send fallback message to maintain graceful user experience
    await streamFallbackMessage(controller);
}

/**
 * Streams a predefined fallback message to the client and closes the stream.
 * This function is used both for error cases and when no relevant content
 * is found for a query.
 * 
 * @param controller - Stream controller used to send data to the client
 * @returns Promise that resolves when the message has been sent and stream is closed
 */
export async function streamFallbackMessage(
    controller: ReadableStreamDefaultController<any>
): Promise<void> {
    // Stream the fallback message using the standard streaming mechanism
    await streamToClient({ controller }, FALLBACK_MESSAGE);
    
    // Ensure the stream is properly closed after sending the message
    controller.close();
}

/**
 * Note on StreamController interface (from types.ts):
 * The StreamController interface defines the contract for controller objects
 * used in streaming responses. While the current implementation uses
 * ReadableStreamDefaultController, the interface allows for future flexibility
 * in controller implementations.
 * 
 * interface StreamController {
 *   close(): void;
 *   // Additional methods as needed
 * }
 */