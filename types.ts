import { ReactElement, ReactNode } from 'react';
import { NextPage } from 'next';

export interface ChatData {
    category: string;
    fileName: string;
    id: string;
    text: string;
    userId: string;
    role: 'user' | 'ai'; // Explicitly define possible roles
    createdAt: {
      seconds: number;
      nanoseconds: number;
    };
    fileDocumentId?: string | null;
    stopped?: boolean // Add this line
    visualization: string | ''
  }
  



export type NextPageWithLayout<P = {}, IP = P> = NextPage<P, IP> & {
  getLayout?: (page: ReactElement) => ReactNode;
};
