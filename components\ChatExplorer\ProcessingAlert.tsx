import React from 'react';
import { AlertCircle, Loader2 } from 'lucide-react';
import { ProcessingProgress } from './getHistoricalChats';

interface ProcessingAlertProps {
  progress: ProcessingProgress;
  contextMessage?: string;
}



const ProcessingAlert = ({ progress }: ProcessingAlertProps) => {
  const percentComplete = progress.totalChats > 0 
    ? Math.round((progress.processedChats / progress.totalChats) * 100) 
    : 0;
  
  return (
    <div className="fixed top-4 right-4 z-50">
      <div className="w-96 rounded-lg border border-yellow-500/20 bg-white p-4 shadow-lg dark:bg-gray-900">
        <div className="flex items-start gap-3">
          <AlertCircle className="h-5 w-5 text-yellow-500" />
          <div className="flex-1">
            {/* Header */}
            <div className="flex items-center gap-2">
              <h5 className="font-medium text-yellow-500">
                Processing Historical Chats
              </h5>
              <Loader2 className="h-4 w-4 animate-spin text-yellow-500" />
            </div>
            
            {/* Content */}
            <div className="mt-2 space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <p>Processing chat history for vector database.</p>
              
              {/* Current chat info */}
              <div className="rounded border border-gray-200 bg-gray-50 p-2 dark:border-gray-700 dark:bg-gray-800">
                <p className="truncate text-xs">
                  Processing: {progress.currentChatId || 'Initializing...'}
                </p>
                <p className="mt-1 text-xs">
                  Messages in current chat: {progress.messagesFound}
                </p>
                
              </div>
              
              {/* Progress statistics */}
              <div className="flex justify-between text-xs">
                <span>Chats: {progress.processedChats} of {progress.totalChats}</span>
                <span>Pairs found: {progress.chatPairsFound}</span>
              </div>
              
              {/* Progress bar */}
              <div className="h-1.5 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                <div 
                  className="h-full rounded-full bg-yellow-500 transition-all duration-300 ease-in-out" 
                  style={{ width: `${percentComplete}%` }}
                />
              </div>
              
              {/* Percentage */}
              <div className="text-right text-xs font-medium">
                {percentComplete}% complete
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProcessingAlert;