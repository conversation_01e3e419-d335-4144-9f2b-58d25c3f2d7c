// lib/validation/validateData.ts

import { performSemanticCheck } from "./semanticCheck";
import { checkForDuplicates } from "./duplicationCheck";

/**
 * Validates client data by performing semantic and duplication checks.
 * @param content - The client data content.
 * @param clientName - The name of the client.
 * @returns A boolean indicating if the client data is valid and unique.
 */
export async function validateClientData(content: string, clientName: string): Promise<boolean> {
  console.log("Performing semantic check for client data...");
  const isSemanticallyValid = await performSemanticCheck(content, "Client Data");

  if (!isSemanticallyValid) {
    console.warn("Client data failed semantic validation.");
    return false;
  }

  console.log("Checking for duplicate client data...");
  const hasDuplicate = await checkForDuplicates("Clients", "clientName", clientName);

  if (hasDuplicate) {
    console.warn("Duplicate client data found in Firestore.");
    return false; // Duplicate found, skip the update
  }

  return true;
}

/**
 * Validates recipe data by performing semantic and duplication checks.
 * @param content - The recipe data content.
 * @param recipeName - The name of the recipe.
 * @returns A boolean indicating if the recipe data is valid and unique.
 */
export async function validateRecipeData(content: string, recipeName: string): Promise<boolean> {
  console.log("Performing semantic check for recipe data...");
  const isSemanticallyValid = await performSemanticCheck(content, "Recipe Data");

  if (!isSemanticallyValid) {
    console.warn("Recipe data failed semantic validation.");
    return false;
  }

  console.log("Checking for duplicate recipe data...");
  const hasDuplicate = await checkForDuplicates("Recipes", "recipeName", recipeName);

  if (hasDuplicate) {
    console.warn("Duplicate recipe data found in Firestore.");
    return false; // Duplicate found, skip the update
  }

  return true;
}
