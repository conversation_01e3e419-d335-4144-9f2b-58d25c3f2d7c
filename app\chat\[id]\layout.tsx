"use client";

import React, { useState } from 'react';
import { SessionProvider } from 'next-auth/react';
import { SelectedDocProvider } from 'components/SelectedDocContext';
import SideBar from 'components/SideBar';
import { Menu } from 'lucide-react';

interface ChatLayoutProps {
  children: React.ReactNode;
}

const ChatLayout: React.FC<ChatLayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <SessionProvider>
      <SelectedDocProvider>
        <div className="flex h-screen w-screen overflow-hidden">
          <style jsx global>{`
            .custom-scrollbar::-webkit-scrollbar {
              width: 8px;
            }
            .custom-scrollbar::-webkit-scrollbar-track {
              background: transparent;
            }
            .custom-scrollbar::-webkit-scrollbar-thumb {
              background-color: rgba(255, 255, 255, 0.2);
              border-radius: 20px;
              border: transparent;
            }
            .custom-scrollbar::-webkit-scrollbar-thumb:hover {
              background-color: rgba(255, 255, 255, 0.3);
            }
          `}</style>

          {/* Desktop Sidebar */}
          <div className="hidden md:block w-64 flex-shrink-0 bg-ike-dark-purple overflow-y-auto custom-scrollbar">
            <SideBar />
          </div>

          {/* Mobile Sidebar */}
          <div
            className={`md:hidden fixed inset-y-0 left-0 z-50 w-64 bg-ike-dark-purple overflow-y-auto custom-scrollbar transform
              ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
              transition-transform duration-300 ease-in-out`}
          >
            <SideBar onClose={() => setSidebarOpen(false)} />
          </div>

          {/* Main Content */}
          <main className="flex-1 overflow-y-auto bg-ike-message-bg [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-track]:bg-gray-900 [&::-webkit-scrollbar-thumb]:bg-gray-800 [&::-webkit-scrollbar-thumb]:rounded-full">
            {/* Mobile menu button */}
            <div className="md:hidden p-4 fixed top-0 right-0 z-40">
              <button
                onClick={toggleSidebar}
                className="p-2 rounded-md text-gray-500 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-900"
                aria-label="Toggle sidebar"
              >
                <Menu className="h-6 w-6" />
              </button>
            </div>
            {children}
          </main>
        </div>
      </SelectedDocProvider>
    </SessionProvider>
  );
};

export default ChatLayout;