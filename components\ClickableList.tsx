import React from 'react';

interface ListItem {
  text: string;
  level: number;
  id: string;
  type: 'bullet' | 'number' | 'toc';
}

interface ClickableListProps {
  content: string;
  onItemClick: (text: string) => void;
}

const ClickableList: React.FC<ClickableListProps> = ({ content, onItemClick }) => {
  // Helper function to extract and structure list items
  const extractListItems = (content: string): ListItem[] => {
    const lines: string[] = content.split('\n');
    const items: ListItem[] = [];
    
    const listItemRegex = /^(\s*)([-*+]|\d+\.)\s+(.+)$/;

    for (const line of lines) {
      const match = line.match(listItemRegex);
      
      if (match) {
        const [, spaces, marker, text] = match;
        const level: number = Math.floor((spaces?.length ?? 0) / 2);
        const type = marker.match(/\d+\./) ? 'number' : 'bullet';
        
        if (text.trim()) {
          items.push({
            text: text.trim(),
            level,
            id: text.trim().toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-'),
            type
          });
        }
      }
    }

    return items;
  };

  // Function to render list items as styled buttons
  const renderListButtons = (items: ListItem[]): React.ReactNode => {
    return items.map((item, index) => {
      // Calculate indentation based on level
      const indentClass = `ml-${item.level * 4}`;
      
      return (
        <div key={index} className={`my-2 ${indentClass}`}>
          <button
            onClick={() => onItemClick(item.text)}
            className={`
              group
              w-full text-left
              text-xs bg-opacity-50 hover:bg-opacity-75
              ${item.type === 'number' ? 'bg-ike-dark-purple' : 'bg-ike-purple'}
              text-amber-500 hover:text-amber-300
              px-3 py-2 rounded-lg
              transition-all duration-200
              flex items-center
              ${item.level > 0 ? 'border-l-2 border-amber-500/30' : ''}
              hover:translate-x-1
            `}
          >
            {/* Marker based on list type and level */}
            <span className="mr-2 opacity-70 group-hover:opacity-100 transition-opacity">
              {item.type === 'number' ? `${index + 1}.` : item.level === 0 ? '•' : item.level === 1 ? '○' : '▹'}
            </span>
            {item.text}
          </button>
        </div>
      );
    });
  };

  const listItems: ListItem[] = extractListItems(content);

  return listItems.length > 0 ? (
    <nav className="my-4 space-y-1">
      <div className="flex flex-col">
        {renderListButtons(listItems)}
      </div>
    </nav>
  ) : null;
};

export default ClickableList;