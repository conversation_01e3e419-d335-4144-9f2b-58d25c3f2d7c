// analyticsService.ts
import { db } from 'components/firebase';
import { 
  collection, addDoc, serverTimestamp,
  DocumentReference, WriteBatch, writeBatch,
  doc
} from 'firebase/firestore';

interface TokenMetrics {
  messageCount: number;
  tokenCountPerMessage: number[];
  totalTokens: number;
  averageTokensPerMessage: number;
  messageTypes: { role: string; length: number }[];
  queryId: string;
}

interface QueryMetrics {
  queryId: string;
  userId: string;
  namespacesCount: number;
  queryLength: number;
  historyLength: number;
  processingTimeMs: number;
  totalMatches: number;
  uniqueNamespaces: number;
  category?: string;
}

interface ChunkProcessingMetrics {
  queryId: string;
  chunkId: string;
  namespace: string;
  documentId: string;
  documentTitle: string;
  contentLength: number;
  hasEmbedding: boolean;
  embeddingLength?: number;
  relevanceScore: number;
  processingTimeMs: number;
}

interface ContentProcessingMetrics {
  queryId: string;
  chunksSelected: number;
  totalTokens: number;
  averageRelevance: number;
  namespaceDistribution: Record<string, number>;
  systemPromptTokens: number;
  chatHistoryTokens: number;
}

export class AnalyticsService {
  private batch: WriteBatch;
  private batchCount: number = 0;
  private readonly MAX_BATCH_SIZE = 500;

  constructor() {
    this.batch = writeBatch(db);
  }

  private async commitBatchIfNeeded(): Promise<void> {
    if (this.batchCount >= this.MAX_BATCH_SIZE) {
      await this.batch.commit();
      this.batch = writeBatch(db);
      this.batchCount = 0;
    }
  }

  private generateQueryId(): string {
    return `query_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  async captureQueryStart(
    userId: string,
    namespacesCount: number,
    queryLength: number,
    historyLength: number,
    category?: string
  ): Promise<string> {
    const queryId = this.generateQueryId();
    
    const metrics: QueryMetrics = {
      queryId,
      userId,
      namespacesCount,
      queryLength,
      historyLength,
      processingTimeMs: 0, // Will be updated at query end
      totalMatches: 0,
      uniqueNamespaces: 0,
      category
    };

    await addDoc(collection(db, 'query_metrics'), {
      ...metrics,
      timestamp: serverTimestamp(),
      status: 'started'
    });

    return queryId;
  }

  async updateQueryMetrics(
    queryId: string,
    updates: Partial<QueryMetrics>
  ): Promise<void> {
    const queryRef = collection(db, 'query_metrics');
    await addDoc(queryRef, {
      queryId,
      ...updates,
      timestamp: serverTimestamp(),
      status: 'updated'
    });
  }

  async captureTokenMetrics(
    queryId: string,
    metrics: Omit<TokenMetrics, 'queryId'>
  ): Promise<void> {
    await addDoc(collection(db, 'token_metrics'), {
      ...metrics,
      queryId,
      timestamp: serverTimestamp()
    });
  }

  async captureChunkProcessing(
    queryId: string,
    chunk: ChunkProcessingMetrics
  ): Promise<void> {
    this.batch.set(
      doc(collection(db, 'chunk_metrics')), 
      {
        ...chunk,
        queryId,
        timestamp: serverTimestamp()
      }
    );
    
    this.batchCount++;
    await this.commitBatchIfNeeded();
  }

  async captureContentProcessing(
    queryId: string,
    metrics: ContentProcessingMetrics
  ): Promise<void> {
    await addDoc(collection(db, 'content_metrics'), {
      ...metrics,
      queryId,
      timestamp: serverTimestamp()
    });
  }
}

