'use client'

import { useEffect, useState, useCallback } from 'react'
import { collection, query, orderBy, limit, onSnapshot } from 'firebase/firestore'
import { db } from 'components/firebase'
import { ArrowUpDown, ChevronDown, ChevronUp } from 'lucide-react'
import React from 'react'

interface LogEntry {
  id: string
  timestamp: Date
  metadata: {
    sources: Array<{
      title: string
      page: string | number
      relevance: number
      diversity: number
    }>
    totalTokens: number
    chunkCount: number
    averageRelevance: number
    averageDiversity: number
  }
  userRequest: string
  response: string
}

export default function LogPage() {
  const [logs, setLogs] = useState<LogEntry[]>([])
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set())
  const [sortField, setSortField] = useState<'timestamp' | 'chunkCount' | 'totalTokens'>('timestamp')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  useEffect(() => {
    const q = query(collection(db, 'users', '<EMAIL>', 'metadata_logs'), orderBy('timestamp', 'desc'), limit(50))
    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const logEntries: LogEntry[] = querySnapshot.docs.map(doc => ({
        id: doc.id,
        timestamp: doc.data().timestamp.toDate(),
        metadata: doc.data().metadata,
        userRequest: doc.data().userRequest,
        response: doc.data().response
      }))
      setLogs(logEntries)
    })

    return () => unsubscribe()
  }, [])

  const toggleRowExpansion = useCallback((id: string) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev)
      if (newSet.has(id)) {
        newSet.delete(id)
      } else {
        newSet.add(id)
      }
      return newSet
    })
  }, [])

  const handleSort = useCallback((field: 'timestamp' | 'chunkCount' | 'totalTokens') => {
    setSortField(currentField => {
      if (field === currentField) {
        setSortDirection(currentDirection => currentDirection === 'asc' ? 'desc' : 'asc')
      } else {
        setSortDirection('asc')
      }
      return field
    })
  }, [])

  const sortedLogs = React.useMemo(() => {
    return [...logs].sort((a, b) => {
      let aValue = sortField === 'timestamp' ? a[sortField] : a.metadata[sortField]
      let bValue = sortField === 'timestamp' ? b[sortField] : b.metadata[sortField]
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
      return 0
    })
  }, [logs, sortField, sortDirection])

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Request Log</h1>
      <div className="overflow-x-auto">
        <table className="min-w-full bg-white border text-ike-dark-purple border-gray-300 text-xs">
          <thead>
            <tr className="bg-gray-100">
              <th className="py-2 px-4 border-b"></th>
              <th className="py-2 px-4 border-b cursor-pointer" onClick={() => handleSort('timestamp')}>
                <div className="flex items-center">
                  Timestamp
                  <ArrowUpDown className="ml-1 h-4 w-4" />
                </div>
              </th>
              <th className="py-2 px-4 border-b cursor-pointer" onClick={() => handleSort('chunkCount')}>
                <div className="flex items-center">
                  Chunks Selected
                  <ArrowUpDown className="ml-1 h-4 w-4" />
                </div>
              </th>
              <th className="py-2 px-4 border-b cursor-pointer" onClick={() => handleSort('totalTokens')}>
                <div className="flex items-center">
                  Total Tokens
                  <ArrowUpDown className="ml-1 h-4 w-4" />
                </div>
              </th>
              <th className="py-2 px-4 border-b">Avg Relevance</th>
              <th className="py-2 px-4 border-b">Avg Diversity</th>
              <th className="py-2 px-4 border-b">User Request</th>
            </tr>
          </thead>
          <tbody>
            {sortedLogs.map((log) => (
              <React.Fragment key={log.id}>
                <tr className="hover:bg-gray-50 text-ike-dark-purple">
                  <td className="py-2 px-4 border-b">
                    <button onClick={() => toggleRowExpansion(log.id)}>
                      {expandedRows.has(log.id) ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </button>
                  </td>
                  <td className="py-2 px-4 border-b">{log.timestamp.toLocaleString()}</td>
                  <td className="py-2 px-4 border-b">{log.metadata.chunkCount}</td>
                  <td className="py-2 px-4 border-b">{log.metadata.totalTokens}</td>
                  <td className="py-2 px-4 border-b">{log.metadata.averageRelevance.toFixed(2)}</td>
                  <td className="py-2 px-4 border-b">{log.metadata.averageDiversity.toFixed(2)}</td>
                  <td className="py-2 px-4 border-b truncate max-w-xs">{log.userRequest}</td>
                </tr>
                {expandedRows.has(log.id) && (
                  <tr>
                    <td colSpan={7} className="py-2 px-4 border-b">
                      <div className="bg-gray-50 text-ike-dark-purple p-4 rounded">
                        <h3 className="font-semibold mb-2">Sources:</h3>
                        <ul className="list-disc list-inside mb-4">
                          {log.metadata.sources.map((source, index) => (
                            <li key={index}>
                              {source.title} (Page {source.page}) - Relevance: {source.relevance.toFixed(2)}, Diversity: {source.diversity.toFixed(2)}
                            </li>
                          ))}
                        </ul>
                        <h3 className="font-semibold mb-2">User Request:</h3>
                        <p className="whitespace-pre-wrap mb-4">{log.userRequest}</p>
                        <h3 className="font-semibold mb-2">Response:</h3>
                        <p className="whitespace-pre-wrap">{log.response}</p>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}