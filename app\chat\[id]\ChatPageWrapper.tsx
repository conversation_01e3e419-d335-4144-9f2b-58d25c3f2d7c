'use client'

import React, { useEffect, useState } from 'react';
import ChatArea from 'components/ChatArea';
import LoadingOverlay from 'components/LoadingOverlay';

interface ChatPageWrapperProps {
  session: any;
  chatData: any;
  errorMessages: string | null;
  chatId: string;
}

export default function ChatPageWrapper({ session, chatData, errorMessages, chatId }: ChatPageWrapperProps) {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate a short delay to show the loading overlay
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <LoadingOverlay message="Loading chat history..." />;
  }

  if (errorMessages) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
        <p className="text-lg text-gray-700">{errorMessages}</p>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      <ChatArea
        initialMessages={[]}
        session={session}
        loadingMessages={false}
        errorMessages={errorMessages}
        isPending={false}
        selectedDocId={chatId}
        category={chatData?.category || ''}
      />
    </div>
  );
}