import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import getOrCreateUserAgent, { getUserAgentId } from '../../../lib/userAgentManager';

/**
 * GET /api/user-agent
 * Returns the user's ElevenLabs agent ID
 */
export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const agentId = await getUserAgentId(session.user.email);

    if (!agentId) {
      return NextResponse.json(
        { error: 'No agent found for user', userEmail: session.user.email },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      agentId,
      userEmail: session.user.email
    });
  } catch (error) {
    console.error('[USER_AGENT_API] Error getting user agent:', error);
    return NextResponse.json(
      { error: 'Failed to get user agent' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/user-agent
 * Creates or ensures a user has an ElevenLabs agent
 */
export async function POST(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const result = await getOrCreateUserAgent(session.user.email);

    if (!result) {
      return NextResponse.json(
        { error: 'Failed to create or get user agent' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      agentId: result,
      userEmail: session.user.email,
      message: 'User agent ready'
    });
  } catch (error) {
    console.error('[USER_AGENT_API] Error creating user agent:', error);
    return NextResponse.json(
      { error: 'Failed to create user agent' },
      { status: 500 }
    );
  }
}
