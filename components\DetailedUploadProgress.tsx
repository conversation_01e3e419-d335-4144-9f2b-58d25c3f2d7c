import React, { useState, useEffect, useCallback } from 'react';
import { Upload, CheckCircle2, AlertCircle, FileUp, Database, Save, Brain } from 'lucide-react';

enum ProcessingStatus {
  UPLOADING = 'UPLOADING',
  PROCESSING = 'PROCESSING',
  STORING = 'STORING',
  EMBEDDING = 'EMBEDDING',
  COMPLETED = 'COMPLETED',
  ERROR = 'ERROR'
}

interface Phase {
  id: ProcessingStatus;
  label: string;
}

interface DetailedUploadProgressProps {
  progress: number | null;
  status: string | null;
  error: string | null;
}

interface ProcessedChunk {
  id: string;
  index: number;
  stored: boolean;
  embedded: boolean;
}

const DetailedUploadProgress: React.FC<DetailedUploadProgressProps> = ({ 
  progress, 
  status, 
  error 
}) => {
  const [processingPhase, setProcessingPhase] = useState<ProcessingStatus>(ProcessingStatus.UPLOADING);
  const [processedChunks, setProcessedChunks] = useState<ProcessedChunk[]>([]);
  const [currentPhaseProgress, setCurrentPhaseProgress] = useState<number>(0);
  const [lastSeenChunkNumber, setLastSeenChunkNumber] = useState<number>(0);

  const updateProgress = useCallback((chunks: ProcessedChunk[], total: number, phase: ProcessingStatus) => {
    const count = phase === ProcessingStatus.EMBEDDING 
      ? chunks.filter(c => c.embedded).length 
      : chunks.filter(c => c.stored).length;
    
    const progress = Math.min(
      Math.round((count / Math.max(total, 1)) * 100),
      99
    );
    setCurrentPhaseProgress(progress);
  }, []);

  useEffect(() => {
    if (status === 'Uploading file...') {
      setProcessingPhase(ProcessingStatus.UPLOADING);
      setCurrentPhaseProgress(progress || 0);
      setProcessedChunks([]);
      setLastSeenChunkNumber(0);
    } else if (status === 'Processing file...') {
      setProcessingPhase(ProcessingStatus.PROCESSING);
      setCurrentPhaseProgress(25);
      const progressInterval = setInterval(() => {
        setCurrentPhaseProgress(prev => {
          if (prev < 90) return prev + 1;
          return prev;
        });
      }, 100);
      return () => clearInterval(progressInterval);
    } else if (status?.includes('Successfully processed chunk:')) {
      const chunkId = status.match(/chunk: (.+)$/)?.[1];
      
      if (chunkId) {
        const seqMatch = chunkId.match(/_(\d+)$/);
        if (seqMatch) {
          const chunkNumber = parseInt(seqMatch[1], 10);
          setLastSeenChunkNumber(prev => Math.max(prev, chunkNumber));

          setProcessedChunks(prev => {
            let newChunks;
            if (!prev.some(chunk => chunk.id === chunkId)) {
              // First time seeing this chunk - add it to our list
              newChunks = [
                ...prev,
                {
                  id: chunkId,
                  index: chunkNumber,
                  stored: true,
                  embedded: false
                }
              ];
              setProcessingPhase(ProcessingStatus.STORING);
            } else {
              // We've seen this chunk before - it's now been embedded
              newChunks = prev.map(chunk => 
                chunk.id === chunkId 
                  ? { ...chunk, embedded: true }
                  : chunk
              );
              setProcessingPhase(ProcessingStatus.EMBEDDING);
            }
            updateProgress(newChunks, lastSeenChunkNumber, processingPhase);
            return newChunks;
          });
        }
      }
    } else if (status === 'Upload and processing complete.') {
      setProcessingPhase(ProcessingStatus.COMPLETED);
      setCurrentPhaseProgress(100);
    }
  }, [status, progress, processedChunks, lastSeenChunkNumber, updateProgress, processingPhase]);

  const getPhaseIcon = (phase: ProcessingStatus): JSX.Element => {
    switch (phase) {
      case ProcessingStatus.UPLOADING:
        return <Upload className="w-6 h-6" />;
      case ProcessingStatus.PROCESSING:
        return <FileUp className="w-6 h-6 animate-spin" />;
      case ProcessingStatus.STORING:
        return <Save className="w-6 h-6 animate-pulse" />;
      case ProcessingStatus.EMBEDDING:
        return <Database className="w-6 h-6 animate-pulse" />;
      case ProcessingStatus.COMPLETED:
        return <CheckCircle2 className="w-6 h-6" />;
      case ProcessingStatus.ERROR:
        return <AlertCircle className="w-6 h-6" />;
      default:
        return <Brain className="w-6 h-6" />;
    }
  };

  const phases: Phase[] = [
    { id: ProcessingStatus.UPLOADING, label: 'Uploading File' },
    { id: ProcessingStatus.PROCESSING, label: 'Processing Document' },
    { id: ProcessingStatus.STORING, label: 'Storing Chunks' },
    { id: ProcessingStatus.EMBEDDING, label: 'Creating Embeddings' },
    { id: ProcessingStatus.COMPLETED, label: 'Completed' }
  ];

  const getCurrentPhaseIndex = (): number => {
    return phases.findIndex(phase => phase.id === processingPhase);
  };

  const getProgressDisplay = (): string => {
    switch (processingPhase) {
      case ProcessingStatus.UPLOADING:
        return progress !== null ? `${progress}%` : '';
      case ProcessingStatus.PROCESSING:
        return 'Processing...';
      case ProcessingStatus.STORING:
        const storedCount = processedChunks.filter(c => c.stored).length;
        return `${storedCount} of ${lastSeenChunkNumber} chunks stored`;
      case ProcessingStatus.EMBEDDING:
        const embeddedCount = processedChunks.filter(c => c.embedded).length;
        return `${embeddedCount} of ${lastSeenChunkNumber} chunks embedded`;
      case ProcessingStatus.COMPLETED:
        return '100%';
      default:
        return '';
    }
  };

  if (error) {
    return (
      <div className="w-full max-w-md mx-auto p-4 bg-red-50 rounded-lg border border-red-200">
        <div className="flex items-center space-x-2 text-red-600">
          <AlertCircle className="w-5 h-5" />
          <span className="font-medium">Error</span>
        </div>
        <p className="mt-2 text-sm text-red-600">{error}</p>
      </div>
    );
  }

  return (
    <div className="w-full max-w-2xl mx-auto p-8">
      <div className="flex justify-between mb-8">
        {phases.map((phase, index) => {
          const isActive = getCurrentPhaseIndex() >= index;
          const isCurrent = processingPhase === phase.id;
          
          return (
            <div key={phase.id} className="flex flex-col items-center w-1/5">
              <div className={`
                flex items-center justify-center w-10 h-10 rounded-full
                ${isActive ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-400'}
                ${isCurrent ? 'ring-4 ring-indigo-100' : ''}
              `}>
                {getPhaseIcon(phase.id)}
              </div>
              <div className="mt-2 text-center">
                <span className={`text-xs font-medium ${isActive ? 'text-indigo-600' : 'text-gray-500'}`}>
                  {phase.label}
                </span>
              </div>
            </div>
          );
        })}
      </div>

      <div className="mb-4">
        <div className="flex justify-between mb-1">
          <span className="text-sm font-medium text-indigo-600">
            {phases.find(p => p.id === processingPhase)?.label}
          </span>
          <span className="text-sm font-medium text-ike-purple">
            {getProgressDisplay()}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-300 ${
              processingPhase === ProcessingStatus.PROCESSING 
                ? 'bg-ike-purple animate-pulse' 
                : 'bg-ike-purple'
            }`}
            style={{ width: `${currentPhaseProgress}%` }}
          />
        </div>
      </div>

      <div className="mt-4 space-y-2">
        {(processingPhase === ProcessingStatus.STORING || processingPhase === ProcessingStatus.EMBEDDING) && 
         processedChunks.length > 0 && (
          <div className="text-sm text-gray-600">
            <p className="font-medium mb-1">Processing Chunks:</p>
            <div className="max-h-32 overflow-y-auto space-y-1">
              {processedChunks
                .sort((a, b) => a.index - b.index)
                .map((chunk) => (
                <div key={chunk.id} className="flex items-center space-x-2">
                  <CheckCircle2 className="w-4 h-4 text-green-500" />
                  <span>
                    Chunk {chunk.index}: 
                    {chunk.stored && !chunk.embedded && ' stored'}
                    {chunk.embedded && ' embedded'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {processingPhase === ProcessingStatus.COMPLETED && (
          <div className="flex items-center justify-center space-x-2 text-green-600 bg-green-50 p-4 rounded-lg">
            <CheckCircle2 className="w-5 h-5" />
            <span className="font-medium">Your document is now ready!</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default DetailedUploadProgress;