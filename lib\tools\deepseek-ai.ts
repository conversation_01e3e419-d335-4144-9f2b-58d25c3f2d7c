/**
 * deepseek-ai.ts
 *
 * Integration with DeepSeek LLM API for processing queries.
 */

import axios from 'axios';

// Define interfaces for DeepSeek processing
export interface DeepSeekProcessingOptions {
  prompt: string;
  model?: string;
  modelOptions?: {
    temperature?: number;
    maxTokens?: number;
    [key: string]: any;
  };
}

/**
 * Process content with DeepSeek LLM
 *
 * @param options - Processing options including prompt and model settings
 * @returns The generated content
 */
export async function processWithDeepSeek(options: DeepSeekProcessingOptions): Promise<string> {
  try {
    const {
      prompt,
      model = "deepseek-r1-distill-llama-70b",
      modelOptions = {}
    } = options;

    console.log(`Processing with DeepSeek model: ${model}`);

    // Check if we're in a test environment or missing API key
    const deepseekApiKey = process.env.DEEPSEEK_API_KEY;
    if (!deepseekApiKey || process.env.NODE_ENV === 'test') {
      console.log("Using mock DeepSeek response for testing");

      // Extract the user query from the prompt
      const userQueryMatch = prompt.match(/USER QUERY:\s*(.*?)(\n|$)/);
      const userQuery = userQueryMatch ? userQueryMatch[1].trim() : "unknown query";

      // Generate a mock response
      return `This is a mock response from DeepSeek for the query: "${userQuery}".

In a production environment, this would be processed by the DeepSeek AI model.

The response would be based on the document content provided in the prompt.

For testing purposes, this mock response is being returned instead of making an actual API call.`;
    }

    // Make API request to DeepSeek
    const response = await axios.post(
      'https://api.deepseek.com/v1/chat/completions',
      {
        model,
        messages: [{ role: 'user', content: prompt }],
        temperature: modelOptions.temperature || 0.3,
        max_tokens: modelOptions.maxTokens || 2000,
        ...modelOptions
      },
      {
        headers: {
          'Authorization': `Bearer ${deepseekApiKey}`,
          'Content-Type': 'application/json'
        }
      }
    );

    // Extract and return the generated content
    return response.data.choices[0].message.content.trim();
  } catch (error: any) {
    console.error("Error processing content with DeepSeek:", error);

    // Provide detailed error information
    const errorDetails = {
      message: error.message || "Unknown error",
      status: error.response?.status,
      data: error.response?.data
    };

    console.error("Error details:", JSON.stringify(errorDetails, null, 2));

    // Return a fallback response instead of throwing an error
    return `I apologize, but I encountered an issue while processing your query.

The error was: ${error.message || "Unknown error"}

Please try again later or contact support if the issue persists.`;
  }
}

// Export available DeepSeek models
export function getDeepSeekModels(): string[] {
  return [
    "deepseek-r1-distill-llama-70b",
    "deepseek-coder-33b-instruct",
    "deepseek-llm-67b-chat"
  ];
}
