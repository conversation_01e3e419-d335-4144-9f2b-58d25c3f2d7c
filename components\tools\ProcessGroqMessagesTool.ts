import { Tool } from "@langchain/core/tools";
import { createGroqClient } from "lib/llms/groq";
import { ChatGroq } from "@langchain/groq";
import { StreamToClientTool } from "./StreamToClientTool";
import { ProcessMessageInput, StreamOptions } from "@/src/types/shared";
import { BasePromptValueInterface } from "@langchain/core/prompt_values";

interface GroqProcessingConfig {
  temperature?: number;
  maxTokens?: number;
  closeStreamDelay?: number;
}

interface ProcessGroqMessagesInput {
  userEmail: string;
  streamOptions: StreamOptions;
  prompt: BasePromptValueInterface;
  config?: GroqProcessingConfig;
}

interface ProcessGroqMessagesOutput {
  success: boolean;
  error?: string;
  response?: any;
}

/**
 * ProcessGroqMessagesTool
 * Handles Groq model interaction and message processing
 */
export class ProcessGroqMessagesTool extends Tool {
  processMessage(input: ProcessMessageInput) {
    throw new Error("Method not implemented.");
  }
  name = "processGroqMessages";
  description = "Process messages using <PERSON><PERSON><PERSON>'s chat model";

  private readonly DEFAULT_CONFIG: Required<GroqProcessingConfig> = {
    temperature: 0.3,
    maxTokens: 5000,
    closeStreamDelay: 500
  };

  constructor(private readonly streamTool: StreamToClientTool) {
    super();
  }

  async _call(input: ProcessGroqMessagesInput): Promise<ProcessGroqMessagesOutput> {
    try {
      // Validate input
      this.validateInput(input);

      const { 
        userEmail,
        streamOptions,
        prompt,
        config = {}
      } = input;

      // Merge with default config
      const finalConfig = {
        ...this.DEFAULT_CONFIG,
        ...config
      };

      // Initialize Groq client
      const groqClient = createGroqClient({ userEmail });
      if (!groqClient.apiKey) {
        throw new Error("Failed to initialize Groq client: No API key available");
      }

      // Create callback handler for streaming
      const callbackHandler = this.streamTool.createCallbackHandler(streamOptions);

      // Initialize Groq chat model with streaming
      const model = new ChatGroq({
        streaming: true,
        temperature: finalConfig.temperature,
        model: process.env.GROQ_MODEL!,
        apiKey: groqClient.apiKey,
        maxTokens: finalConfig.maxTokens,
        callbacks: [callbackHandler],
      });

      // Start processing
      await this.streamTool.startProcessing(streamOptions);

      // Generate response
      const response = await model.generatePrompt([prompt]);

      // Finish processing
      await this.streamTool.finishProcessing(streamOptions);

      return {
        success: true,
        response
      };

    } catch (error) {
      console.error("ProcessGroqMessagesTool: Error:", error);
      
      if (input.streamOptions?.controller) {
        const stream = await this.streamTool.streamError(
          input.streamOptions,
          error instanceof Error ? error : new Error(String(error))
        );
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private validateInput(input: ProcessGroqMessagesInput): void {
    if (!input.userEmail) {
      throw new Error("User email is required");
    }

    if (!input.streamOptions?.controller) {
      throw new Error("Stream controller is required");
    }

    if (!input.prompt) {
      throw new Error("Prompt is required");
    }

    // Validate config if provided
    if (input.config) {
      if (input.config.temperature !== undefined &&
          (typeof input.config.temperature !== 'number' ||
           input.config.temperature < 0 ||
           input.config.temperature > 1)) {
        throw new Error("Temperature must be between 0 and 1");
      }

      if (input.config.maxTokens !== undefined &&
          (typeof input.config.maxTokens !== 'number' ||
           input.config.maxTokens <= 0)) {
        throw new Error("Max tokens must be a positive number");
      }

      if (input.config.closeStreamDelay !== undefined &&
          (typeof input.config.closeStreamDelay !== 'number' ||
           input.config.closeStreamDelay < 0)) {
        throw new Error("Close stream delay must be a non-negative number");
      }
    }
  }
}