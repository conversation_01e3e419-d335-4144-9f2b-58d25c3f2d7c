import { db } from 'components/firebase';
import { doc, setDoc } from 'firebase/firestore';

async function addScenarioToDatabase(fileId: string, aiScenario: string): Promise<void> {
  try {
    const docRef = doc(db, 'scenarios', fileId);
    await setDoc(docRef, {
      scenario: aiScenario,
      timestamp: new Date()
    });
  } catch (error) {
    console.error("Error adding scenario to database:", error);
  }
}

export { addScenarioToDatabase };
