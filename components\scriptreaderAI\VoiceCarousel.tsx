import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Users, User } from 'lucide-react';
import { AVAILABLE_VOICES, VoiceData } from './voiceUtils';
import VoicePreview from './VoicePreview';

interface VoiceCarouselProps {
  selectedVoiceId: string | null;
  onVoiceSelect: (voiceId: string) => void;
  className?: string;
  isUpdatingVoice?: boolean;
}

const VoiceCarousel: React.FC<VoiceCarouselProps> = ({
  selectedVoiceId,
  onVoiceSelect,
  className = '',
  isUpdatingVoice = false
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [genderFilter, setGenderFilter] = useState<'all' | 'female' | 'male'>('all');

  // Filter voices based on gender selection
  const filteredVoices = genderFilter === 'all'
    ? AVAILABLE_VOICES
    : AVAILABLE_VOICES.filter(voice => voice.gender === genderFilter);

  // Calculate how many voices to show at once (responsive)
  const getVisibleCount = () => {
    if (typeof window !== 'undefined') {
      if (window.innerWidth < 640) return 1; // mobile
      if (window.innerWidth < 1024) return 2; // tablet
      return 3; // desktop
    }
    return 3;
  };

  const [visibleCount, setVisibleCount] = useState(getVisibleCount);

  // Update visible count on window resize
  React.useEffect(() => {
    const handleResize = () => {
      setVisibleCount(getVisibleCount());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const maxIndex = Math.max(0, filteredVoices.length - visibleCount);

  const handlePrevious = () => {
    setCurrentIndex(prev => Math.max(0, prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex(prev => Math.min(maxIndex, prev + 1));
  };

  const handleGenderFilter = (gender: 'all' | 'female' | 'male') => {
    setGenderFilter(gender);
    setCurrentIndex(0); // Reset to first page when changing filter
  };

  const visibleVoices = filteredVoices.slice(currentIndex, currentIndex + visibleCount);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center space-y-2">
        <h3 className="text-xl font-bold text-white">Choose Your Castmate Voice</h3>
        <p className="text-gray-400 text-sm">
          Select a voice that will guide you through your script rehearsal
        </p>
        {isUpdatingVoice && (
          <div className="flex items-center justify-center gap-2 text-purple-400 text-sm">
            <div className="w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full animate-spin"></div>
            Updating voice configuration...
          </div>
        )}
      </div>

      {/* Gender Filter */}
      <div className="flex justify-center">
        <div className="flex bg-black/30 rounded-lg p-1 border border-white/10">
          <button
            onClick={() => handleGenderFilter('all')}
            className={`
              flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors
              ${genderFilter === 'all'
                ? 'bg-blue-500 dark:bg-purple-600 text-white'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-white/10'
              }
            `}
          >
            <Users className="w-4 h-4" />
            All Voices
          </button>
          <button
            onClick={() => handleGenderFilter('female')}
            className={`
              flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors
              ${genderFilter === 'female'
                ? 'bg-blue-500 dark:bg-purple-600 text-white'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-white/10'
              }
            `}
          >
            <User className="w-4 h-4" />
            Female
          </button>
          <button
            onClick={() => handleGenderFilter('male')}
            className={`
              flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors
              ${genderFilter === 'male'
                ? 'bg-blue-500 dark:bg-purple-600 text-white'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-white/10'
              }
            `}
          >
            <User className="w-4 h-4" />
            Male
          </button>
        </div>
      </div>

      {/* Carousel */}
      <div className="relative">
        {/* Navigation buttons */}
        {filteredVoices.length > visibleCount && (
          <>
            <button
              onClick={handlePrevious}
              disabled={currentIndex === 0}
              className={`
                absolute left-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full flex items-center justify-center transition-colors shadow-sm
                ${currentIndex === 0
                  ? 'bg-gray-200 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                  : 'bg-white dark:bg-black/50 text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-black/70 border border-gray-200 dark:border-white/20'
                }
              `}
            >
              <ChevronLeft className="w-5 h-5" />

            </button>

            <button
              onClick={handleNext}
              disabled={currentIndex >= maxIndex}
              className={`
                absolute right-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full flex items-center justify-center transition-colors shadow-sm
                ${currentIndex >= maxIndex
                  ? 'bg-gray-200 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                  : 'bg-white dark:bg-black/50 text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-black/70 border border-gray-200 dark:border-white/20'
                }
              `}
            >
              <ChevronRight className="w-5 h-5" />

            </button>
          </>
        )}

        {/* Voice cards */}
        <div className="px-12">
          <div className={`grid gap-4 ${
            visibleCount === 1 ? 'grid-cols-1' :
            visibleCount === 2 ? 'grid-cols-2' :
            'grid-cols-3'
          }`}>
            {visibleVoices.map((voice) => (
              <VoicePreview
                key={voice.id}
                voice={voice}
                isSelected={selectedVoiceId === voice.id}
                onSelect={onVoiceSelect}
                className="h-full"
              />
            ))}
          </div>
        </div>

        {/* Pagination dots */}
        {filteredVoices.length > visibleCount && (
          <div className="flex justify-center mt-4 space-x-2">
            {Array.from({ length: maxIndex + 1 }, (_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`
                  w-2 h-2 rounded-full transition-colors
                  ${index === currentIndex
                    ? 'bg-blue-500 dark:bg-purple-500'
                    : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
                  }
                `}
              />
            ))}
          </div>
        )}
      </div>

      {/* Selected voice info */}
      {selectedVoiceId && (
        <div className="text-center p-3 bg-blue-50 dark:bg-purple-500/10 rounded-lg border border-blue-200 dark:border-purple-500/30 transition-colors duration-300">
          <p className="text-sm text-blue-700 dark:text-purple-300">
            Selected voice: <span className="font-semibold text-blue-900 dark:text-white">
              {AVAILABLE_VOICES.find(v => v.id === selectedVoiceId)?.name}
            </span>
          </p>
        </div>
      )}
    </div>
  );
};

export default VoiceCarousel;
