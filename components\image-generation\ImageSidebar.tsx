import React from 'react';
import Link from 'next/link';
import { PlusCircle } from 'lucide-react';

const ImageSidebar: React.FC = () => {
  return (
    <div className="bg-gray-200 w-64 h-full p-4">
      <h3 className="text-lg font-semibold mb-4">Image Actions</h3>
      <Link href="/generate-image" className="flex items-center text-blue-600 hover:text-blue-800">
        <PlusCircle className="mr-2" />
        Generate New Image
      </Link>
    </div>
  );
};

export default ImageSidebar;

