import _ from 'lodash';

interface PreprocessingConfiguration {
  data: any[];
  type: string;
  options?: {
    timeField?: string;
    valueFields?: string[];
    categoryField?: string;
    aggregation?: string;
    fillMissingValues?: boolean;
    filterOutliers?: boolean;
    normalization?: 'min-max' | 'z-score';
    binning?: {
      field: string;
      bins: number;
    };
    sortBy?: {
      field: string;
      order: 'asc' | 'desc';
    };
    limit?: number;
  };
}

interface PreprocessingResponse {
  success: boolean;
  error?: string;
  data?: any[];
  metadata?: {
    columns: string[];
    summary: {
      total: number;
      processed: number;
      outliers?: number;
      missingValues?: number;
    };
  };
}

export class DataPreprocessingTool {
  static description = `
    DataPreprocessingTool prepares and transforms data for optimal visualization.
    It handles:

    1. Data Cleaning:
       - Missing value handling
       - Outlier detection and removal
       - Data type validation and conversion
       - Duplicate removal
       - String normalization

    2. Data Transformation:
       - Time series processing
       - Numerical transformations
       - Categorical encoding
       - Binning/Bucketing
       - Normalization/Standardization

    3. Data Aggregation:
       - Grouping operations
       - Summary statistics
       - Pivot operations
       - Rolling calculations
       - Custom aggregations

    4. Data Optimization:
       - Data sampling
       - Dimensional reduction
       - Performance optimization
       - Memory efficiency
       - Format conversion

    5. Special Processing:
       - Time series specific operations
       - Geographic data handling
       - Hierarchical data processing
       - Multi-dimensional scaling
       - Custom data formats

    When to use:
    - Before creating any visualization
    - When data needs cleaning or transformation
    - For optimal performance with large datasets
    - When combining multiple data sources
    - For special data type handling (e.g., time series, geospatial)
    - To prepare data for specific chart types (e.g., aggregating for pie charts)
    - When data requires normalization or standardization
    - To handle outliers or missing values that could skew visualizations
    - For creating derived features or calculated fields
    - To reduce data dimensionality for complex visualizations
  `;

  async call(input: string): Promise<PreprocessingResponse> {
    try {
      const config = JSON.parse(input) as PreprocessingConfiguration;
      
      if (!this.validateConfig(config)) {
        throw new Error('Invalid preprocessing configuration');
      }

      // Process data based on visualization type
      const processedData = await this.processData(config);
      
      return {
        success: true,
        data: processedData.data,
        metadata: processedData.metadata
      };

    } catch (error) {
      console.error('DataPreprocessingTool error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  private validateConfig(config: PreprocessingConfiguration): boolean {
    if (!config.data || !Array.isArray(config.data) || config.data.length === 0) {
      return false;
    }

    if (!config.type || typeof config.type !== 'string') {
      return false;
    }

    return true;
  }

  private async processData(config: PreprocessingConfiguration) {
    let data = [...config.data];
    let metadata = {
      columns: Object.keys(data[0]),
      summary: {
        total: data.length,
        processed: 0,
        outliers: 0,
        missingValues: 0
      }
    };

    // Step 1: Handle missing values
    if (config.options?.fillMissingValues) {
      const result = this.handleMissingValues(data);
      data = result.data;
      metadata.summary.missingValues = result.missingCount;
    }

    // Step 2: Filter outliers
    if (config.options?.filterOutliers) {
      const result = this.filterOutliers(data, config.options.valueFields || []);
      data = result.data;
      metadata.summary.outliers = result.outlierCount;
    }

    // Step 3: Normalize data
    if (config.options?.normalization) {
      data = this.normalizeData(data, config.options.valueFields || [], config.options.normalization);
    }

    // Step 4: Process based on visualization type
    switch (config.type) {
      case 'line':
        return this.processTimeSeriesData(data, config.options);
      case 'bar':
        return this.processCategoricalData(data, config.options);
      case 'pie':
        return this.processProportionalData(data, config.options);
      case 'scatter':
        return this.processScatterData(data, config.options);
      case 'table':
        return this.processTabularData(data, config.options);
      default:
        return { data, metadata };
    }
  }

  private handleMissingValues(data: any[]) {
    let missingCount = 0;
    const processed = data.map(row => {
      const newRow = { ...row };
      Object.keys(newRow).forEach(key => {
        if (newRow[key] === null || newRow[key] === undefined || newRow[key] === '') {
          missingCount++;
          if (typeof row[key] === 'number') {
            // Use mean for numerical values
            const values = data.map(r => r[key]).filter(v => v !== null && v !== undefined);
            newRow[key] = _.mean(values);
          } else {
            // Use mode for categorical values
            const values = data.map(r => r[key]).filter(v => v !== null && v !== undefined);
            newRow[key] = _.chain(values).countBy().toPairs().maxBy(_.last).head().value();
          }
        }
      });
      return newRow;
    });
    return { data: processed, missingCount };
  }

  private filterOutliers(data: any[], fields: string[]) {
    let outlierCount = 0;
    const filtered = data.filter(row => {
      return fields.every(field => {
        const values = data.map(r => r[field]);
        const q1 = this.getQuantile(values, 0.25);
        const q3 = this.getQuantile(values, 0.75);
        const iqr = q3 - q1;
        const value = row[field];
        const isOutlier = value < q1 - 1.5 * iqr || value > q3 + 1.5 * iqr;
        if (isOutlier) outlierCount++;
        return !isOutlier;
      });
    });
    return { data: filtered, outlierCount };
  }

  private normalizeData(
    data: any[],
    fields: string[],
    method: 'min-max' | 'z-score'
  ): any[] {
    return data.map(row => {
      const normalized = { ...row };
      fields.forEach(field => {
        const values = data.map(r => r[field]);
        if (method === 'min-max') {
          const min = Math.min(...values);
          const max = Math.max(...values);
          normalized[field] = (row[field] - min) / (max - min);
        } else {
          const mean = _.mean(values);
          const std = Math.sqrt(_.sum(values.map(v => Math.pow(v - mean, 2))) / values.length);
          normalized[field] = (row[field] - mean) / std;
        }
      });
      return normalized;
    });
  }

  private processTimeSeriesData(data: any[], options?: PreprocessingConfiguration['options']) {
    const timeField = options?.timeField || this.inferTimeField(data);
    const valueFields = options?.valueFields || this.inferValueFields(data);

    // Sort by time
    data = _.sortBy(data, timeField);

    // Aggregate if needed
    if (options?.aggregation) {
      data = this.aggregateTimeSeriesData(data, timeField, valueFields, options.aggregation);
    }

    // Handle gaps in time series
    data = this.fillTimeSeriesGaps(data, timeField);

    return {
      data,
      metadata: {
        columns: [timeField, ...valueFields],
        summary: {
          total: data.length,
          processed: data.length,
          timeRange: {
            start: data[0][timeField],
            end: data[data.length - 1][timeField]
          }
        }
      }
    };
  }

  private processCategoricalData(data: any[], options?: PreprocessingConfiguration['options']) {
    const categoryField = options?.categoryField || this.inferCategoryField(data);
    const valueFields = options?.valueFields || this.inferValueFields(data);

    // Aggregate by category
    let processed = _(data)
      .groupBy(categoryField)
      .map((group, category) => ({
        [categoryField]: category,
        ...valueFields.reduce((acc, field) => ({
          ...acc,
          [field]: _.sumBy(group, field)
        }), {})
      }))
      .value();

    // Sort if requested
    if (options?.sortBy) {
      processed = _.orderBy(
        processed,
        [options.sortBy.field],
        [options.sortBy.order]
      );
    }

    // Limit if requested
    if (options?.limit) {
      processed = processed.slice(0, options.limit);
    }

    return {
      data: processed,
      metadata: {
        columns: [categoryField, ...valueFields],
        summary: {
          total: data.length,
          processed: processed.length,
          categories: processed.length
        }
      }
    };
  }

  private processProportionalData(data: any[], options?: PreprocessingConfiguration['options']) {
    const categoryField = options?.categoryField || this.inferCategoryField(data);
    const valueField = options?.valueFields?.[0] || this.inferValueFields(data)[0];

    // Aggregate and calculate proportions
    let processed = _(data)
      .groupBy(categoryField)
      .map((group, category) => ({
        [categoryField]: category,
        value: _.sumBy(group, valueField)
      }))
      .value();

    const total = _.sumBy(processed, 'value');
    processed = processed.map(item => ({
      ...item,
      percentage: (item.value / total) * 100
    }));

    // Sort by value descending
    processed = _.orderBy(processed, ['value'], ['desc']);

    return {
      data: processed,
      metadata: {
        columns: [categoryField, 'value', 'percentage'],
        summary: {
          total: data.length,
          processed: processed.length,
          segments: processed.length
        }
      }
    };
  }

  private processScatterData(data: any[], options?: PreprocessingConfiguration['options']) {
    // Handle outliers if requested
    if (options?.filterOutliers) {
      data = this.filterOutliers(data, options.valueFields || []).data;
    }

    // Normalize if requested
    if (options?.normalization) {
      data = this.normalizeData(data, options.valueFields || [], options.normalization);
    }

    // Bin data if requested
    if (options?.binning) {
      data = this.binData(data, options.binning);
    }

    return {
      data,
      metadata: {
        columns: Object.keys(data[0]),
        summary: {
          total: data.length,
          processed: data.length,
          dimensions: Object.keys(data[0]).length
        }
      }
    };
  }

  private processTabularData(data: any[], options?: PreprocessingConfiguration['options']) {
    // Apply sorting if specified
    if (options?.sortBy) {
      data = _.orderBy(
        data,
        [options.sortBy.field],
        [options.sortBy.order]
      );
    }

    // Apply limit if specified
    if (options?.limit) {
      data = data.slice(0, options.limit);
    }

    return {
      data,
      metadata: {
        columns: Object.keys(data[0]),
        summary: {
          total: data.length,
          processed: data.length,
          columns: Object.keys(data[0]).length
        }
      }
    };
  }

  private inferTimeField(data: any[]): string {
    return Object.keys(data[0]).find(key => 
      data.every(row => !isNaN(Date.parse(row[key])))
    ) || Object.keys(data[0])[0];
  }

  private inferValueFields(data: any[]): string[] {
    return Object.keys(data[0]).filter(key =>
      data.every(row => typeof row[key] === 'number' || !isNaN(parseFloat(row[key])))
    );
  }

  private inferCategoryField(data: any[]): string {
    return Object.keys(data[0]).find(key =>
      !this.inferValueFields(data).includes(key) &&
      new Set(data.map(row => row[key])).size <= Math.sqrt(data.length)
    ) || Object.keys(data[0])[0];
  }

  private aggregateTimeSeriesData(
    data: any[],
    timeField: string,
    valueFields: string[],
    aggregationType: string
  ): any[] {
    return _(data)
      .groupBy(timeField)
      .map((group, time) => ({
        [timeField]: time,
        ...valueFields.reduce((acc, field) => ({
          ...acc,
          [field]: this.aggregate(group, field, aggregationType)
        }), {})
      }))
      .value();
  }

  private aggregate(data: any[], field: string, type: string): number {
    switch (type) {
      case 'sum':
        return _.sumBy(data, field);
      case 'average':
        return _.meanBy(data, field);
      case 'max':
        return _.maxBy(data, field)?.[field] || 0;
      case 'min':
        return _.minBy(data, field)?.[field] || 0;
      default:
        return _.sumBy(data, field);
    }
  }

  private fillTimeSeriesGaps(data: any[], timeField: string): any[] {
    const dates = data.map(row => new Date(row[timeField]).getTime());
    const minDate = new Date(Math.min(...dates));
    const maxDate = new Date(Math.max(...dates));
    
    const allDates = this.generateDateRange(minDate, maxDate);
    const existingDates = new Set(dates.map(d => new Date(d).toISOString()));

    const filledData = [...data];
    allDates.forEach(date => {
      if (!existingDates.has(date.toISOString())) {
        const newRow = {
          [timeField]: date.toISOString(),
          ...Object.keys(data[0])
            .filter(key => key !== timeField)
            .reduce((acc, key) => ({ ...acc, [key]: null }), {})
        };
        filledData.push(newRow);
      }
    });

    return _.sortBy(filledData, timeField);
  }

  private generateDateRange(start: Date, end: Date): Date[] {
    
const dates: Date[] = [];
    let current = new Date(start);

    while (current <= end) {
      dates.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }

    return dates;
  }

  private getQuantile(numbers: number[], quantile: number): number {
    const sorted = _.sortBy(numbers);
    const pos = (sorted.length - 1) * quantile;
    const base = Math.floor(pos);
    const rest = pos - base;
    if (sorted[base + 1] !== undefined) {
      return sorted[base] + rest * (sorted[base + 1] - sorted[base]);
    }
    return sorted[base];
  }

  private binData(data: any[], binning: { field: string; bins: number }): any[] {
    const values = data.map(row => row[binning.field]);
    const min = Math.min(...values);
    const max = Math.max(...values);
    const binSize = (max - min) / binning.bins;

    return data.map(row => {
      const value = row[binning.field];
      const binIndex = Math.min(Math.floor((value - min) / binSize), binning.bins - 1);
      const binStart = min + binIndex * binSize;
      const binEnd = binStart + binSize;
      return {
        ...row,
        [`${binning.field}_bin`]: `${binStart.toFixed(2)} - ${binEnd.toFixed(2)}`
      };
    });
  }
}

