import { Tool } from "@langchain/core/tools";
import { ChatHistoryProcessor } from '../../lib/ChatHistoryProcessor';
import { TokenManagement } from "@/src/tokenTracker/tokenManagement";
import { ChatMessage } from '../../lib/ChatHistoryProcessor';

interface ProcessChatHistoryInput {
  rawChatHistory: string;
  tokenManager?: TokenManagement;
  maxHistoryMessages?: number;
  maxTokensPerMessage?: number;
}

interface ProcessChatHistoryOutput {
  chatHistoryArray: ChatMessage[];
  chatHistoryTokens: number;
  historyAnalytics: {
    tokenCount: number;
    messageCount: number;
    avgTokensPerMessage: number;
    [key: string]: any;
  };
}

/**
 * ProcessChatHistoryTool
 * Processes chat history for integration with Groq AI processing pipeline
 */
export class ProcessChatHistoryTool extends Tool {
  name = "processChatHistory";
  description = "Process raw chat history into structured format with token analytics";

  private readonly DEFAULT_MAX_HISTORY_MESSAGES = 10;
  private readonly DEFAULT_MAX_TOKENS_PER_MESSAGE = 500;

  constructor(
    private tokenManager?: TokenManagement,
    private config: {
      maxHistoryMessages?: number;
      maxTokensPerMessage?: number;
    } = {}
  ) {
    super();
  }

  async _call(input: string | ProcessChatHistoryInput): Promise<ProcessChatHistoryOutput> {
    try {
      // Handle different input types
      const {
        rawChatHistory,
        tokenManager = this.tokenManager,
        maxHistoryMessages = this.config.maxHistoryMessages || this.DEFAULT_MAX_HISTORY_MESSAGES,
        maxTokensPerMessage = this.config.maxTokensPerMessage || this.DEFAULT_MAX_TOKENS_PER_MESSAGE
      } = typeof input === 'string' ? { rawChatHistory: input } : input;

      // Create processor instance with configuration
      const historyProcessor = new ChatHistoryProcessor({
        maxHistoryMessages,
        maxTokensPerMessage
      });

      // Process the raw history
      const escapedHistory = this.escapeCodeSnippets(rawChatHistory);

      // Convert to message array
      const { messages: rawChatHistoryArray } =
        historyProcessor.convertToMessageArray(escapedHistory);

      // Process each message
      const chatHistoryArray = rawChatHistoryArray.map(msg => ({
        ...msg,
        text: this.escapeCodeSnippets(msg.text)
      }));

      // Calculate tokens and analytics
      const { tokenCount: chatHistoryTokens, analytics: historyAnalytics } =
        historyProcessor.calculateHistoryTokens(escapedHistory);

      // Update token manager if provided
      if (tokenManager) {
        await tokenManager.initializeWithChatHistory(
          chatHistoryTokens,
          historyAnalytics
        );
      }

      return {
        chatHistoryArray,
        chatHistoryTokens,
        historyAnalytics: {
          tokenCount: chatHistoryTokens,
          messageCount: chatHistoryArray.length,
          avgTokensPerMessage: chatHistoryTokens / chatHistoryArray.length,
          ...historyAnalytics
        }
      };

    } catch (error: any) {
      console.error("ProcessChatHistoryTool: Error processing chat history:", error);
      throw new Error(`Failed to process chat history: ${error.message}`);
    }
  }

  private escapeCodeSnippets(text: string): string {
    const codeBlockRegex = /```[\s\S]*?```|`[^`\n]+`/g;
    return text.replace(codeBlockRegex, (match) => {
      return match.replace(/{/g, '{{').replace(/}/g, '}}');
    });
  }
}