import { Pinecone } from "@pinecone-database/pinecone";
import { ChatAnthropic } from "@langchain/anthropic";
import { 
  SystemMessage, 
  HumanMessage
} from "@langchain/core/messages";
import { Document as CustomDocument } from "langchain/document";
import { FirestoreStore } from "./FirestoreStore"; // Ensure correct import path

// Initialize Pinecone
const pinecone = new Pinecone();
const pineconeIndex = pinecone.Index(process.env.PINECONE_INDEX!);


// Initialize FirestoreStore instance
let concatenatedPageNumbers = '';
let concatenatedPageContent = '';
let concatenatedPageTitle = '';
let relevantDocsForAI = '';

// Helper function to stream data to the client
async function streamToClient(controller: ReadableStreamDefaultController<any>, chunk: string) {
  const encoder = new TextEncoder();
  controller.enqueue(encoder.encode(chunk));
}

const FALLBACK_MESSAGE = "I'm sorry, I couldn't find any information to answer your request. Please try again with a different question.";

/**
 * Main function to query Pinecone and process AI response.
 */
export async function queryCLAUDEAcrossNamespacesAndProcessAI(
  controller: ReadableStreamDefaultController<any>,
  queryVector: number[],
  namespaces: string[] | null,
  userQuery: string,
  chatHistory: string,
  category: string | null,
  userId: string
) {
  console.log("User question:", userQuery);

  // Initialize FirestoreStore instance
const byteCollection = `users/${userId}/byteStoreCollection`;
const firestoreStore = new FirestoreStore({ collectionPath: byteCollection }); // FirestoreStore initialized with the correct path


  // Validate the query vector
  if (
    !Array.isArray(queryVector) ||
    queryVector.length === 0 ||
    !queryVector.every(num => typeof num === 'number')
  ) {
    console.error("Invalid query vector provided.");
    await streamToClient(controller, FALLBACK_MESSAGE + '\nEND_METADATA\n');
    controller.close();
    return;
  }

  try {
    console.log(`Namespaces to query: ${namespaces?.join(', ') || 'No specific namespaces'}`);

    let concatenatedDocuments = '';

    const queryPromises = namespaces?.map(async (namespace) => {
      console.log(`Querying Pinecone for namespace: ${namespace}`);
      try {
        // Perform a broader similarity search without filters
        const queryResponse = await pineconeIndex.namespace(namespace).query({
          vector: queryVector,
          topK: 3,
          includeValues: true,
          includeMetadata: true,
        });

        // Extract document IDs from the matches
        return queryResponse.matches.map((match: any) => match.metadata.documentId);
      } catch (error) {
        console.error(`Error querying namespace ${namespace}:`, error);
        return [];
      }
    }) || [];

    const allResults = await Promise.all(queryPromises);
    const uniqueDocumentIds = [...new Set(allResults.flat())].filter(id => id !== undefined && id !== '');

    console.log("Unique Document IDs to fetch from Firestore:", uniqueDocumentIds);

    if (uniqueDocumentIds.length === 0) {
      console.warn("No valid Document IDs found to fetch from Firestore.");
      await streamToClient(controller, FALLBACK_MESSAGE + '\nEND_METADATA\n');
      controller.close();
      return;
    }

    // Fetch document chunks using fetchDocumentChunksByDocId
    for (const documentId of uniqueDocumentIds) {
      const retrievedDocs = await firestoreStore.fetchDocumentChunksByDocId(documentId);
      retrievedDocs.forEach((doc: CustomDocument) => {
        if (doc) {
          // Extract metadata fields
          const metadata = doc.metadata || {};
          const content = metadata.text || doc.pageContent || 'No content available';
          const documentTitle = metadata.document_title || 'Unknown Title';
          const pageNumber = metadata.page_number || 'Unknown Page';

          // Logging for debugging purposes
          console.log(`Match metadata for document:`, metadata);
          console.log(`Match content:`, content);
          console.log(`Match documentTitle:`, documentTitle);
          console.log(`Match pageNumber:`, pageNumber);

          // Concatenate the page numbers and content
          concatenatedPageNumbers += `${pageNumber}, `;
          concatenatedPageContent += `Title: ${documentTitle} - Page: '${pageNumber}'\n\n\n${content}\n\n`;
          concatenatedPageTitle += `${documentTitle}\n`;
          relevantDocsForAI += `\nDocument (Title: ${documentTitle}, Page: ${pageNumber}): ${content}`;

          // Concatenate to the main documents for AI context
          concatenatedDocuments += `\n${content}\n`;
        }
      });
    }

    if (!concatenatedDocuments.trim()) {
      console.error('No documents retrieved to pass to the AI model.');
      await streamToClient(controller, FALLBACK_MESSAGE + '\nEND_METADATA\n');
      controller.close();
      return;
    }

    console.log("Sample of Concatenated Documents:\n", concatenatedDocuments.slice(0, 1000)); // Adjust slice as needed

    // Pass metadata (concatenatedPageNumbers, concatenatedPageContent) to the client
    const metadataPayload = JSON.stringify({
      pageContent: concatenatedPageContent,
      pageNumber: concatenatedPageNumbers.slice(0, -2), // Remove trailing comma and space
      pageTitle: concatenatedPageTitle.slice(0, -2),   // Remove trailing comma and space
    });

    // Send metadata to client
    await streamToClient(controller, metadataPayload);

    // Streaming the AI response
    const streamingComplete = new Promise<void>(async (resolve, reject) => {
      try {
        const modelWithCaching = new ChatAnthropic({
          streaming: true,
          temperature: 0.1,
          model: process.env.CLAUDE_MODEL!,
          apiKey: process.env.CLAUDE_API_KEY!,
          callbacks: [
            {
              handleLLMNewToken(token: any) {
                if (controller) {
                  controller.enqueue(new TextEncoder().encode(token));
                }
              },
              handleLLMEnd() {
                // Close the stream when the model signals the end
                controller.close();
                resolve();
              },
              handleLLMError(err: any) {
                console.error('Error during LLM streaming:', err);
                controller.error(new Error('Error during LLM streaming.'));
                reject(err);
              }
            },
          ],
        });

        let systemPromptContent: any;

        // Provide the preferred system prompt format
        if (category) { 
        systemPromptContent = `You are an expert in the ${category} field. Please:
            Identify the Target Audience:
            Determine the likely audience for the document, considering their knowledge level and background.
            Assess Expertise Level:
            Evaluate the probable expertise of individuals in this subject area and adopt the role of such an expert.
            Provide a Comprehensive Response:
            Using the provided context and any available chat history, answer the user's question with detailed and thorough information.`;
      } else {
        systemPromptContent = `Given the context provided,
            Determine the likely audience for the document, considering their knowledge level and background.
            Assess Expertise Level:
            Evaluate the probable expertise of individuals in this subject area and adopt the role of such an expert.
            Provide a Comprehensive Response:
            Using the provided context and any available chat history, answer the user's question with detailed and thorough information.`;
      }

        // Check if chatHistory and concatenatedDocuments are empty
        const validChatHistory = chatHistory?.trim() || "No previous chat history available.";
        const validConcatenatedDocs = concatenatedDocuments.trim() || "No document content available.";

        const messages = [
          new SystemMessage({
            content: systemPromptContent,
          }),   

          new HumanMessage({
            content: validConcatenatedDocs,
          }),

          new HumanMessage({
            content: validChatHistory,
          }),

          new HumanMessage({
            content: `User Message: ${userQuery}`,
          }),
        ];

        console.log("Formatted AI Messages:", messages);

        // Invoke the model with the messages
        await modelWithCaching.invoke(messages);

      } catch (error) {
        console.error('Error during AI response streaming:', error);
        reject(error);  // Ensure rejection happens in case of error
      }
    });

    // Await the completion of streaming
    await streamingComplete;

  } catch (error) {
    console.error('Error querying and processing AI response:', error);
    await streamToClient(controller, FALLBACK_MESSAGE + '\nEND_METADATA\n');
    controller.close();
  }
}
