import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./contexts/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      colors: {
        // Legacy colors (keeping for backward compatibility)
        'ike-purple': '#1f2937', // gray-800 equivalent - darker base
        'ike-dark-purple': '#030712', // gray-950 equivalent - nearly black
        'ike-message-bg' : '#111827', // gray-900 equivalent - very dark
        'ike-message-ai' : '#1f2937', // gray-800 equivalent - dark
        'ike-purple_b': '#0f172a', // slate-900 equivalent - very dark

        // Theme-aware colors
        'theme-bg': {
          'primary': 'rgb(var(--theme-bg-primary) / <alpha-value>)',
          'secondary': 'rgb(var(--theme-bg-secondary) / <alpha-value>)',
          'tertiary': 'rgb(var(--theme-bg-tertiary) / <alpha-value>)',
        },
        'theme-text': {
          'primary': 'rgb(var(--theme-text-primary) / <alpha-value>)',
          'secondary': 'rgb(var(--theme-text-secondary) / <alpha-value>)',
          'muted': 'rgb(var(--theme-text-muted) / <alpha-value>)',
        },
        'theme-border': 'rgb(var(--theme-border) / <alpha-value>)',
        'theme-accent': 'rgb(var(--theme-accent) / <alpha-value>)',
      },
    },
  },
  plugins: [],
};
export default config;