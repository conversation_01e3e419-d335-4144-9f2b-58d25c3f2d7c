'use client'

import { useState } from 'react'
import { jsPDF } from "jspdf"
import { Download, FileText, Upload, ExternalLink } from 'lucide-react'
import { SearchResult } from "./ChatVectorTypes"
import useUpload from "hooks/useUpload"
import { format } from 'date-fns'
import Link from 'next/link'

interface ResultCardProps {
  result: SearchResult
}

export default function Component({ result }: ResultCardProps) {
  const { handleUpload, status, progress, error } = useUpload()
  const [isUploading, setIsUploading] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)

  const formatScore = (score: number) => {
    return `${Math.round(score * 100)}%`
  }

  const formatDate = (timestamp: string) => {
    try {
      const date = new Date(Number(timestamp))
      if (isNaN(date.getTime())) {
        console.error('Invalid timestamp:', timestamp)
        return 'Invalid Date'
      }
      return format(date, 'd <PERSON><PERSON><PERSON>, yyyy')
    } catch (error) {
      console.error('Error formatting date:', error)
      return 'Date Error'
    }
  }

  const handlePdfExport = () => {
    const doc = new jsPDF()
    
    doc.setFontSize(16)
    doc.text("Chat Export", 20, 20)
    
    doc.setFontSize(12)
    doc.text(`Category: ${result.metadata.category}`, 20, 40)
    doc.text(`Date: ${new Date(Number(result.metadata.timestamp)).toLocaleDateString()}`, 20, 50)
    doc.text(`File: ${result.metadata.fileName || 'Untitled'}`, 20, 60)
    
    doc.setFontSize(14)
    doc.text("Summary:", 20, 80)
    doc.setFontSize(12)
    const summaryText = doc.splitTextToSize(result.metadata.summary, 170)
    doc.text(summaryText, 20, 90)

    doc.setFontSize(14)
    doc.text("Message Content:", 20, 120)
    doc.setFontSize(12)
    const contentText = doc.splitTextToSize(result.metadata.text, 170)
    doc.text(contentText, 20, 130)
    
    doc.save(`chat-export-${result.id}.pdf`)
  }

  const handleCsvExport = () => {
    const csvData = [
      ["Category", "Date", "File", "Summary", "Message"],
      [
        result.metadata.category,
        new Date(Number(result.metadata.timestamp)).toLocaleDateString(),
        result.metadata.fileName || 'Untitled',
        result.metadata.summary,
        result.metadata.text
      ]
    ]
    
    const csvString = csvData.map(row => row.map(cell => 
      `"${cell.replace(/"/g, '""')}"`
    ).join(",")).join("\n")
    
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `chat-export-${result.id}.csv`
    link.click()
  }

  const handleFileUpload = async () => {
    setIsUploading(true)
    try {
      const fileUrl = typeof result.metadata.fileUrl === 'function' 
        ? result.metadata.fileUrl(result.id)
        : result.metadata.fileUrl

      if (typeof fileUrl !== 'string') {
        throw new Error('File URL must be a string')
      }

      const response = await fetch(fileUrl)
      const blob = await response.blob()
      const file = new File([blob], result.metadata.fileName || 'untitled', { type: blob.type })
      await handleUpload(file, result.metadata.category)
    } catch (err) {
      console.error('Error uploading file:', err)
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className="rounded-lg bg-ike-dark-purple border border-ike-purple shadow-lg">
      <div className="p-4">
        {/* Header section with file info and external link */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-5 mt-8">
              <FileText className="w-6 h-6 text-amber-500 flex-shrink-0" />
              <h2 className="text-sm font-semibold truncate">
                {result.metadata.fileName || 'Untitled Document'}
              </h2>
            </div>
            <div className="flex flex-wrap gap-2">
              <span className="px-3 py-1 bg-ike-purple text-gray-300 text-sm rounded-full">
                {result.metadata.category === 'Unknown' ? 'General' : result.metadata.category}
              </span>
              <span className="px-3 py-1 bg-blue-500 bg-opacity-20 text-blue-400 text-sm rounded-full">
                Score: {formatScore(result.score)}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex gap-2 mb-5">
              <button
                className="h-8 w-8 text-green-500 hover:text-green-200 focus:outline-none focus:ring-2 focus:ring-green-100 focus:ring-opacity-50 rounded-full"
                onClick={handlePdfExport}
                aria-label="Export as PDF"
              >
                <Download className="h-4 w-4 mx-auto" />
              </button>
              <button
                className="h-8 w-8 text-blue-500 hover:text-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 rounded-full"
                onClick={handleCsvExport}
                aria-label="Export as CSV"
              >
                <FileText className="h-4 w-4 mx-auto" />
              </button>
              <button
                className="h-8 w-8 text-pink-600 hover:text-pink-300 focus:outline-none focus:ring-2 focus:ring-pink-300 focus:ring-opacity-50 rounded-full disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={handleFileUpload}
                disabled={isUploading}
                aria-label="Upload to storage"
              >
                <Upload className="h-4 w-4 mx-auto" />
              </button>
            </div>
            <Link
              href={`/chat/${result.metadata.chatId}`}
              className="flex items-center gap-1 text-blue-400 hover:text-blue-300 transition-colors ml-2"
            >
              <ExternalLink className="w-4 h-4 text-amber-500 mb-5" />
            </Link>
          </div>
        </div>

        {/* Content section */}
        <div className="space-y-4 pt-2">
          {/* Summary section with timestamp */}
          <div>
            <h3 className="text-gray-400 mb-2 text-xs text-right">
              {formatDate(result.metadata.timestamp)}
            </h3>
            <p className="text-gray-300 text-sm">{result.metadata.summary}</p>
          </div>

          {/* Original message section with expand option */}
          <div>
            <h3 className="text-gray-400 mb-2">Original Message</h3>
            <div className="bg-ike-message-bg rounded-lg p-4">
              <p className={`text-gray-300 text-sm ${!isExpanded ? 'line-clamp-3' : ''}`}>
                {result.metadata.text}
              </p>
              <button 
                className="text-blue-400 text-sm mt-2 hover:text-blue-300"
                onClick={() => setIsExpanded(!isExpanded)}
              >
                {isExpanded ? 'LESS' : 'MORE'}
              </button>
            </div>
          </div>

          {/* Message and document IDs grid */}
          <div className="grid grid-cols-3 gap-2">
            <div className="bg-gray-900 rounded-lg p-4">
              <h4 className="text-blue-400 text-sm font-medium mb-2">User MessageId</h4>
              <p className="text-gray-400 text-sm break-all">{result.metadata.userMessageId}</p>
            </div>
            <div className="bg-gray-900 rounded-lg p-4">
              <h4 className="text-blue-400 text-sm font-medium mb-2">AI MessageId</h4>
              <p className="text-gray-400 text-sm break-all">{result.metadata.aiMessageId}</p>
            </div>
            <div className="bg-gray-900 rounded-lg p-4">
              <h4 className="text-blue-400 text-sm font-medium mb-2">Document ID</h4>
              <p className="text-gray-400 text-sm break-all">{result.metadata.fileDocumentId}</p>
            </div>
          </div>

          {/* File preview section */}
          <div className="bg-gray-900 rounded-lg p-4 flex items-center justify-center">
            <div className="text-center max-w-full">
              <FileText className="w-16 h-16 text-blue-400 mx-auto mb-2" />
              <p className="text-gray-300 text-sm truncate max-w-[200px] mx-auto">
                {result.metadata.fileName}
              </p>
            </div>
          </div>

          {/* Upload progress */}
          {isUploading && (
            <div className="mt-2">
              <p className="text-sm text-amber-500">Uploading: {progress}%</p>
              {error && <p className="text-sm text-red-500">{error}</p>}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}