# SceneMate Script Reading Assistant - Comprehensive Analysis

## Overview

SceneMate is an intelligent script reading assistant application that utilizes ElevenLabs AI assistants to help actors practice their lines and rehearse scenes. The application provides both text-based chat functionality and voice-based rehearsal capabilities, creating an interactive AI scene partner for actors.

## Architecture Overview

### Frontend Architecture
The application follows a Next.js 13+ App Router architecture with React components organized into specialized modules:

```
app/
├── scriptReader/page.tsx          # Main landing page
├── api/                          # Backend API routes
│   ├── processScriptfile/        # Script file processing
│   ├── processMessage/           # Chat message processing
│   └── transcribeAudio/          # Voice transcription
components/
├── scriptreaderAI/               # Core script reader components
│   ├── Reader-modal.tsx          # Main modal container
│   ├── ChatTab.tsx              # Chat interface
│   ├── Rehearsals.tsx           # Voice rehearsal interface
│   ├── SideBar.tsx              # File management sidebar
│   ├── ChatMessages.tsx         # Message display component
│   ├── elevenlabs.ts            # ElevenLabs integration
│   └── useUpload.tsx            # File upload hook
└── VoiceAssistants/             # Voice component utilities
```

### Backend Architecture
The backend consists of several API endpoints that handle different aspects of the application:

1. **File Processing Pipeline** (`/api/processScriptfile`)
2. **Message Processing** (`/api/processMessage`)
3. **Audio Transcription** (`/api/transcribeAudio`)

## Core Components Analysis

### 1. Main Application Entry Point

**File**: `app/scriptReader/page.tsx`

This is the landing page that provides:
- Authentication integration with NextAuth
- User profile management
- Modal trigger for the main application
- Responsive design with Framer Motion animations

Key features:
- Google OAuth integration
- User profile completion flow
- Animated landing page with feature highlights
- Modal-based application launch

### 2. Reader Modal Container

**File**: `components/scriptreaderAI/Reader-modal.tsx`

The main application container that orchestrates all functionality:

**Key Features:**
- **Tab Management**: Three main sections (Rehearsing, Chat, Details)
- **File Management**: Script upload and selection
- **ElevenLabs Integration**: Voice conversation setup
- **State Management**: Centralized state for active files, chats, and voice status

**ElevenLabs Integration:**
```typescript
const conversation = useConversation({
  apiKey: process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY,
  onConnect: () => setApiConfigStatus('valid'),
  onDisconnect: () => setIsListening(false),
  onError: (error) => setApiConfigStatus('invalid')
})
```

**Voice Session Management:**
- Handles microphone permissions
- Manages conversation state with ElevenLabs agents
- Provides error handling and status feedback
- Integrates script context into voice sessions

### 3. Chat Interface

**File**: `components/scriptreaderAI/ChatTab.tsx`

Comprehensive chat system with advanced message management:

**Key Features:**
- **Message Deduplication**: Sophisticated system to prevent duplicate messages
- **Real-time Chat**: Firestore-based message storage and retrieval
- **Voice Recording**: Browser-based audio recording with transcription
- **File Context**: Messages are associated with specific script files
- **Audio Playback**: Automatic and manual audio playback for AI responses

**Message Flow:**
1. User sends message (text or voice)
2. Message stored in Firestore with temporary ID
3. API processes message with script context
4. AI generates response with audio
5. Response stored and displayed with audio controls

**Voice Recording Integration:**
```typescript
const processAudioBlob = async (blob: Blob) => {
  const formData = new FormData();
  formData.append("audio", blob, `voice-${Date.now()}.mp3`);

  const response = await fetch("/api/transcribeAudio", {
    method: "POST",
    body: formData,
  });
}
```

### 4. Voice Rehearsal Interface

**File**: `components/scriptreaderAI/Rehearsals.tsx`

Dedicated interface for voice-based script rehearsal:

**Features:**
- **Real-time Voice Interaction**: Direct conversation with ElevenLabs agents
- **Visual Feedback**: Connection status, speaking indicators, listening states
- **Error Handling**: Comprehensive error display and troubleshooting
- **Permission Management**: Microphone access handling

**Voice Controls:**
- Start/Stop conversation buttons
- Mute/Unmute functionality
- Visual indicators for speaking/listening states
- Error message display with dismissal

### 5. File Management System

**File**: `components/scriptreaderAI/SideBar.tsx`

Sophisticated file management with:
- **Script Library**: Display of uploaded scripts
- **Search Functionality**: Real-time script filtering
- **Upload Interface**: Drag-and-drop file upload
- **Mobile Responsiveness**: Adaptive UI for different screen sizes

**File Upload Flow:**
1. File selection through UI or drag-and-drop
2. Firebase Storage upload with progress tracking
3. Document processing and vectorization
4. ElevenLabs Knowledge Base integration
5. Firestore metadata storage

### 6. Message Display System

**File**: `components/scriptreaderAI/ChatMessages.tsx`

Advanced message rendering with:
- **Audio Integration**: Play/pause controls for AI responses
- **Markdown Rendering**: Rich text display for AI responses
- **Auto-scroll**: Automatic scrolling to new messages
- **Loading States**: Visual feedback during message processing

**Audio Management:**
```typescript
const toggleAudio = (messageKey: string) => {
  const audio = audioElements[messageKey];
  if (playingAudio === messageKey) {
    audio.pause();
    setPlayingAudio(null);
  } else {
    audio.play();
    setPlayingAudio(messageKey);
  }
};
```

## ElevenLabs Integration Deep Dive

### 1. Knowledge Base Management

**File**: `components/scriptreaderAI/elevenlabs.ts`

Comprehensive ElevenLabs API integration:

**Core Functions:**
- `uploadToKnowledgeBase()`: Uploads scripts to ElevenLabs Knowledge Base
- `computeRagIndex()`: Triggers and monitors RAG indexing
- `updateAgentKnowledgeBase()`: Associates documents with agents
- `getAgentConfiguration()`: Retrieves agent settings

**Upload Process:**
1. File validation (type, size, format)
2. File fetch from Firebase Storage URL
3. Upload to ElevenLabs Knowledge Base
4. RAG indexing initiation and monitoring
5. Agent association and configuration update

**Error Handling:**
- Comprehensive error catching and logging
- Retry mechanisms for API calls
- Detailed error messages for debugging
- Fallback configurations

### 2. Voice Conversation Management

The application uses the `@11labs/react` package for real-time voice conversations:

**Session Management:**
```typescript
const conversationId = await conversation.startSession({
  agentId: agentId,
  scriptId: activeTab,
  scriptName: fileName
});
```

**Features:**
- Real-time bidirectional voice communication
- Script context injection into conversations
- Connection state management
- Error recovery and reconnection

## Backend API Analysis

### 1. Script File Processing

**File**: `app/api/processScriptfile/route.ts`

Comprehensive document processing pipeline:

**Processing Steps:**
1. **File Validation**: Type, size, and format validation
2. **Document Processing**: Text extraction and chunking
3. **Embedding Generation**: OpenAI embeddings for semantic search
4. **Vector Storage**: Pinecone vector database storage
5. **Firestore Storage**: Document metadata and chunks
6. **ElevenLabs Upload**: Knowledge Base integration

**Key Features:**
- Support for multiple file formats (PDF, DOCX, TXT, images)
- Intelligent chunking with overlap
- Metadata preservation and enhancement
- Error handling with fallback storage

### 2. Message Processing

**File**: `app/api/processMessage/route.ts`

Sophisticated message processing with context awareness:

**Processing Flow:**
1. **Message Storage**: User message stored in Firestore
2. **Vector Search**: Semantic search across script content
3. **Context Retrieval**: Relevant script chunks and chat history
4. **AI Generation**: Groq-based response generation
5. **Audio Synthesis**: OpenAI TTS for voice responses
6. **Response Storage**: Assistant message with audio URL

**AI Prompt Engineering:**
```typescript
const systemMessage = {
  role: "system",
  content: `You are an AI Script/Rehearsal Assistant that helps actors
  practice their lines and prepare for performances...`
};
```

**Context Integration:**
- Script chunks from vector search
- Relevant chat history
- User preferences and rehearsal state
- Character and scene information

### 3. Audio Transcription

**File**: `app/api/transcribeAudio/route.ts`

Voice-to-text conversion for user input:
- OpenAI Whisper integration
- Audio format handling
- Error recovery and validation
- Session-based authentication

## Data Flow Architecture

### 1. File Upload and Processing Flow

```
User Upload → Firebase Storage → Document Processing →
Vector Generation → Pinecone Storage → ElevenLabs Upload →
Knowledge Base Indexing → Agent Association
```

### 2. Chat Message Flow

```
User Input → Firestore Storage → Vector Search →
Context Retrieval → AI Processing → Audio Generation →
Response Storage → UI Update
```

### 3. Voice Rehearsal Flow

```
Voice Input → ElevenLabs Agent → Script Context →
AI Response → Voice Output → Session Management
```

## Database Schema

### Firestore Collections

**Users Collection:**
```
users/{userId}/
├── files/{fileId}                 # Script files metadata
├── chats/{chatId}/                # Chat sessions
│   └── messages/{messageId}       # Individual messages
├── ElevenLabsData/{docId}         # ElevenLabs integration data
└── MetadataFallback/{docId}       # Error recovery data
```

**File Document Structure:**
```typescript
{
  name: string,
  size: number,
  type: string,
  category: "SceneMate",
  namespace: string,
  downloadUrl: string,
  createdAt: Date,
  isImage: boolean,
  elevenlabs_upload_requested: boolean
}
```

**Message Document Structure:**
```typescript
{
  role: "user" | "assistant",
  text: string,
  createdAt: Timestamp,
  audioUrl?: string,
  fileDocumentId: string,
  tempId?: string
}
```

### Pinecone Vector Database

**Index Structure:**
- **Index Name**: "scenemate"
- **Namespaces**: One per document (using document ID)
- **Vectors**: OpenAI embeddings (1536 dimensions)
- **Metadata**: Document chunks with enhanced metadata

**Metadata Schema:**
```typescript
{
  content: string,
  doc_id: string,
  chunk_id: string,
  document_title: string,
  category: "SceneMate",
  file_type: string,
  sectionTitle: string,
  questions: string[],
  is_summary: boolean
}
```

## Integration Points

### 1. Authentication
- **NextAuth**: Google OAuth integration
- **Firebase Auth**: Token-based API authentication
- **Session Management**: Client-server session synchronization

### 2. Storage Systems
- **Firebase Storage**: File uploads and audio storage
- **Firestore**: Metadata, messages, and user data
- **Pinecone**: Vector embeddings for semantic search

### 3. AI Services
- **ElevenLabs**: Voice conversations and Knowledge Base
- **OpenAI**: Embeddings and text-to-speech
- **Groq**: Fast language model inference

### 4. Frontend Libraries
- **Next.js 13+**: App Router and server components
- **React**: Component-based UI
- **Framer Motion**: Animations and transitions
- **Tailwind CSS**: Utility-first styling

## Key Features Summary

### 1. Script Management
- Multi-format file upload (PDF, DOCX, TXT, images)
- Automatic text extraction and processing
- Vector-based semantic search
- ElevenLabs Knowledge Base integration

### 2. Chat Interface
- Real-time messaging with Firestore
- Voice recording and transcription
- Audio response playback
- Message deduplication and state management

### 3. Voice Rehearsal
- Real-time voice conversations with ElevenLabs agents
- Script context injection
- Connection state management
- Error handling and recovery

### 4. AI Integration
- Context-aware response generation
- Script-specific knowledge retrieval
- Multi-modal interaction (text and voice)
- Personalized rehearsal assistance

## Technical Considerations

### 1. Performance Optimizations
- Lazy loading of components
- Efficient vector search with top-K results
- Audio streaming and caching
- Optimistic UI updates

### 2. Error Handling
- Comprehensive error boundaries
- Graceful degradation for API failures
- User-friendly error messages
- Automatic retry mechanisms

### 3. Security
- Authentication-based API access
- File type and size validation
- Secure token management
- Environment variable protection

### 4. Scalability
- Serverless API architecture
- Vector database optimization
- Efficient state management
- Modular component design

## Component Interaction Flow

### 1. Application Initialization
```
User Access → Landing Page → Authentication → Modal Launch →
Component Initialization → File Loading → State Setup
```

### 2. File Upload Process
```
File Selection → Validation → Firebase Upload →
Document Processing → Vector Generation → ElevenLabs Upload →
Knowledge Base Indexing → UI Update
```

### 3. Chat Interaction
```
User Input → Message Storage → Context Retrieval →
AI Processing → Response Generation → Audio Synthesis →
Storage → UI Display
```

### 4. Voice Rehearsal
```
Permission Request → ElevenLabs Connection →
Session Start → Voice Input → AI Processing →
Voice Response → Session Management
```

## API Endpoints Summary

### `/api/processScriptfile`
- **Purpose**: Process uploaded script files
- **Input**: File metadata and download URL
- **Processing**: Document parsing, vectorization, ElevenLabs upload
- **Output**: Success status and ElevenLabs metadata

### `/api/processMessage`
- **Purpose**: Handle chat messages and generate responses
- **Input**: User message, chat context, file reference
- **Processing**: Vector search, context assembly, AI generation, TTS
- **Output**: AI response text and audio URL

### `/api/transcribeAudio`
- **Purpose**: Convert voice recordings to text
- **Input**: Audio blob from browser recording
- **Processing**: OpenAI Whisper transcription
- **Output**: Transcribed text

## State Management Architecture

### 1. Global State (Reader Modal)
- Active file/script selection
- Chat session management
- Voice connection status
- Error states and notifications

### 2. Component State (Chat Tab)
- Message history and display
- Recording status
- Upload progress
- Loading indicators

### 3. Persistent State (Firestore)
- User files and metadata
- Chat history and messages
- ElevenLabs integration data
- User preferences

## Error Handling Strategy

### 1. Frontend Error Boundaries
- Component-level error catching
- Graceful degradation for failed features
- User-friendly error messages
- Recovery action suggestions

### 2. API Error Handling
- Comprehensive try-catch blocks
- Detailed error logging
- Status code-based responses
- Retry mechanisms for transient failures

### 3. Integration Error Management
- ElevenLabs API error handling
- Firebase service error recovery
- Network failure resilience
- Fallback configurations

## Performance Considerations

### 1. Frontend Optimizations
- Component lazy loading
- Efficient re-rendering with React keys
- Optimistic UI updates
- Audio preloading and caching

### 2. Backend Optimizations
- Vector search result limiting
- Efficient document chunking
- Parallel processing where possible
- Connection pooling for external APIs

### 3. Data Management
- Efficient Firestore queries
- Pinecone namespace organization
- Audio file compression
- Metadata optimization

This comprehensive analysis provides a complete understanding of the SceneMate script reading assistant application, covering its architecture, components, integrations, data flow, and technical implementation details.