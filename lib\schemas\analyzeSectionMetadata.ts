// /lib/schemas/analyzeSectionMetadata.ts

import { ChatOpenAI } from "@langchain/openai";
import { z } from "zod";
import { retry } from "../../Utils/retry";

/**
 * Analyzes a specific section of a document to extract metadata and key attributes.
 * @param sectionTitle - The title of the section.
 * @param sectionContent - The content of the section.
 * @returns An object containing metadata and key attributes for the section.
 */
export async function analyzeSectionMetadata(sectionTitle: string, sectionContent: string): Promise<any> {
  const model = new ChatOpenAI({
    temperature: 0,
    model: process.env.OPENAI_MODEL!,
    apiKey: process.env.OPENAI_API_KEY!,
  });

  // Define a Zod schema to validate the structured output
  const metadataSchema = z.object({
    documentTitle: z.string(),
    author: z.string().optional(),
    publicationDate: z.string().optional(),
    keyAttributes: z.record(z.string()).optional(), // Made optional to handle missing cases
    additionalDetails: z.object({
      sectionTitle: z.string(),
      sectionContent: z.string(),
    }).optional(),
  });

  // Initialize the model with structured output capability
  const structuredLlm = model.withStructuredOutput(metadataSchema, {
    method: "json_mode",
    name: "MetadataSchema",
  });

  const prompt = `
    Analyze the following section of the document and extract key metadata and attributes. 
    If available, include details such as document title, author, publication date, and any additional 
    attributes related to the content. Mark optional fields like author and summary appropriately.
    
    Section Title: ${sectionTitle}
    Section Content:
    ${sectionContent}
    
    The output should strictly follow this JSON format without any additional text:
    {
      "documentTitle": "string",
      "author": "string | optional",
      "publicationDate": "string | optional",
      "keyAttributes": {
        "attribute1": "string",
        "attribute2": "string | optional",
        ...
      },
      "additionalDetails": {
        "sectionTitle": "string",
        "sectionContent": "string"
      }
    }
    
    If there are no key attributes to extract, provide an empty object for "keyAttributes":
    "keyAttributes": {}
  `;

  try {
    const response = await retry(() => structuredLlm.invoke(prompt));

    // Validate the response using Zod schema
    const parseResult = metadataSchema.safeParse(response);
    if (!parseResult.success) {
      console.error("Metadata Schema Validation Failed:", parseResult.error.errors);
      console.error("Received response:", JSON.stringify(response, null, 2));
      throw new Error("Failed to analyze section metadata with valid schema.");
    }

    return parseResult.data;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error("Error analyzing section metadata:", errorMessage);
    console.error("Prompt used:", prompt);
    console.error("Section Title:", sectionTitle);
    console.error("Section Content (first 500 chars):", sectionContent.substring(0, 500));
    throw new Error("Failed to analyze section metadata.");
  }
}
