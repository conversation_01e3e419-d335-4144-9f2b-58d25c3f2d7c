// Import model-specific integrations
import { processWithOpenAI, getOpenAIModels } from "./openai-ai";
import { processWithAnthropic, getAnthropicModels } from "./anthropic-ai";
import { processWithClaude, getClaudeModels } from "./claude-ai";
import { processWithGroq, getGroqModels } from "./groq-ai";
import { processWithGoogleAI, getGoogleAIModels } from "./google-ai";

// Define types for the LLM tool
export type LlmProvider = 'openai' | 'anthropic' | 'claude' | 'google' | 'groq' | 'deepseek';

export interface ModelOptions {
  temperature?: number;
  maxTokens?: number;
  [key: string]: any;
}

export interface WebSearchOptions {
  searchContextSize?: string;
  userLocation?: string;
  forceWebSearch?: boolean;
  [key: string]: any;
}

export interface ProcessContentOptions {
  prompt: string;
  context?: string;
  model?: string;
  provider?: LlmProvider;
  modelOptions?: ModelOptions;
  useWebSearch?: boolean;
  webSearchOptions?: WebSearchOptions;
}

export interface ProcessLargeContentOptions {
  content: string;
  prompt: string;
  context?: string;
  model?: string;
  provider?: LlmProvider;
  maxChunkSize?: number;
  maxChunks?: number;
}

/**
 * LLM Tool for processing content with various language models
 */
export class LlmTool {
  constructor() {
    // No initialization needed
  }

  /**
   * Process content with a language model
   * @param options - Processing options
   * @returns - The generated content
   */
  async processContent(options: ProcessContentOptions): Promise<string> {
    try {
      const {
        prompt,
        context = "",
        model = "gpt-4o",
        provider = "openai",
        modelOptions = {},
        useWebSearch = false,
        webSearchOptions = {}
      } = options;

      if (!prompt) {
        throw new Error("Prompt is required");
      }

      // Handle web search if enabled
      let enhancedContext = context;
      if (useWebSearch) {
        // This is a placeholder for web search functionality
        // In a real implementation, you would call a web search API here
        console.log("Web search requested with options:", webSearchOptions);
        // For now, we'll just add a note to the context
        enhancedContext = enhancedContext ?
          `${enhancedContext}\n\n[Note: Web search was requested but is not fully implemented]` :
          "[Note: Web search was requested but is not fully implemented]";
      }

      // Combine prompt and context
      const fullPrompt = enhancedContext ? `${enhancedContext}\n\n${prompt}` : prompt;

      // Process with the appropriate provider
      switch (provider.toLowerCase() as LlmProvider) {
        case "anthropic":
          return await processWithAnthropic({
            prompt: fullPrompt,
            model: model,
            modelOptions: modelOptions
          });
        case "claude":
          return await processWithClaude({
            prompt: fullPrompt,
            model: model,
            modelOptions: modelOptions
          });
        case "google":
          return await processWithGoogleAI({
            prompt: fullPrompt,
            model: model
          });
        case "groq":
          return await processWithGroq({
            prompt: fullPrompt,
            model: model,
            modelOptions: modelOptions
          });
        case "openai":
        default:
          return await processWithOpenAI({
            prompt: fullPrompt,
            model: model,
            modelOptions: modelOptions
          });
      }
    } catch (error) {
      console.error("Error processing content with LLM:", error);
      throw error;
    }
  }

  /**
   * Get available models for a provider
   * @param provider - The LLM provider (openai, anthropic, claude, google, groq)
   * @returns - List of available models
   */
  getAvailableModels(provider: LlmProvider = "openai"): string[] {
    switch (provider.toLowerCase() as LlmProvider) {
      case "anthropic":
        return getAnthropicModels().map(model => model.id);
      case "claude":
        return getClaudeModels();
      case "google":
        return getGoogleAIModels();
      case "groq":
        return getGroqModels();
      case "openai":
      default:
        return getOpenAIModels();
    }
  }

  /**
   * Split text into chunks of a specified maximum length
   * @param text - The input text to split
   * @param maxLength - Maximum length of each chunk
   * @param maxChunks - Maximum number of chunks to generate
   * @returns Array of text chunks
   */
  splitIntoChunks(text: string, maxLength: number = 4000, maxChunks: number = 10): string[] {
    const chunks: string[] = [];

    if (!text || typeof text !== "string") return chunks;

    // First attempt: Split by paragraphs
    const paragraphs = text.split("\n");
    let currentChunk = "";

    for (const paragraph of paragraphs) {
      if (currentChunk.length + paragraph.length > maxLength) {
        chunks.push(currentChunk.trim());
        currentChunk = paragraph;
        if (chunks.length >= maxChunks - 1) break; // Reserve last chunk for remainder
      } else {
        currentChunk += (currentChunk ? "\n" : "") + paragraph;
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }

    // If no chunks or insufficient splitting, fall back to character-based splitting
    if (chunks.length === 0) {
      for (let i = 0; i < text.length && chunks.length < maxChunks; i += maxLength) {
        chunks.push(text.substring(i, i + maxLength).trim());
      }
    }

    // Truncate to maxChunks if exceeded
    return chunks.slice(0, maxChunks);
  }

  /**
   * Process large content by splitting it into chunks and processing each chunk
   * @param options - Processing options
   * @returns - The combined processed content
   */
  async processLargeContent(options: ProcessLargeContentOptions): Promise<string> {
    const {
      content,
      prompt,
      context = "",
      model = "gpt-4o",
      provider = "openai",
      maxChunkSize = 4000,
      maxChunks = 10
    } = options;

    if (!content || !prompt) {
      throw new Error("Content and prompt are required");
    }

    // Split content into chunks
    const chunks = this.splitIntoChunks(content, maxChunkSize, maxChunks);

    let processedContent = "";

    // Process each chunk
    for (const chunk of chunks) {
      // Replace {chunk} placeholder in prompt with actual chunk
      const chunkPrompt = prompt.replace("{chunk}", chunk);

      const result = await this.processContent({
        prompt: chunkPrompt,
        context,
        model,
        provider
      });

      processedContent += result + "\n\n";
    }

    return processedContent.trim();
  }
}

// Export a singleton instance
export const llmTool = new LlmTool();
