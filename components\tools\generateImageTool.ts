// src/components/tools/generateImageTool.ts

import OpenAI from 'openai';

interface GenerateImageResult {
  success: boolean;
  base64Image?: string;
  error?: string;
}

export class GenerateImageTool {
  private openai: OpenAI;

  constructor(openaiApiKey: string) {
    this.openai = new OpenAI({
      apiKey: openaiApiKey,
    });
  }

  /**
   * Refines a user’s initial prompt via a GPT model.
   */
  public async refinePrompt(initialPrompt: string): Promise<string> {
    try {
      const chatCompletion = await this.openai.chat.completions.create({
        model: 'gpt-4o-2024-11-20', // (Assuming this is a valid model name in your usage)
        messages: [
          {
            role: 'system',
            content: `You are an expert in generating highly detailed, photorealistic image prompts. When a user provides a prompt, your task is to transform it into a single, exceptionally descriptive and precise prompt. Focus on the following elements to enhance the image’s realism and artistic quality:

Lighting and Atmosphere

Specify the type of lighting (e.g., soft natural light, dramatic backlight, golden-hour glow).
Include atmospheric details (e.g., mist, haze, crisp air).
Camera Angle and Perspective

Indicate viewpoint (e.g., low-angle shot, eye-level, aerial).
Determine focal length and depth of field (e.g., wide-angle lens, shallow depth of field).
Color Palette and Mood

Choose complementary or contrasting colors to establish a specific mood (e.g., warm and inviting, cool and mysterious).
Include descriptive adjectives for the palette (e.g., muted pastels, vibrant neons).
Textures and Materials

Describe the surfaces and materials in detail (e.g., weathered wood, glossy metal, soft fabric).
Highlight subtle elements like reflections or patterns.
Environmental Details

Enrich the scene with contextual elements (e.g., surrounding architecture, natural landscape, distant objects).
Ensure they support the main subject without overwhelming it.
Composition and Framing

Apply compositional guidelines (e.g., rule of thirds, centered framing, leading lines).
Clarify the placement of the subject and supporting elements.
How to Apply These Steps

Read the User’s Core Concept – Identify the subject or central idea.
Expand with Relevant Details – Incorporate each of the categories above to heighten realism.
Maintain Clarity – Combine all details into a single prompt, ensuring it flows naturally and concisely.
Avoid Extraneous Commentary – Present the final refined prompt without additional explanations or meta-notes.
Example of a Refined Prompt (Using These Steps)
“Capture a crisp, eye-level shot of a solitary figure standing on a secluded 
shoreline at dawn, illuminated by soft, warm light filtering through a gentle 
morning haze. Incorporate a shallow depth of field to keep the subject in sharp focus 
while softly blurring the rolling waves and distant horizon. Emphasize natural textures—subtle ripples 
in wet sand, the subdued sheen of the water, and the slight wrinkles in the figure’s light-colored 
fabric. Use a serene color palette of soft blues and pale golds to evoke a peaceful, reflective mood. 
Place the main subject slightly off-center, guided by the rule of thirds, to balance the wide, 
open sky and gentle surf. Highlight realistic details such as delicate reflections on the water’s 
surface and faint footprints trailing behind the figure, creating a professional, 
photorealistic scene.”`
          },
          { role: 'user', content: initialPrompt }
        ],
        max_tokens: 200,
        temperature: 0.7
      });

      // If no message, return original prompt to avoid throwing
      return chatCompletion.choices[0]?.message?.content || initialPrompt;
    } catch (error) {
      console.error('Prompt refinement error:', error);
      // Fallback: just return original prompt
      return initialPrompt;
    }
  }

  /**
   * Generates a base64-encoded image from a prompt and returns it.
   */
  public async generateImageBase64(
    prompt: string,
    model = 'dall-e-3',
    size: '1024x1024' | '256x256' | '512x512' = '1024x1024'
  ): Promise<GenerateImageResult> {
    try {
      const response = await this.openai.images.generate({
        prompt,
        model,
        n: 1,
        size,
        response_format: 'b64_json',
      });

      const b64Data = response.data?.[0]?.b64_json;
      return b64Data
        ? { success: true, base64Image: b64Data }
        : { success: false, error: 'No image data returned' };
    } catch (error: any) {
      return {
        success: false,
        error: error?.message || 'Image generation failed',
      };
    }
  }
}
