'use client';

import React, { useState, useEffect } from 'react';
import {
  collection,
  query,
  getDocs,
  where,
  orderBy,
  limit,
  addDoc,
  serverTimestamp,
} from 'firebase/firestore';
import { db } from 'components/firebase';
import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Search, ExpandIcon, ChevronLeft, Trash, FilesIcon, SpeechIcon, FoldersIcon, MessageSquare } from 'lucide-react';
import Image from 'next/image';
import { Timestamp } from 'firebase/firestore';
import DeleteConfirmationModal from './DeleteConfirmationModal';
import SignOutModal from './SignOutModal';
import FileManagerDirectory from './FileManagerDirectory';



export interface FileData {
  id: string;
  name: string;
  category: string;
  createdAt: Timestamp;
  lastUpdated: Timestamp;
  size: number;
  type: string;
  downloadUrl: string;
  chatCount: number;
}

const truncateFileName = (fileName: string, maxLength: number = 27) => {
  return fileName.length > maxLength ? fileName.slice(0, maxLength) + '...' : fileName;
};

const SkeletonLoader = () => (
  <div className="animate-pulse">
    <div className="h-8 bg-gray-300 rounded mb-4"></div>
    <div className="space-y-2">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="grid grid-cols-6 gap-4">
          <div className="col-span-2 h-6 bg-gray-300 rounded"></div>
          <div className="h-6 bg-gray-300 rounded"></div>
          <div className="h-6 bg-gray-300 rounded"></div>
          <div className="h-6 bg-gray-300 rounded"></div>
          <div className="h-6 bg-gray-300 rounded"></div>
        </div>
      ))}
    </div>
  </div>
);

function FileManagerStats() {
  const { data: session } = useSession();
  const [stats, setStats] = useState({
    folders: 0,
    files: 0,
    chats: 0
  });

  useEffect(() => {
    const fetchStats = async () => {
      if (!session?.user?.email) return;

      try {
        const userEmail = session.user.email;
        const filesCollection = collection(db, 'users', userEmail, 'files');
        const filesSnapshot = await getDocs(filesCollection);
        
        // Count files
        const filesCount = filesSnapshot.size;

        // Count folders (categories)
        const categories = new Set();
        filesSnapshot.forEach(doc => {
          const category = doc.data().category;
          if (category && category !== 'Unknown') {
            categories.add(category);
          }
        });
        const foldersCount = categories.size;

        // Count chats
        const chatsCollection = collection(db, 'users', userEmail, 'chats');
        const chatsSnapshot = await getDocs(chatsCollection);
        const chatsCount = chatsSnapshot.size;

        setStats({
          folders: foldersCount,
          files: filesCount,
          chats: chatsCount
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
      }
    };

    fetchStats();
  }, [session]);

  return (
    <div className="ml-auto flex items-center gap-2">
      
      <div className="px-6 py-2 bg-ike-dark-purple/75 text-gray-400 rounded-2xl 
                    border border-gray-700 hover:bg-ike-message-bg 
                    transition-colors duration-200 shadow-sm
                    focus:outline-none focus:ring-1 focus:ring-gray-600
                    flex items-center justify-center">
         <FoldersIcon className="h-5 w-5 mr-2 text-blue-500" />  {stats.folders}
      </div>
      <div className="px-6 py-2 bg-ike-dark-purple/75 text-gray-400 rounded-2xl 
                    border border-gray-700 hover:bg-ike-message-bg 
                    transition-colors duration-200 shadow-sm
                    focus:outline-none focus:ring-1 focus:ring-gray-600
                    flex items-center justify-center">
            <FilesIcon className="h-5 w-5 mr-2 text-amber-500" />  {stats.files}
      </div>
      <div className="px-6 py-2 bg-ike-dark-purple/75 text-gray-400 rounded-2xl 
                    border border-gray-700 hover:bg-ike-message-bg 
                    transition-colors duration-200 shadow-sm
                    focus:outline-none focus:ring-1 focus:ring-gray-600
                    flex items-center justify-center">
         <MessageSquare className="h-5 w-5 mr-2 text-pink-600" />  {stats.chats}
      </div>
    </div>
  );
}

export default function FileManagerStructure() {
  const [files, setFiles] = useState<FileData[]>([]);
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const { data: session } = useSession();
  const [isProcessing, setIsProcessing] = useState(false);
  const [deletefiledialog, setDeletefiledialog] = useState('');
  const [selectedFile, setSelectedFile] = useState<FileData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showSignOutModal, setShowSignOutModal] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchFiles = async () => {
      if (session?.user?.email) {
        setIsLoading(true);
        try {
          const userEmail = session.user.email;
          const filesCollection = collection(db, 'users', userEmail, 'files');
          const filesQuery = query(filesCollection);
          const snapshot = await getDocs(filesQuery);

          const fetchedFiles = await Promise.all(
            snapshot.docs.map(async (doc) => {
              const fileData = {
                id: doc.id,
                ...doc.data(),
              } as FileData;

              const chatsQuery = query(
                collection(db, 'users', userEmail, 'chats'),
                where('fileDocumentId', '==', doc.id)
              );
              const chatsSnapshot = await getDocs(chatsQuery);
              fileData.chatCount = chatsSnapshot.docs.length;

              return fileData;
            })
          );

          setFiles(fetchedFiles);
        } catch (error) {
          console.error('Error fetching files:', error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchFiles();
  }, [session]);

  const toggleCategory = (category: string) => {
    if (expandedCategory === category) {
      setExpandedCategory(null);
      setShowSearchResults(false);
    } else {
      setExpandedCategory(category);
      setShowSearchResults(true);
    }
  };

  const filteredFiles = files.filter(
    (file) =>
      file.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      file.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const groupedFiles = [...new Set(filteredFiles.map((file) => file.category))].map(
    (category) => ({
      category,
      files: filteredFiles.filter((file) => file.category === category),
      isExpanded: expandedCategory === category,
    })
  );

  const formatTimestamp = (timestamp: Timestamp | undefined): string => {
    if (!timestamp || !(timestamp instanceof Timestamp)) {
      return 'N/A';
    }
    const date = timestamp.toDate();
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const toggleSearchResults = () => {
    setShowSearchResults(!showSearchResults);
    if (expandedCategory) {
      setExpandedCategory(null);
    }
  };

  const handleDeleteClick = (file: FileData) => {
    setSelectedFile(file);
    setShowModal(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedFile && session?.user?.email) {
      try {
        setIsProcessing(true);
        setDeletefiledialog(`File: ${selectedFile.name} is being removed`);

        const response = await fetch('/api/deleteDocumentAndChats', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            userId: session.user.email,
            namespace: selectedFile.id,
          }),
        });

        if (response.ok) {
          setFiles((prevFiles) =>
            prevFiles.filter((file) => file.id !== selectedFile.id)
          );
        } else {
          console.error('Failed to delete file:', await response.json());
        }
      } catch (error) {
        console.error('Error deleting file:', error);
      } finally {
        setIsProcessing(false);
        setShowModal(false);
        setSelectedFile(null);
      }
    }
  };

  const handleCancelDelete = () => {
    setShowModal(false);
    setSelectedFile(null);
  };

  const handleFileClick = async (file: FileData) => {
    if (!session?.user?.email) {
      console.log("Can't see the user");
      alert('You must be logged in to perform this action.');
      return;
    }
  
    try {
      const userEmail = session.user.email;
      const chatsRef = collection(db, 'users', userEmail, 'chats');
      
      // Determine target file ID and find/create appropriate chat
      let targetFileId: string;
      let chatId: string;
  
      if (file.category === 'Unknown') {
        // For Single Files: Find last chat or create new one for this specific file
        const lastChatQuery = query(
          chatsRef,
          where('fileDocumentId', '==', file.id),
          orderBy('createdAt', 'desc'),
          limit(1)
        );
        const lastChatSnapshot = await getDocs(lastChatQuery);
  
        if (!lastChatSnapshot.empty) {
          // Use the last chat for this file
          chatId = lastChatSnapshot.docs[0].id;
        } else {
          // Create new chat for this file
          const newChatRef = await addDoc(chatsRef, {
            userId: userEmail,
            createdAt: serverTimestamp(),
            fileDocumentId: file.id
          });
          chatId = newChatRef.id;
        }
      } else {
        // For grouped files: Find alphabetically first file in category
        const filesCollection = collection(db, 'users', userEmail, 'files');
        const categoryQuery = query(
          filesCollection,
          where('category', '==', file.category)
        );
        const categorySnapshot = await getDocs(categoryQuery);
  
        const primaryFile = categorySnapshot.docs
          .map(doc => ({
            id: doc.id,
            ...doc.data()
          } as FileData))
          .sort((a, b) => a.name.localeCompare(b.name))[0];
  
        if (!primaryFile) {
          console.error('No files found in category');
          return;
        }
  
        targetFileId = primaryFile.id;
  
        // Find or create chat for the primary file
        const chatQuery = query(
          chatsRef,
          where('fileDocumentId', '==', targetFileId),
          orderBy('createdAt', 'desc'),
          limit(1)
        );
        const chatSnapshot = await getDocs(chatQuery);
  
        if (!chatSnapshot.empty) {
          chatId = chatSnapshot.docs[0].id;
        } else {
          const newChatRef = await addDoc(chatsRef, {
            userId: userEmail,
            createdAt: serverTimestamp(),
            fileDocumentId: targetFileId
          });
          chatId = newChatRef.id;
        }
      }
  
      router.push(`/chat/${chatId}`);
    } catch (error) {
      console.error('Error handling file click:', error);
      alert('An error occurred. Please try again.');
    }
  };

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/' });
  };

  return (
    <div className="w-full max-w-7xl p-5 space-y-4 rounded-xl bg-black bg-opacity-65 text-blue-200 min-h-screen flex flex-col">
      {isProcessing && (
        <div className="flex justify-center items-center h-screen bg-transparent">
          <p className="text-sm text-red-600 font-semibold mr-10">
            {deletefiledialog}
          </p>
          <ExpandIcon className="animate-spin w-12 h-12 text-gray-500" />
        </div>
      )}

      <div className="flex justify-between items-center mb-4">
        <h1 className="text-3xl font-bold text-white text-center">File Manager</h1>
        <div className="flex items-center">
          {session?.user && (
            <div className="flex items-center ml-5">
              <span className="text-white mr-2">{session.user.name}</span>
              <button
                onClick={() => setShowSignOutModal(true)}
                className="flex items-center"
              >
                <Image
                  src={session.user.image || '/placeholder-user.jpg'}
                  alt="User profile"
                  width={40}
                  height={40}
                  className="rounded-full mr-5"
                />
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="flex items-center mb-4">
        <div className="relative w-1/4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              if (e.target.value && !showSearchResults) {
                setShowSearchResults(true);
              }
            }}
            className="w-full px-10 py-2 rounded-lg bg-ike-dark-purple/75 text-gray-200 
                      border border-gray-700 focus:border-gray-600
                      placeholder-gray-400 focus:outline-none focus:ring-1 
                      focus:ring-gray-600 shadow-sm transition-all duration-200"
          />
        </div>
      
        <button 
          onClick={() => {
            router.push('/chatexplorer')
            console.log('Search chats clicked');
          }}
          className="ml-4 px-8 py-2 bg-ike-dark-purple/75 text-amber-500 rounded-lg 
                    border border-gray-700 hover:bg-ike-message-bg 
                    transition-colors duration-200 shadow-sm
                    focus:outline-none focus:ring-1 focus:ring-gray-600
                    flex items-center justify-center"
        >
          <Search className="mr-2 h-4 w-4" />
          Search Chats
        </button>

        <FileManagerStats />
      </div>

      <div className="flex flex-row">
        {!isProcessing && (
          <FileManagerDirectory
            isLoading={isLoading}
            showSearchResults={showSearchResults}
            groupedFiles={groupedFiles}
            toggleCategory={toggleCategory}
            handleFileClick={handleFileClick}
            formatTimestamp={formatTimestamp}
            truncateFileName={truncateFileName}
            SkeletonLoader={SkeletonLoader}
          />
        )}

        <div
          className={`bg-black transition-all duration-300 ease-in-out border border-gray-700 ${
            showSearchResults ? 'w-[40%] ml-4 p-4 rounded-lg' : 'w-0 overflow-hidden'
          }`}
        >
          <div className="flex justify-between items-left mb-4">
            <h2 className="text-sm text-amber-500">Search Results</h2>
            <button
              onClick={toggleSearchResults}
              className="text-amber-500 hover:text-amber-400 transition-colors duration-200"
              aria-label="Toggle search results"
            >
              <ChevronLeft className="h-6 w-6" />
            </button>
          </div>
          {isLoading ? (
            <SkeletonLoader />
          ) : expandedCategory ? (
            <div>
              <h3 className="text-md font-semibold text-white mb-2 text-left">
                {expandedCategory === 'Unknown' ? 'Single Files' : expandedCategory} 
              </h3>
              <ul className="space-y-2">
                {files
                  .filter((file) => file.category === expandedCategory)
                  .map((file) => (
                    <li
                      key={file.id}
                      className="flex items-center text-xs text-left text-blue-200 hover:text-blue-400 transition-colors duration-200"
                    >
                      <Trash
                        className="inline mr-2 h-4 w-4 cursor-pointer text-red-600"
                        onClick={() => handleDeleteClick(file)}
                      />
                      <span
                        className="cursor-pointer"
                        onClick={() => handleFileClick(file)}
                      >
                        {truncateFileName(file.name)}
                      </span>
                    </li>
                  ))}
              </ul>
            </div>
          ) : filteredFiles.length > 0 ? (
            <ul className="space-y-2">
              {filteredFiles.map((file) => (
                <li
                  key={file.id}
                  className="text-blue-200 hover:text-blue-400 transition-colors duration-200 flex items-center"
                >
                  <Trash
                    onClick={() => handleDeleteClick(file)}
                    className="inline mr-2 h-4 w-4 cursor-pointer text-red-600"
                  />
                  <span
                    className="cursor-pointer"
                    onClick={() => handleFileClick(file)}
                  >
                    {truncateFileName(file.name)}
                  </span>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-400 italic">Search results will appear here</p>
          )}
        </div>
      </div>

      {showModal && selectedFile && !isProcessing && (
        <DeleteConfirmationModal
          selectedFile={selectedFile}
          onConfirm={handleConfirmDelete}
          onCancel={handleCancelDelete}
          truncateFileName={truncateFileName}
        />
      )}

      {showSignOutModal && (
        <SignOutModal
          onSignOut={handleSignOut}
          onCancel={() => setShowSignOutModal(false)}
        />
      )}
    </div>
  );
}