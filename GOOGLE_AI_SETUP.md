# Google AI Setup for CastMate

This document explains how to set up Google AI (Gemini) integration for the CastMate AI rehearsal application.

## Environment Variables

To use Google AI with CastMate, you need to set one of the following environment variables:

### Option 1: GOOGLE_AI_API_KEY (Recommended)
```bash
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
```

### Option 2: GEMINI_API_KEY (Alternative)
```bash
GEMINI_API_KEY=your_google_ai_api_key_here
```

## Getting a Google AI API Key

1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Sign in with your Google account
3. Click on "Get API key" in the left sidebar
4. Create a new API key or use an existing one
5. Copy the API key

## Setting Environment Variables

### For Development (.env.local)
Create or update your `.env.local` file in the project root:

```bash
# Google AI Configuration
GOOGLE_AI_API_KEY=your_api_key_here

# Other existing environment variables...
```

### For Production
Set the environment variable in your production environment:

- **Vercel**: Add the environment variable in your Vercel dashboard
- **Netlify**: Add the environment variable in your Netlify dashboard
- **Docker**: Use the `-e` flag or environment file
- **Server**: Export the variable in your shell or add to your service configuration

## Default Configuration

The CastMate application is now configured to use:

- **Default Provider**: Google AI
- **Default Model**: `gemini-2.5-pro-preview-05-06`
- **Fallback**: If Google AI is not available, the system will show appropriate error messages

## Testing the Integration

1. Set your API key in the environment variables
2. Start the CastMate application
3. Navigate to the Script tab
4. Upload or select a script
5. The script should be formatted using Google AI

## Troubleshooting

### Common Issues

1. **"Google AI API key not found"**
   - Ensure you've set either `GOOGLE_AI_API_KEY` or `GEMINI_API_KEY`
   - Restart your development server after adding the environment variable

2. **"Google AI quota exceeded"**
   - Check your Google AI Studio usage limits
   - Consider upgrading your plan if needed

3. **"Google AI model error"**
   - Verify that the model name is correct
   - Check if the model is available in your region

### Debug Mode

To enable debug logging for Google AI integration, check the browser console for detailed error messages when script formatting fails.

## Model Options

The Google AI integration supports the following models:

- `gemini-2.5-pro-preview-05-06` (Default)
- `gemini-2.0-flash-exp`
- `gemini-1.5-pro`
- `gemini-1.5-pro-002`
- `gemini-1.5-flash`
- `gemini-1.5-flash-002`
- `gemini-1.0-pro`
- `gemini-pro`
- `gemini-pro-vision`

## Configuration Options

You can customize the Google AI behavior by modifying the model options in the script formatter:

```typescript
{
  model: "gemini-2.5-pro-preview-05-06",
  provider: "google",
  modelOptions: {
    temperature: 0.2,      // Creativity level (0.0 - 1.0)
    maxTokens: 7000,       // Maximum response length
    topP: 1.0,             // Nucleus sampling
    topK: 40               // Top-k sampling
  }
}
```

## Support

If you encounter issues with the Google AI integration:

1. Check the browser console for error messages
2. Verify your API key is valid and has sufficient quota
3. Ensure your internet connection is stable
4. Try using a different model if the default one fails

For more information about Google AI models and capabilities, visit the [Google AI documentation](https://ai.google.dev/docs).
