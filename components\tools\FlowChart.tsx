import React from 'react';
import React<PERSON>low, { 
  Node, 
  Edge, 
  Controls, 
  Background, 
  MiniMap 
} from 'react-flow-renderer';

export interface FlowChartProps {
  data: {
    nodes: Node[];
    edges: Edge[];
  };
}

export const FlowChart: React.FC<FlowChartProps> = ({ data }) => {
  const { nodes, edges } = data;

  return (
    <div className="w-full h-[400px]">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        fitView
        attributionPosition="bottom-left"
      >
        <Controls />
        <Background />
        <MiniMap />
      </ReactFlow>
    </div>
  );
};

