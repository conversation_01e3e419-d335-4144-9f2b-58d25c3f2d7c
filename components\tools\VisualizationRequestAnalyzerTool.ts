import { LLMDataProcessingTool } from './LLMDataProcessingTool';

interface VisualizationPatterns {
  readonly visualizationKeywords: string[];
  readonly dataRelatedTerms: string[];
  readonly businessMetrics: string[];
  readonly exclusionTerms: string[];
  readonly informationRequestTerms: string[];
  readonly dataIndicators: string[];
}

const VISUALIZATION_PATTERNS: VisualizationPatterns = {
  visualizationKeywords: [
    'show', 'display', 'visualize', 'plot', 'graph',
    'chart', 'diagram', 'dashboard', 'trend', 'view',
    'draw', 'illustrate', 'map', 'render', 'present'
  ],
  dataRelatedTerms: [
    'data', 'numbers', 'metrics', 'statistics',
    'figures', 'results', 'performance', 'analytics',
    'measurements', 'values', 'indicators', 'ratios'
  ],
  businessMetrics: [
    'sales', 'revenue', 'growth', 'profit', 'costs',
    'conversion', 'engagement', 'retention', 'churn',
    'roi', 'margin', 'transactions', 'volume'
  ],
  exclusionTerms: [
    'explain', 'describe', 'what', 'why', 'how',
    'best practices', 'strategy', 'policy', 'guidelines',
    'provide information', 'tell me about', 'elaborate',
    'discuss', 'clarify', 'define', 'understand'
  ],
  informationRequestTerms: [
    'provide additional information',
    'tell me more',
    'explain',
    'describe',
    'elaborate on',
    'give details about',
    'provide context',
    'help me understand'
  ],
  dataIndicators: [
    '%', '$', '€', '£',
    'million', 'billion',
    'increase', 'decrease',
    'grew by', 'declined by',
    'rose', 'fell'
  ]
};

export interface VisualizationAnalysis {
  isVisualizationRequest: boolean;
  confidence: number;
  suggestion?: {
    type: string;
    reason: string;
  };
}

export class VisualizationRequestAnalyzerTool {
  static description = `VisualizationRequestAnalyzerTool analyzes user requests to:
  - Detect visualization intent in natural language
  - Extract structured data from text descriptions
  - Score confidence in visualization requests
  - Suggest appropriate visualization types
  - Parse time series and numerical data`;

  private readonly minConfidenceThreshold = 0.4; // Lowered threshold
  private readonly patterns: VisualizationPatterns;
  private readonly llmDataProcessor: LLMDataProcessingTool;

  constructor(llmDataProcessor: LLMDataProcessingTool, patterns?: VisualizationPatterns) {
    this.llmDataProcessor = llmDataProcessor;
    this.patterns = patterns || VISUALIZATION_PATTERNS;
  }

  private detectExplicitData(text: string): boolean {
    // Check for numerical patterns - include currency
    const hasNumbers = /\$?\d+([,.]\d+)?%?/.test(text);
    
    // Check for data table indicators
    const hasTableStructure = text.includes('|') || text.includes('\t');
    
    // Check for data series indicators
    const hasDataSeries = /Q[1-4]|Quarter|Year|Month/i.test(text);
    
    // Check for structured data patterns
    const hasStructuredData = /[\[\]{},|]/.test(text) || 
                            text.split('\n').some(line => line.includes(','));
    
    return hasNumbers || hasTableStructure || hasDataSeries || hasStructuredData;
  }

  private calculateConfidence(text: string): number {
    const normalizedText = text.toLowerCase();
    let score = 0;

    // Check for explicit visualization request (40%)
    const visualizationKeywords = this.patterns.visualizationKeywords
      .filter(keyword => normalizedText.includes(keyword));
    score += (visualizationKeywords.length / 3) * 0.4; // Cap at 40%

    // Check for presence of actual data (30%)
    if (this.detectExplicitData(text)) {
      score += 0.3;
    }

    // Check for data-related terms (15%)
    const dataTerms = this.patterns.dataRelatedTerms
      .filter(term => normalizedText.includes(term));
    score += (dataTerms.length / 2) * 0.15; // Cap at 15%

    // Check for business metrics (15%)
    const metrics = this.patterns.businessMetrics
      .filter(metric => normalizedText.includes(metric));
    score += (metrics.length / 2) * 0.15; // Cap at 15%

    // Boost for specific chart types
    if (normalizedText.includes('stacked') && 
        (normalizedText.includes('column') || normalizedText.includes('bar'))) {
      score += 0.2;
    }

    // Boost for tabular data
    if (text.includes('|') || /\n.*\|/.test(text)) {
      score += 0.2;
    }

    return Math.min(1, score);
  }

  async call(request: string): Promise<VisualizationAnalysis> {
    console.log('VisualizationRequestAnalyzerTool: Analyzing request:', request);
    
    try {
      // Initial confidence calculation
      const confidence = this.calculateConfidence(request);
      console.log('Initial confidence score:', confidence);
      
      // Early exit if confidence is too low
      if (confidence < this.minConfidenceThreshold) {
        console.log('Confidence below threshold, returning early');
        return {
          isVisualizationRequest: false,
          confidence
        };
      }

      // For explicit visualization requests with tabular data
      if (request.toLowerCase().includes('chart') && this.detectExplicitData(request)) {
        let suggestionType = 'bar'; // default
        
        // Determine chart type from request
        const reqLower = request.toLowerCase();
        if (reqLower.includes('stacked') && 
            (reqLower.includes('column') || reqLower.includes('bar'))) {
          suggestionType = 'stacked-column';
        }

        return {
          isVisualizationRequest: true,
          confidence: confidence,
          suggestion: {
            type: suggestionType,
            reason: `Request explicitly asks for ${suggestionType} chart with tabular data provided`
          }
        };
      }

      return {
        isVisualizationRequest: false,
        confidence: confidence
      };

    } catch (error) {
      console.error('Error during visualization analysis:', error);
      return {
        isVisualizationRequest: false,
        confidence: 0
      };
    }
  }

  getDescription(): string {
    return VisualizationRequestAnalyzerTool.description;
  }
}