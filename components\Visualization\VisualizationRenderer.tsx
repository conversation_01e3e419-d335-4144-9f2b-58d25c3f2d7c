import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Bar,
  Sc<PERSON>er,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';

interface VisualizationRendererProps {
  data: {
    type: 'visualization';
    content: {
      type: 'chart';
      subtype: string;
      config: {
        type: string;
        xAxis?: string;
        yAxis?: string;
        series?: string[];
        colors?: string[];
        stacked?: boolean;
      };
      data: any[];
      metadata: {
        type: string;
        metrics: string[];
        timeUnit?: string | null;
      };
    };
  };
}

const VisualizationRenderer: React.FC<VisualizationRendererProps> = ({ data }) => {
  if (!data?.content?.config?.type) {
    return <div className="text-red-500">Invalid chart configuration</div>;
  }

  const { config, data: chartData, metadata } = data.content;
  const { type, series = [], colors = [], stacked = false } = config;

  // Format currency values
  const formatCurrency = (value: number): string => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`;
    }
    return `$${value.toFixed(0)}`;
  };

  // Format time period values
  const formatTimePeriod = (value: any): string => {
    if (!value) return '';
    if (typeof value === 'string') return value;
    const year = value.year || '';
    const quarter = value.quarter || '';
    return `${year} ${quarter}`.trim();
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload) return null;
    
    return (
      <div className="bg-white p-2 rounded-lg shadow-lg border border-gray-200">
        <p className="text-gray-700 text-xs font-semibold mb-1">{formatTimePeriod(label)}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center gap-2 text-xs">
            <div
              className="w-2 h-2 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span style={{ color: '#666' }}>
              {entry.name}: {formatCurrency(entry.value)}
            </span>
          </div>
        ))}
      </div>
    );
  };

  // Transform data to ensure proper time formatting
  const transformedData = chartData.map(item => ({
    ...item,
    time: formatTimePeriod(item.time)
  }));

  // Common chart props
  const commonProps = {
    data: transformedData,
    margin: { top: 5, right: 20, left: 10, bottom: 5 },
    style: { backgroundColor: 'white' }
  };

  // Common axis props
  const commonXAxisProps = {
    dataKey: "time",
    height: 40,
    tick: { fontSize: 10, fill: '#666' },
    label: {
      value: 'Time Period',
      position: 'insideBottom',
      offset: -10,
      style: { fontSize: 12, fill: '#666' }
    }
  };

  const commonYAxisProps = {
    tickFormatter: formatCurrency,
    width: 60,
    tick: { fontSize: 10, fill: '#666' },
    label: {
      value: 'Sales ($)',
      angle: -90,
      position: 'insideLeft',
      offset: -5,
      style: { fontSize: 12, fill: '#666' }
    }
  };

  // Generate a color for a series if none provided
  const getSeriesColor = (index: number): string => {
    return colors[index] || `hsl(${(index * 137) % 360}, 70%, 50%)`;
  };

  // Render appropriate chart based on type
  const renderChart = () => {
    switch (type.toLowerCase()) {
      case 'bar':
        return (
          <BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
            <XAxis {...commonXAxisProps} />
            <YAxis {...commonYAxisProps} />
            <Tooltip content={<CustomTooltip />} />
            <Legend 
              wrapperStyle={{ fontSize: '10px', paddingTop: '10px' }}
              iconSize={8}
              align="center"
              verticalAlign="bottom"
            />
            {series.map((metric, index) => (
              <Bar
                key={metric}
                dataKey={metric}
                name={metric}
                stackId={stacked ? "stack" : undefined}
                fill={getSeriesColor(index)}
              />
            ))}
          </BarChart>
        );

      case 'line':
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
            <XAxis {...commonXAxisProps} />
            <YAxis {...commonYAxisProps} />
            <Tooltip content={<CustomTooltip />} />
            <Legend 
              wrapperStyle={{ fontSize: '10px', paddingTop: '10px' }}
              iconSize={8}
              align="center"
              verticalAlign="bottom"
            />
            {series.map((metric, index) => (
              <Line
                key={metric}
                type="monotone"
                dataKey={metric}
                name={metric}
                stroke={getSeriesColor(index)}
                strokeWidth={2}
                dot={{ r: 4, fill: 'white', strokeWidth: 2 }}
                activeDot={{ r: 6 }}
              />
            ))}
          </LineChart>
        );

      case 'scatter':
        return (
          <ScatterChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
            <XAxis {...commonXAxisProps} type="number" />
            <YAxis {...commonYAxisProps} type="number" />
            <Tooltip content={<CustomTooltip />} />
            <Legend 
              wrapperStyle={{ fontSize: '10px', paddingTop: '10px' }}
              iconSize={8}
              align="center"
              verticalAlign="bottom"
            />
            {series.map((metric, index) => (
              <Scatter
                key={metric}
                name={metric}
                data={transformedData}
                fill={getSeriesColor(index)}
              />
            ))}
          </ScatterChart>
        );

      case 'pie':
        return (
          <PieChart {...commonProps}>
            <Pie
              data={transformedData}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              outerRadius={80}
              label={({ name, value }) => `${name}: ${formatCurrency(value)}`}
              labelLine={true}
            >
              {transformedData.map((_: any, index: number) => (
                <Cell
                  key={`cell-${index}`}
                  fill={getSeriesColor(index)}
                />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend 
              wrapperStyle={{ fontSize: '10px', paddingTop: '10px' }}
              iconSize={8}
              align="center"
              verticalAlign="bottom"
            />
          </PieChart>
        );

      default:
        return (
          <div className="text-red-500 flex items-center justify-center h-full">
            Unsupported chart type: {type}
          </div>
        );
    }
  };

  return (
    <ResponsiveContainer width="100%" height="100%">
      {renderChart()}
    </ResponsiveContainer>
  );
};

export default VisualizationRenderer;