// lib/checkDuplicates.ts

import { collection, query, where, getDocs } from "firebase/firestore";
import { db } from "components/firebase";

/**
 * Checks for duplicate Recipes based on the recipe name.
 * @param recipeName - The name of the recipe to check.
 * @returns A boolean indicating whether a duplicate exists.
 */
export async function isDuplicateRecipe(userId: string, recipeName: string): Promise<boolean> {
  const recipesRef = collection(db, "users", userId, "Recipes");
  const q = query(recipesRef, where("name", "==", recipeName));
  
  try {
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  } catch (error) {
    console.error("Error checking duplicate Recipes:", error);
    // Decide whether to treat errors as duplicates to prevent saving or allow saving.
    return true; // Prevent saving in case of error
  }
}

/**
 * Checks for duplicate Clients based on the client name.
 * @param clientName - The name of the client to check.
 * @returns A boolean indicating whether a duplicate exists.
 */
export async function isDuplicateClient(userId: string, clientName: string): Promise<boolean> {
  const clientsRef = collection(db, "users", userId, "Clients");
  const q = query(clientsRef, where("clientName", "==", clientName));
  
  try {
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  } catch (error) {
    console.error("Error checking duplicate Clients:", error);
    // Decide whether to treat errors as duplicates to prevent saving or allow saving.
    return true; // Prevent saving in case of error
  }
}
