/**
 * streaming.ts
 * 
 * This module handles the streaming of data chunks to clients using the Web Streams API.
 * It provides utilities for controlled, buffered streaming with error handling and
 * encoding support. The module is designed to work with ReadableStream controllers
 * for efficient data transmission.
 * 
 * Key features:
 * - Text encoding for stream transmission
 * - Optional buffering for rate control
 * - Promise-based streaming
 * - Error handling
 */

/**
 * Configuration options for stream control
 * Used to pass the ReadableStream controller to streaming functions
 */
interface StreamOptions {
  controller: ReadableStreamDefaultController;
}

/**
 * Streams a chunk of data to the client with optional buffering
 * 
 * This function handles the encoding and transmission of data chunks through
 * a ReadableStream. It supports both immediate streaming and buffered streaming
 * with a delay for rate control.
 * 
 * @param {StreamOptions} options - Object containing the stream controller
 * @param {string} chunk - The data chunk to be streamed
 * @param {boolean} shouldBuffer - Whether to buffer the transmission with a delay (default: false)
 * @returns {Promise<void>} Resolves when the chunk has been enqueued
 * 
 * @example
 * // Immediate streaming
 * await streamToClient({ controller }, "Data chunk");
 * 
 * @example
 * // Buffered streaming
 * await streamToClient({ controller }, "Data chunk", true);
 * 
 * @throws {Error} If there's an error during encoding or streaming
 */
export async function streamToClient(
  { controller }: StreamOptions,
  chunk: string,
  shouldBuffer: boolean = false
): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      // Create text encoder for converting string to Uint8Array
      const encoder = new TextEncoder();
      
      // Encode the chunk for streaming
      const encoded = encoder.encode(chunk);

      if (shouldBuffer) {
        // Buffer the transmission with a 50ms delay
        // Useful for controlling stream rate or allowing UI updates
        setTimeout(() => {
          controller.enqueue(encoded);
          resolve();
        }, 50);
      } else {
        // Stream immediately without buffering
        controller.enqueue(encoded);
        resolve();
      }
    } catch (error) {
      // Log and propagate any streaming errors
      console.error("Streaming error:", error);
      reject(error);
    }
  });
}

/**
 * Note on usage:
 * 
 * The streamToClient function is typically used in scenarios where:
 * 1. Streaming large amounts of data incrementally
 * 2. Real-time data transmission is required
 * 3. Rate control is needed to prevent overwhelming the client
 * 4. Processing time is needed between chunks
 * 
 * The buffering option (shouldBuffer) should be used when:
 * - The client needs time to process each chunk
 * - UI updates need to be smooth
 * - Rate limiting is required
 * - Network conditions require controlled transmission
 */