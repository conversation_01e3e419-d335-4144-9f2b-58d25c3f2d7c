// app/api/initializeImageGeneration/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { GenerateImageAgent } from 'components/Agents/GenerateImageAgent';
import { GenerateImageTool } from 'components/tools/generateImageTool';

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // (A) Parse the incoming JSON body
    const body = await req.json();
    const { prompt, userId } = body;

    // (B) Validate user input
    if (!prompt || typeof prompt !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Invalid prompt provided' },
        { status: 400 }
      );
    }
    if (!userId || typeof userId !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Invalid userId provided' },
        { status: 400 }
      );
    }

    // (C) Validate OpenAI API key
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      console.error('OpenAI API key not configured');
      return NextResponse.json(
        { success: false, error: 'Service configuration error' },
        { status: 500 }
      );
    }

    // (D) Instantiate the tool & agent
    const imageTool = new GenerateImageTool(apiKey);
    const imageAgent = new GenerateImageAgent({ generateImageTool: imageTool });

    // (E) Create a new image generation job in Firestore
    const jobId = await imageAgent.initializeJob(prompt, userId);
    
    // Refine the prompt - now passing both jobId and userId
    await imageAgent.refinePrompt(jobId, userId);

    // (F) Respond with success
    return NextResponse.json({
      success: true,
      jobId,
      message: 'Image generation job initialized successfully'
    });

  } catch (error) {
    console.error('[Route] Image generation initialization failed:', error);

    // (G) Return a 500 error response
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to initialize image generation'
      },
      { status: 500 }
    );
  }
}