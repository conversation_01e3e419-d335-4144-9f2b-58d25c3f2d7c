// lib/schemas/Recipes_filter_schema.ts
import { z } from 'zod';

/**
 * Zod schema defining the structure and types of recipe filters.
 * Excludes fields like ingredients, measurements, and instructions.
 */
export const Recipes_filter_schema = z.object({
  servingSize: z.number().optional(),
  proteins: z.number().optional(),
  fats: z.number().optional(),
  carbohydrates: z.number().optional(),
  calories: z.number().optional(),
  // Add other fields relevant for filtering if needed
}).strict();


